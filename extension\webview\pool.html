<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aug激活管理</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            padding: 20px;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
        }

        .container {
            max-width: 500px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            color: var(--vscode-editor-foreground);
            margin-bottom: 20px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
        }

        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 5px;
        }

        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .logo {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }

        .user-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            border-radius: 4px;
            text-align: left;
        }

        .user-info p {
            margin: 5px 0;
        }

        .copy-button {
            background-color: transparent;
            color: var(--vscode-button-foreground);
            border: 1px solid var(--vscode-button-background);
            padding: 2px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }

        .copy-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .id-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .user-section {
            display: none;
        }

        .login-section {
            margin-bottom: 20px;
        }

        .vip-status {
            margin-top: 5px;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }

        .vip-expired {
            color: var(--vscode-errorForeground);
        }

        .vip-active {
            color: var(--vscode-charts-green);
        }

        .version {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin-bottom: 15px;
            padding: 4px 8px;
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            border-radius: 12px;
            display: inline-block;
            font-weight: 500;
            letter-spacing: 0.5px;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .version:hover {
            opacity: 1;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Aug激活管理</h1>
        <div class="version">1.0.2</div>

        <!-- 登录区域 -->
        <div id="loginSection" class="login-section">
            <div class="input-group">
                <input type="text" id="authCode" placeholder="请输入授权码">
            </div>
            <button id="loginButton">登录</button>
        </div>


        <!-- 用户信息区域 -->
        <div id="userSection" class="user-section">
            <div class="user-info">
                <div class="id-container">
                    <div>
                        <strong>用户ID:</strong>
                        <span id="userId"></span>
                        <div id="vipStatus" class="vip-status">
                            <span id="vipExpireText"></span>
                        </div>
                    </div>
                    <div>
                        <button id="copyIdButton" class="copy-button">复制</button>
                        <button id="logoutButton" class="copy-button">退出</button>
                    </div>
                </div>


            </div>


        </div>
    </div>

    <script>
        // 获取 VS Code API
        const vscode = acquireVsCodeApi();





        // 登录按钮点击事件
        document.getElementById('loginButton').addEventListener('click', () => {
            const authCode = document.getElementById('authCode').value.trim();
            if (authCode) {
                // 发送消息到扩展
                vscode.postMessage({
                    command: 'login',
                    code: authCode
                });
            } else {
                // 显示错误提示
                vscode.postMessage({
                    command: 'error',
                    message: '请输入授权码'
                });
            }
        });

        // 退出登录按钮点击事件
        document.getElementById('logoutButton').addEventListener('click', () => {
            vscode.postMessage({
                command: 'logout'
            });
        });





        // 监听来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;

            // 处理登录状态更新消息
            if (message.command === 'updateLoginStatus') {
                updateUIBasedOnLoginStatus(!!message.user, message.user, message.vipInfo);
            }
        });



        // 更新VIP状态显示
        function updateVipStatus(vipInfo) {
            const vipStatusSection = document.getElementById('vipStatus');
            const vipExpireText = document.getElementById('vipExpireText');

            if (vipInfo && vipInfo.expire_at) {
                const expireDate = new Date(vipInfo.expire_at);
                const now = new Date();
                const isExpired = expireDate < now;

                // 格式化过期时间
                const expireTimeStr = expireDate.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });

                if (isExpired) {
                    vipExpireText.textContent = `会员已过期 (${expireTimeStr})`;
                    vipExpireText.className = 'vip-expired';
                } else {
                    vipExpireText.textContent = `会员过期时间: ${expireTimeStr}`;
                    vipExpireText.className = 'vip-active';
                }
            } else {
                vipExpireText.textContent = `会员已过期`;
                vipExpireText.className = 'vip-expired';
            }
        }

        // 根据登录状态更新UI
        function updateUIBasedOnLoginStatus(isLoggedIn, user, vipInfo) {
            const loginSection = document.getElementById('loginSection');
            const userSection = document.getElementById('userSection');

            // 始终显示登录区域，方便切换激活码
            loginSection.style.display = 'block';

            if (isLoggedIn && user) {
                // 用户已登录，显示用户信息
                userSection.style.display = 'block';

                // 更新用户信息
                document.getElementById('userId').textContent = user.id;

                // 更新VIP状态信息
                updateVipStatus(vipInfo);
            } else {
                // 用户未登录，隐藏用户信息区域
                userSection.style.display = 'none';
            }
        }

        // 初始化时默认显示登录界面，隐藏用户信息区域
        document.getElementById('loginSection').style.display = 'block';
        document.getElementById('userSection').style.display = 'none';

        // 复制用户ID按钮点击事件
        document.getElementById('copyIdButton').addEventListener('click', () => {
            const userId = document.getElementById('userId').textContent;
            if (userId) {
                // 复制到剪贴板
                navigator.clipboard.writeText(userId)
                    .then(() => {
                        // 复制成功，更改按钮文本为“已复制”
                        const copyButton = document.getElementById('copyIdButton');
                        const originalText = copyButton.textContent;
                        copyButton.textContent = '已复制';

                        // 1.5秒后恢复按钮文本
                        setTimeout(() => {
                            copyButton.textContent = originalText;
                        }, 1500);
                    })
                    .catch(err => {
                        console.error('复制失败:', err);
                        vscode.postMessage({
                            command: 'error',
                            message: '复制用户ID失败'
                        });
                    });
            }
        });
    </script>
</body>

</html>
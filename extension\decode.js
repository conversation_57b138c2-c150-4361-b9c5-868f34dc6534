var a0_0x151492 = function () {
  var _0xc23823 = true;
  return function (_0x1b03be, _0x37c892) {
    var _0x53bc18 = _0xc23823 ? function () {
      if (_0x37c892) {
        var _0x39e8c6 = _0x37c892.apply(_0x1b03be, arguments);
        _0x37c892 = null;
        return _0x39e8c6;
      }
    } : function () {};
    _0xc23823 = false;
    return _0x53bc18;
  };
}();
var a0_0x1d8703 = a0_0x151492(this, function () {
  var _0x4b4021 = function () {
    var _0x358681;
    try {
      _0x358681 = Function("return (function() {}.constructor(\"return this\")( ));")();
    } catch (_0x21f777) {
      _0x358681 = window;
    }
    return _0x358681;
  };
  var _0x529644 = _0x4b4021();
  var _0x5d9012 = _0x529644.console = _0x529644.console || {};
  var _0x1f9c00 = ["log", "warn", "info", "error", "exception", "table", "trace"];
  for (var _0x498f04 = 0x0; _0x498f04 < _0x1f9c00.length; _0x498f04++) {
    var _0x50bc4b = a0_0x151492.constructor.prototype.bind(a0_0x151492);
    var _0x112d5c = _0x1f9c00[_0x498f04];
    var _0xa13e66 = _0x5d9012[_0x112d5c] || _0x50bc4b;
    _0x50bc4b.__proto__ = a0_0x151492.bind(a0_0x151492);
    _0x50bc4b.toString = _0xa13e66.toString.bind(_0xa13e66);
    _0x5d9012[_0x112d5c] = _0x50bc4b;
  }
});
a0_0x1d8703();
(() => {
  var _0x2acbc8 = {
    0x3f: function (_0x4966a7, _0x4b7dda, _0x1bdf25) {
      'use strict';

      var _0x18ef20;
      var _0x54fd84 = this && this.__createBinding || (Object.create ? function (_0x4f431b, _0x2d1dcc, _0x201ddd, _0x5db9ae) {
        if (undefined === _0x5db9ae) {
          _0x5db9ae = _0x201ddd;
        }
        var _0x45b4a3 = Object.getOwnPropertyDescriptor(_0x2d1dcc, _0x201ddd);
        if (!(_0x45b4a3 && !("get" in _0x45b4a3 ? !_0x2d1dcc.__esModule : _0x45b4a3.writable || _0x45b4a3.configurable))) {
          _0x45b4a3 = {
            'enumerable': true,
            'get': function () {
              return _0x2d1dcc[_0x201ddd];
            }
          };
        }
        Object.defineProperty(_0x4f431b, _0x5db9ae, _0x45b4a3);
      } : function (_0x1a4fe6, _0x2eeb5f, _0x308f84, _0x84ec16) {
        if (undefined === _0x84ec16) {
          _0x84ec16 = _0x308f84;
        }
        _0x1a4fe6[_0x84ec16] = _0x2eeb5f[_0x308f84];
      });
      var _0x155454 = this && this.__setModuleDefault || (Object.create ? function (_0x1cab5f, _0x227b52) {
        Object.defineProperty(_0x1cab5f, 'default', {
          'enumerable': true,
          'value': _0x227b52
        });
      } : function (_0x5e1ebe, _0x3f08c5) {
        _0x5e1ebe["default"] = _0x3f08c5;
      });
      var _0x1611e2 = this && this.__importStar || (_0x18ef20 = function (_0xf03da6) {
        _0x18ef20 = Object.getOwnPropertyNames || function (_0xdc7436) {
          var _0x39f01f = [];
          for (var _0x2bb8b8 in _0xdc7436) if (Object.prototype.hasOwnProperty.call(_0xdc7436, _0x2bb8b8)) {
            _0x39f01f[_0x39f01f.length] = _0x2bb8b8;
          }
          return _0x39f01f;
        };
        return _0x18ef20(_0xf03da6);
      }, function (_0x1b6eb7) {
        if (_0x1b6eb7 && _0x1b6eb7.__esModule) {
          return _0x1b6eb7;
        }
        var _0x5d2006 = {};
        if (null != _0x1b6eb7) {
          var _0x10d868 = _0x18ef20(_0x1b6eb7);
          for (var _0x441e09 = 0x0; _0x441e09 < _0x10d868.length; _0x441e09++) {
            if ("default" !== _0x10d868[_0x441e09]) {
              _0x54fd84(_0x5d2006, _0x1b6eb7, _0x10d868[_0x441e09]);
            }
          }
        }
        _0x155454(_0x5d2006, _0x1b6eb7);
        return _0x5d2006;
      });
      Object.defineProperty(_0x4b7dda, "__esModule", {
        'value': true
      });
      _0x4b7dda.globalStatus = _0x4b7dda.shareLocal = undefined;
      _0x4b7dda.getExtentionDir = _0x135c9f;
      _0x4b7dda.getAugmentFilepath = async function () {
        let _0x26fc67 = await _0x135c9f();
        _0x4d88bb.logger.debug("Extension directory: " + _0x26fc67);
        let _0x2fbebf = await _0x3d5998.readdir(_0x26fc67);
        _0x2fbebf = _0x2fbebf.filter(_0x50efbc => _0x50efbc.startsWith('augment.vscode-augment-'));
        if (!_0x2fbebf.length) {
          return void _0x4d88bb.logger.warn("No augment extension found");
        }
        _0x2fbebf.sort((_0x15e282, _0x3283fd) => _0x45b8c8(_0x3283fd) - _0x45b8c8(_0x15e282));
        _0x4d88bb.logger.debug("Found augment extensions: " + _0x2fbebf.join(", "));
        let _0x2259ee = _0x15e887.join(_0x26fc67, _0x2fbebf[0x0], "out/extension.js");
        if (await _0x3d5998.pathExists(_0x2259ee)) {
          return _0x2259ee;
        }
        _0x4d88bb.logger.warn("Augment extension file not found at path: " + _0x2259ee);
      };
      const _0x4a8698 = _0x1611e2(_0x1bdf25(0x576));
      const _0x15e887 = _0x1611e2(_0x1bdf25(0x1b10));
      const _0x3d5998 = _0x1611e2(_0x1bdf25(0x645));
      const _0x1b531a = _0x1611e2(_0x1bdf25(0x359));
      const _0x4d88bb = _0x1bdf25(0x1140);
      function _0x135c9f() {
        const _0x4b94d0 = _0x15e887.dirname(_0x4b7dda.globalStatus.context.extensionPath);
        _0x4d88bb.logger.info('extensionPath', _0x4b94d0);
        if (_0x4b94d0.endsWith("extensions")) {
          return _0x4b94d0;
        }
        let _0x5e0272 = ".vscode";
        switch (_0x4a8698.env.appName) {
          case "Visual Studio Code":
            _0x5e0272 = '.vscode';
            break;
          case "Windsurf":
            _0x5e0272 = '.windsurf';
            break;
          case "Cursor":
            _0x5e0272 = '.cursor';
        }
        return _0x15e887.join(_0x1b531a.homedir(), _0x5e0272, "extensions");
      }
      function _0x45b8c8(_0x54da0d) {
        let _0x24ea12 = /\d+(\.\d+)+/.exec(_0x54da0d);
        if (!_0x24ea12) {
          return 0x0;
        }
        let _0x4e4094 = _0x24ea12[0x0].split('.').map(_0x46ebb1 => +_0x46ebb1);
        return 0xf4240 * _0x4e4094[0x0] + 0x3e8 * _0x4e4094[0x1] + _0x4e4094[0x2];
      }
      _0x4b7dda.shareLocal = {
        'user': null
      };
      _0x4b7dda.globalStatus = {
        'windsurf': false,
        'augment': false,
        'context': null
      };
    },
    0xae: (_0x1d386c, _0x439f30, _0x150672) => {
      'use strict';

      const _0x2cfa94 = _0x150672(0xe97);
      const _0x2e2858 = _0x150672(0x1b10);
      const _0x3f707f = _0x150672(0xed6).mkdirsSync;
      const _0x350493 = _0x150672(0x1c2b).utimesMillisSync;
      const _0x4ae4ac = _0x150672(0x193e);
      function _0x57e256(_0x597416, _0x138364, _0x5d2fb1, _0x537b67) {
        if (!_0x537b67.filter || _0x537b67.filter(_0x138364, _0x5d2fb1)) {
          return function (_0x561642, _0x2fe00a, _0x14c501, _0x25343f) {
            const _0x3a5011 = (_0x25343f.dereference ? _0x2cfa94.statSync : _0x2cfa94.lstatSync)(_0x2fe00a);
            return _0x3a5011.isDirectory() ? function (_0x5f31ac, _0x1a0cc6, _0x383f9a, _0x5d2d36, _0x5d065e) {
              if (!_0x1a0cc6) {
                return function (_0xe9f615, _0x1b0b43, _0xeb4f71, _0x1b0908) {
                  _0x2cfa94.mkdirSync(_0xeb4f71);
                  _0x9cfe22(_0x1b0b43, _0xeb4f71, _0x1b0908);
                  return _0x2cfa94.chmodSync(_0xeb4f71, _0xe9f615.mode);
                }(_0x5f31ac, _0x383f9a, _0x5d2d36, _0x5d065e);
              }
              if (_0x1a0cc6 && !_0x1a0cc6.isDirectory()) {
                throw new Error("Cannot overwrite non-directory '" + _0x5d2d36 + "' with directory '" + _0x383f9a + "'.");
              }
              return _0x9cfe22(_0x383f9a, _0x5d2d36, _0x5d065e);
            }(_0x3a5011, _0x561642, _0x2fe00a, _0x14c501, _0x25343f) : _0x3a5011.isFile() || _0x3a5011.isCharacterDevice() || _0x3a5011.isBlockDevice() ? function (_0x1e9c51, _0x1e9086, _0x5408ca, _0x466c49, _0x3a3a03) {
              return _0x1e9086 ? function (_0x381ce3, _0x5cea12, _0x480f8f, _0xf0d2f8) {
                if (_0xf0d2f8.overwrite) {
                  _0x2cfa94.unlinkSync(_0x480f8f);
                  return _0x3db524(_0x381ce3, _0x5cea12, _0x480f8f, _0xf0d2f8);
                }
                if (_0xf0d2f8.errorOnExist) {
                  throw new Error("'" + _0x480f8f + "' already exists");
                }
              }(_0x1e9c51, _0x5408ca, _0x466c49, _0x3a3a03) : _0x3db524(_0x1e9c51, _0x5408ca, _0x466c49, _0x3a3a03);
            }(_0x3a5011, _0x561642, _0x2fe00a, _0x14c501, _0x25343f) : _0x3a5011.isSymbolicLink() ? function (_0xde6c54, _0x264ded, _0x497241, _0x34d2fa) {
              let _0x3a19a6 = _0x2cfa94.readlinkSync(_0x264ded);
              if (_0x34d2fa.dereference) {
                _0x3a19a6 = _0x2e2858.resolve(process.cwd(), _0x3a19a6);
              }
              if (_0xde6c54) {
                let _0x64a8c9;
                try {
                  _0x64a8c9 = _0x2cfa94.readlinkSync(_0x497241);
                } catch (_0x36c054) {
                  if ("EINVAL" === _0x36c054.code || "UNKNOWN" === _0x36c054.code) {
                    return _0x2cfa94.symlinkSync(_0x3a19a6, _0x497241);
                  }
                  throw _0x36c054;
                }
                if (_0x34d2fa.dereference) {
                  _0x64a8c9 = _0x2e2858.resolve(process.cwd(), _0x64a8c9);
                }
                if (_0x4ae4ac.isSrcSubdir(_0x3a19a6, _0x64a8c9)) {
                  throw new Error("Cannot copy '" + _0x3a19a6 + "' to a subdirectory of itself, '" + _0x64a8c9 + "'.");
                }
                if (_0x2cfa94.statSync(_0x497241).isDirectory() && _0x4ae4ac.isSrcSubdir(_0x64a8c9, _0x3a19a6)) {
                  throw new Error("Cannot overwrite '" + _0x64a8c9 + "' with '" + _0x3a19a6 + "'.");
                }
                return function (_0x48b96b, _0x2da3b3) {
                  _0x2cfa94.unlinkSync(_0x2da3b3);
                  return _0x2cfa94.symlinkSync(_0x48b96b, _0x2da3b3);
                }(_0x3a19a6, _0x497241);
              }
              return _0x2cfa94.symlinkSync(_0x3a19a6, _0x497241);
            }(_0x561642, _0x2fe00a, _0x14c501, _0x25343f) : undefined;
          }(_0x597416, _0x138364, _0x5d2fb1, _0x537b67);
        }
      }
      function _0x3db524(_0x2c7ed9, _0x7b877c, _0x40319f, _0x59fec5) {
        return "function" == typeof _0x2cfa94.copyFileSync ? (_0x2cfa94.copyFileSync(_0x7b877c, _0x40319f), _0x2cfa94.chmodSync(_0x40319f, _0x2c7ed9.mode), _0x59fec5.preserveTimestamps ? _0x350493(_0x40319f, _0x2c7ed9.atime, _0x2c7ed9.mtime) : undefined) : function (_0x4e028d, _0x21182c, _0x49d23f, _0x4f94be) {
          const _0x2c69f6 = _0x150672(0x41e)(0x10000);
          const _0x42108e = _0x2cfa94.openSync(_0x21182c, 'r');
          const _0x14fb6e = _0x2cfa94.openSync(_0x49d23f, 'w', _0x4e028d.mode);
          let _0x26b47a = 0x0;
          for (; _0x26b47a < _0x4e028d.size;) {
            const _0x533e33 = _0x2cfa94.readSync(_0x42108e, _0x2c69f6, 0x0, 0x10000, _0x26b47a);
            _0x2cfa94.writeSync(_0x14fb6e, _0x2c69f6, 0x0, _0x533e33);
            _0x26b47a += _0x533e33;
          }
          if (_0x4f94be.preserveTimestamps) {
            _0x2cfa94.futimesSync(_0x14fb6e, _0x4e028d.atime, _0x4e028d.mtime);
          }
          _0x2cfa94.closeSync(_0x42108e);
          _0x2cfa94.closeSync(_0x14fb6e);
        }(_0x2c7ed9, _0x7b877c, _0x40319f, _0x59fec5);
      }
      function _0x9cfe22(_0x3cd655, _0x41877c, _0x67ee5a) {
        _0x2cfa94.readdirSync(_0x3cd655).forEach(_0x4c44b9 => function (_0xea26e3, _0x534104, _0x34dea0, _0x2ba533) {
          const _0x3b93ad = _0x2e2858.join(_0x534104, _0xea26e3);
          const _0x1f5198 = _0x2e2858.join(_0x34dea0, _0xea26e3);
          const {
            destStat: _0x147945
          } = _0x4ae4ac.checkPathsSync(_0x3b93ad, _0x1f5198, "copy");
          return _0x57e256(_0x147945, _0x3b93ad, _0x1f5198, _0x2ba533);
        }(_0x4c44b9, _0x3cd655, _0x41877c, _0x67ee5a));
      }
      _0x1d386c.exports = function (_0x10c0d5, _0x2e3e4c, _0x533244) {
        if ('function' == typeof _0x533244) {
          _0x533244 = {
            'filter': _0x533244
          };
        }
        (_0x533244 = _0x533244 || {}).clobber = !("clobber" in _0x533244) || !!_0x533244.clobber;
        _0x533244.overwrite = "overwrite" in _0x533244 ? !!_0x533244.overwrite : _0x533244.clobber;
        if (_0x533244.preserveTimestamps && "ia32" === process.arch) {
          console.warn("fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269");
        }
        const {
          srcStat: _0x1c17f8,
          destStat: _0x2d3e9c
        } = _0x4ae4ac.checkPathsSync(_0x10c0d5, _0x2e3e4c, "copy");
        _0x4ae4ac.checkParentPathsSync(_0x10c0d5, _0x1c17f8, _0x2e3e4c, "copy");
        return function (_0x2813a3, _0x44707e, _0x130a06, _0x110297) {
          if (_0x110297.filter && !_0x110297.filter(_0x44707e, _0x130a06)) {
            return;
          }
          const _0x31a091 = _0x2e2858.dirname(_0x130a06);
          if (!_0x2cfa94.existsSync(_0x31a091)) {
            _0x3f707f(_0x31a091);
          }
          return _0x57e256(_0x2813a3, _0x44707e, _0x130a06, _0x110297);
        }(_0x2d3e9c, _0x10c0d5, _0x2e3e4c, _0x533244);
      };
    },
    0xb5: _0x30228b => {
      'use strict';

      _0x30228b.exports = require("buffer");
    },
    0x2ca: _0x2f2a68 => {
      function _0x5095e2(_0x1b5572, _0x5339a3) {
        if (!(this instanceof _0x5095e2)) {
          return new _0x5095e2(_0x1b5572, _0x5339a3);
        }
        this._bsontype = "Timestamp";
        this.low_ = 0x0 | _0x1b5572;
        this.high_ = 0x0 | _0x5339a3;
      }
      _0x5095e2.prototype.toInt = function () {
        return this.low_;
      };
      _0x5095e2.prototype.toNumber = function () {
        return this.high_ * _0x5095e2.TWO_PWR_32_DBL_ + this.getLowBitsUnsigned();
      };
      _0x5095e2.prototype.toJSON = function () {
        return this.toString();
      };
      _0x5095e2.prototype.toString = function (_0x2d90af) {
        var _0x2e71d2 = _0x2d90af || 0xa;
        if (_0x2e71d2 < 0x2 || 0x24 < _0x2e71d2) {
          throw Error("radix out of range: " + _0x2e71d2);
        }
        if (this.isZero()) {
          return '0';
        }
        if (this.isNegative()) {
          if (this.equals(_0x5095e2.MIN_VALUE)) {
            var _0x36fbe5 = _0x5095e2.fromNumber(_0x2e71d2);
            var _0x156585 = this.div(_0x36fbe5);
            var _0x130215 = _0x156585.multiply(_0x36fbe5).subtract(this);
            return _0x156585.toString(_0x2e71d2) + _0x130215.toInt().toString(_0x2e71d2);
          }
          return '-' + this.negate().toString(_0x2e71d2);
        }
        var _0x3dff41 = _0x5095e2.fromNumber(Math.pow(_0x2e71d2, 0x6));
        _0x130215 = this;
        for (var _0x2c42e0 = ''; !_0x130215.isZero();) {
          var _0x46aa14 = _0x130215.div(_0x3dff41);
          var _0x2cf54d = _0x130215.subtract(_0x46aa14.multiply(_0x3dff41)).toInt().toString(_0x2e71d2);
          if ((_0x130215 = _0x46aa14).isZero()) {
            return _0x2cf54d + _0x2c42e0;
          }
          for (; _0x2cf54d.length < 0x6;) {
            _0x2cf54d = '0' + _0x2cf54d;
          }
          _0x2c42e0 = '' + _0x2cf54d + _0x2c42e0;
        }
      };
      _0x5095e2.prototype.getHighBits = function () {
        return this.high_;
      };
      _0x5095e2.prototype.getLowBits = function () {
        return this.low_;
      };
      _0x5095e2.prototype.getLowBitsUnsigned = function () {
        return this.low_ >= 0x0 ? this.low_ : _0x5095e2.TWO_PWR_32_DBL_ + this.low_;
      };
      _0x5095e2.prototype.getNumBitsAbs = function () {
        if (this.isNegative()) {
          return this.equals(_0x5095e2.MIN_VALUE) ? 0x40 : this.negate().getNumBitsAbs();
        }
        var _0x10693c = 0x0 !== this.high_ ? this.high_ : this.low_;
        for (var _0xd490e1 = 0x1f; _0xd490e1 > 0x0 && !(_0x10693c & 0x1 << _0xd490e1); _0xd490e1--) {
          ;
        }
        return 0x0 !== this.high_ ? _0xd490e1 + 0x21 : _0xd490e1 + 0x1;
      };
      _0x5095e2.prototype.isZero = function () {
        return 0x0 === this.high_ && 0x0 === this.low_;
      };
      _0x5095e2.prototype.isNegative = function () {
        return this.high_ < 0x0;
      };
      _0x5095e2.prototype.isOdd = function () {
        return !(0x1 & ~this.low_);
      };
      _0x5095e2.prototype.equals = function (_0xaf6dd4) {
        return this.high_ === _0xaf6dd4.high_ && this.low_ === _0xaf6dd4.low_;
      };
      _0x5095e2.prototype.notEquals = function (_0x29af1c) {
        return this.high_ !== _0x29af1c.high_ || this.low_ !== _0x29af1c.low_;
      };
      _0x5095e2.prototype.lessThan = function (_0x160dd5) {
        return this.compare(_0x160dd5) < 0x0;
      };
      _0x5095e2.prototype.lessThanOrEqual = function (_0x8b1437) {
        return this.compare(_0x8b1437) <= 0x0;
      };
      _0x5095e2.prototype.greaterThan = function (_0x11319b) {
        return this.compare(_0x11319b) > 0x0;
      };
      _0x5095e2.prototype.greaterThanOrEqual = function (_0x5d933b) {
        return this.compare(_0x5d933b) >= 0x0;
      };
      _0x5095e2.prototype.compare = function (_0x408ada) {
        if (this.equals(_0x408ada)) {
          return 0x0;
        }
        var _0x571bf4 = this.isNegative();
        var _0x20b73f = _0x408ada.isNegative();
        return _0x571bf4 && !_0x20b73f ? -0x1 : !_0x571bf4 && _0x20b73f ? 0x1 : this.subtract(_0x408ada).isNegative() ? -0x1 : 0x1;
      };
      _0x5095e2.prototype.negate = function () {
        return this.equals(_0x5095e2.MIN_VALUE) ? _0x5095e2.MIN_VALUE : this.not().add(_0x5095e2.ONE);
      };
      _0x5095e2.prototype.add = function (_0x4a6441) {
        var _0x930221 = this.high_ >>> 0x10;
        var _0x83e9d4 = 0xffff & this.high_;
        var _0x55434f = this.low_ >>> 0x10;
        var _0x429402 = 0xffff & this.low_;
        var _0xb814b2 = _0x4a6441.high_ >>> 0x10;
        var _0x599666 = 0xffff & _0x4a6441.high_;
        var _0x433f52 = _0x4a6441.low_ >>> 0x10;
        var _0x3f9816 = 0x0;
        var _0x51e928 = 0x0;
        var _0x464cfb = 0x0;
        var _0x3f566c = 0x0;
        _0x464cfb += (_0x3f566c += _0x429402 + (0xffff & _0x4a6441.low_)) >>> 0x10;
        _0x3f566c &= 0xffff;
        _0x51e928 += (_0x464cfb += _0x55434f + _0x433f52) >>> 0x10;
        _0x464cfb &= 0xffff;
        _0x3f9816 += (_0x51e928 += _0x83e9d4 + _0x599666) >>> 0x10;
        _0x51e928 &= 0xffff;
        _0x3f9816 += _0x930221 + _0xb814b2;
        _0x3f9816 &= 0xffff;
        return _0x5095e2.fromBits(_0x464cfb << 0x10 | _0x3f566c, _0x3f9816 << 0x10 | _0x51e928);
      };
      _0x5095e2.prototype.subtract = function (_0x368133) {
        return this.add(_0x368133.negate());
      };
      _0x5095e2.prototype.multiply = function (_0x13f400) {
        if (this.isZero()) {
          return _0x5095e2.ZERO;
        }
        if (_0x13f400.isZero()) {
          return _0x5095e2.ZERO;
        }
        if (this.equals(_0x5095e2.MIN_VALUE)) {
          return _0x13f400.isOdd() ? _0x5095e2.MIN_VALUE : _0x5095e2.ZERO;
        }
        if (_0x13f400.equals(_0x5095e2.MIN_VALUE)) {
          return this.isOdd() ? _0x5095e2.MIN_VALUE : _0x5095e2.ZERO;
        }
        if (this.isNegative()) {
          return _0x13f400.isNegative() ? this.negate().multiply(_0x13f400.negate()) : this.negate().multiply(_0x13f400).negate();
        }
        if (_0x13f400.isNegative()) {
          return this.multiply(_0x13f400.negate()).negate();
        }
        if (this.lessThan(_0x5095e2.TWO_PWR_24_) && _0x13f400.lessThan(_0x5095e2.TWO_PWR_24_)) {
          return _0x5095e2.fromNumber(this.toNumber() * _0x13f400.toNumber());
        }
        var _0x4d85f8 = this.high_ >>> 0x10;
        var _0x593209 = 0xffff & this.high_;
        var _0x1485f8 = this.low_ >>> 0x10;
        var _0x13ac81 = 0xffff & this.low_;
        var _0x5e58e1 = _0x13f400.high_ >>> 0x10;
        var _0x262baf = 0xffff & _0x13f400.high_;
        var _0x85879f = _0x13f400.low_ >>> 0x10;
        var _0x337796 = 0xffff & _0x13f400.low_;
        var _0x5670d2 = 0x0;
        var _0x3d98fc = 0x0;
        var _0x449b86 = 0x0;
        var _0x550aa5 = 0x0;
        _0x449b86 += (_0x550aa5 += _0x13ac81 * _0x337796) >>> 0x10;
        _0x550aa5 &= 0xffff;
        _0x3d98fc += (_0x449b86 += _0x1485f8 * _0x337796) >>> 0x10;
        _0x449b86 &= 0xffff;
        _0x3d98fc += (_0x449b86 += _0x13ac81 * _0x85879f) >>> 0x10;
        _0x449b86 &= 0xffff;
        _0x5670d2 += (_0x3d98fc += _0x593209 * _0x337796) >>> 0x10;
        _0x3d98fc &= 0xffff;
        _0x5670d2 += (_0x3d98fc += _0x1485f8 * _0x85879f) >>> 0x10;
        _0x3d98fc &= 0xffff;
        _0x5670d2 += (_0x3d98fc += _0x13ac81 * _0x262baf) >>> 0x10;
        _0x3d98fc &= 0xffff;
        _0x5670d2 += _0x4d85f8 * _0x337796 + _0x593209 * _0x85879f + _0x1485f8 * _0x262baf + _0x13ac81 * _0x5e58e1;
        _0x5670d2 &= 0xffff;
        return _0x5095e2.fromBits(_0x449b86 << 0x10 | _0x550aa5, _0x5670d2 << 0x10 | _0x3d98fc);
      };
      _0x5095e2.prototype.div = function (_0x4517e8) {
        if (_0x4517e8.isZero()) {
          throw Error("division by zero");
        }
        if (this.isZero()) {
          return _0x5095e2.ZERO;
        }
        if (this.equals(_0x5095e2.MIN_VALUE)) {
          if (_0x4517e8.equals(_0x5095e2.ONE) || _0x4517e8.equals(_0x5095e2.NEG_ONE)) {
            return _0x5095e2.MIN_VALUE;
          }
          if (_0x4517e8.equals(_0x5095e2.MIN_VALUE)) {
            return _0x5095e2.ONE;
          }
          var _0x41b005 = this.shiftRight(0x1).div(_0x4517e8).shiftLeft(0x1);
          if (_0x41b005.equals(_0x5095e2.ZERO)) {
            return _0x4517e8.isNegative() ? _0x5095e2.ONE : _0x5095e2.NEG_ONE;
          }
          var _0x5830c1 = this.subtract(_0x4517e8.multiply(_0x41b005));
          return _0x41b005.add(_0x5830c1.div(_0x4517e8));
        }
        if (_0x4517e8.equals(_0x5095e2.MIN_VALUE)) {
          return _0x5095e2.ZERO;
        }
        if (this.isNegative()) {
          return _0x4517e8.isNegative() ? this.negate().div(_0x4517e8.negate()) : this.negate().div(_0x4517e8).negate();
        }
        if (_0x4517e8.isNegative()) {
          return this.div(_0x4517e8.negate()).negate();
        }
        var _0x3cd1cd = _0x5095e2.ZERO;
        for (_0x5830c1 = this; _0x5830c1.greaterThanOrEqual(_0x4517e8);) {
          _0x41b005 = Math.max(0x1, Math.floor(_0x5830c1.toNumber() / _0x4517e8.toNumber()));
          var _0x327f31 = Math.ceil(Math.log(_0x41b005) / Math.LN2);
          var _0x446be7 = _0x327f31 <= 0x30 ? 0x1 : Math.pow(0x2, _0x327f31 - 0x30);
          var _0x8e46f2 = _0x5095e2.fromNumber(_0x41b005);
          for (var _0x3dbb20 = _0x8e46f2.multiply(_0x4517e8); _0x3dbb20.isNegative() || _0x3dbb20.greaterThan(_0x5830c1);) {
            _0x41b005 -= _0x446be7;
            _0x3dbb20 = (_0x8e46f2 = _0x5095e2.fromNumber(_0x41b005)).multiply(_0x4517e8);
          }
          if (_0x8e46f2.isZero()) {
            _0x8e46f2 = _0x5095e2.ONE;
          }
          _0x3cd1cd = _0x3cd1cd.add(_0x8e46f2);
          _0x5830c1 = _0x5830c1.subtract(_0x3dbb20);
        }
        return _0x3cd1cd;
      };
      _0x5095e2.prototype.modulo = function (_0x53283c) {
        return this.subtract(this.div(_0x53283c).multiply(_0x53283c));
      };
      _0x5095e2.prototype.not = function () {
        return _0x5095e2.fromBits(~this.low_, ~this.high_);
      };
      _0x5095e2.prototype.and = function (_0xa568bf) {
        return _0x5095e2.fromBits(this.low_ & _0xa568bf.low_, this.high_ & _0xa568bf.high_);
      };
      _0x5095e2.prototype.or = function (_0x52f1ca) {
        return _0x5095e2.fromBits(this.low_ | _0x52f1ca.low_, this.high_ | _0x52f1ca.high_);
      };
      _0x5095e2.prototype.xor = function (_0x18d006) {
        return _0x5095e2.fromBits(this.low_ ^ _0x18d006.low_, this.high_ ^ _0x18d006.high_);
      };
      _0x5095e2.prototype.shiftLeft = function (_0x542f51) {
        if (0x0 == (_0x542f51 &= 0x3f)) {
          return this;
        }
        var _0x2e1c9e = this.low_;
        if (_0x542f51 < 0x20) {
          var _0x47debb = this.high_;
          return _0x5095e2.fromBits(_0x2e1c9e << _0x542f51, _0x47debb << _0x542f51 | _0x2e1c9e >>> 0x20 - _0x542f51);
        }
        return _0x5095e2.fromBits(0x0, _0x2e1c9e << _0x542f51 - 0x20);
      };
      _0x5095e2.prototype.shiftRight = function (_0x3c4fb7) {
        if (0x0 == (_0x3c4fb7 &= 0x3f)) {
          return this;
        }
        var _0x430123 = this.high_;
        if (_0x3c4fb7 < 0x20) {
          var _0x48cfb2 = this.low_;
          return _0x5095e2.fromBits(_0x48cfb2 >>> _0x3c4fb7 | _0x430123 << 0x20 - _0x3c4fb7, _0x430123 >> _0x3c4fb7);
        }
        return _0x5095e2.fromBits(_0x430123 >> _0x3c4fb7 - 0x20, _0x430123 >= 0x0 ? 0x0 : -0x1);
      };
      _0x5095e2.prototype.shiftRightUnsigned = function (_0x3ac8e7) {
        if (0x0 == (_0x3ac8e7 &= 0x3f)) {
          return this;
        }
        var _0x44b49a = this.high_;
        if (_0x3ac8e7 < 0x20) {
          var _0x4c8d0b = this.low_;
          return _0x5095e2.fromBits(_0x4c8d0b >>> _0x3ac8e7 | _0x44b49a << 0x20 - _0x3ac8e7, _0x44b49a >>> _0x3ac8e7);
        }
        return 0x20 === _0x3ac8e7 ? _0x5095e2.fromBits(_0x44b49a, 0x0) : _0x5095e2.fromBits(_0x44b49a >>> _0x3ac8e7 - 0x20, 0x0);
      };
      _0x5095e2.fromInt = function (_0x730549) {
        if (-0x80 <= _0x730549 && _0x730549 < 0x80) {
          var _0x15aa96 = _0x5095e2.INT_CACHE_[_0x730549];
          if (_0x15aa96) {
            return _0x15aa96;
          }
        }
        var _0x16fdd7 = new _0x5095e2(0x0 | _0x730549, _0x730549 < 0x0 ? -0x1 : 0x0);
        if (-0x80 <= _0x730549 && _0x730549 < 0x80) {
          _0x5095e2.INT_CACHE_[_0x730549] = _0x16fdd7;
        }
        return _0x16fdd7;
      };
      _0x5095e2.fromNumber = function (_0x4424c7) {
        return isNaN(_0x4424c7) || !isFinite(_0x4424c7) ? _0x5095e2.ZERO : _0x4424c7 <= -_0x5095e2.TWO_PWR_63_DBL_ ? _0x5095e2.MIN_VALUE : _0x4424c7 + 0x1 >= _0x5095e2.TWO_PWR_63_DBL_ ? _0x5095e2.MAX_VALUE : _0x4424c7 < 0x0 ? _0x5095e2.fromNumber(-_0x4424c7).negate() : new _0x5095e2(_0x4424c7 % _0x5095e2.TWO_PWR_32_DBL_ | 0x0, _0x4424c7 / _0x5095e2.TWO_PWR_32_DBL_ | 0x0);
      };
      _0x5095e2.fromBits = function (_0x1018e8, _0x4e2801) {
        return new _0x5095e2(_0x1018e8, _0x4e2801);
      };
      _0x5095e2.fromString = function (_0x5e8f19, _0x49ecbc) {
        if (0x0 === _0x5e8f19.length) {
          throw Error("number format error: empty string");
        }
        var _0x42f177 = _0x49ecbc || 0xa;
        if (_0x42f177 < 0x2 || 0x24 < _0x42f177) {
          throw Error("radix out of range: " + _0x42f177);
        }
        if ('-' === _0x5e8f19.charAt(0x0)) {
          return _0x5095e2.fromString(_0x5e8f19.substring(0x1), _0x42f177).negate();
        }
        if (_0x5e8f19.indexOf('-') >= 0x0) {
          throw Error("number format error: interior \"-\" character: " + _0x5e8f19);
        }
        var _0x197bf8 = _0x5095e2.fromNumber(Math.pow(_0x42f177, 0x8));
        var _0x6bae2c = _0x5095e2.ZERO;
        for (var _0x3c4762 = 0x0; _0x3c4762 < _0x5e8f19.length; _0x3c4762 += 0x8) {
          var _0x1e4944 = Math.min(0x8, _0x5e8f19.length - _0x3c4762);
          var _0x2b9e8f = parseInt(_0x5e8f19.substring(_0x3c4762, _0x3c4762 + _0x1e4944), _0x42f177);
          if (_0x1e4944 < 0x8) {
            var _0x3947ef = _0x5095e2.fromNumber(Math.pow(_0x42f177, _0x1e4944));
            _0x6bae2c = _0x6bae2c.multiply(_0x3947ef).add(_0x5095e2.fromNumber(_0x2b9e8f));
          } else {
            _0x6bae2c = (_0x6bae2c = _0x6bae2c.multiply(_0x197bf8)).add(_0x5095e2.fromNumber(_0x2b9e8f));
          }
        }
        return _0x6bae2c;
      };
      _0x5095e2.INT_CACHE_ = {};
      _0x5095e2.TWO_PWR_16_DBL_ = 0x10000;
      _0x5095e2.TWO_PWR_24_DBL_ = 16777216;
      _0x5095e2.TWO_PWR_32_DBL_ = _0x5095e2.TWO_PWR_16_DBL_ * _0x5095e2.TWO_PWR_16_DBL_;
      _0x5095e2.TWO_PWR_31_DBL_ = _0x5095e2.TWO_PWR_32_DBL_ / 0x2;
      _0x5095e2.TWO_PWR_48_DBL_ = _0x5095e2.TWO_PWR_32_DBL_ * _0x5095e2.TWO_PWR_16_DBL_;
      _0x5095e2.TWO_PWR_64_DBL_ = _0x5095e2.TWO_PWR_32_DBL_ * _0x5095e2.TWO_PWR_32_DBL_;
      _0x5095e2.TWO_PWR_63_DBL_ = _0x5095e2.TWO_PWR_64_DBL_ / 0x2;
      _0x5095e2.ZERO = _0x5095e2.fromInt(0x0);
      _0x5095e2.ONE = _0x5095e2.fromInt(0x1);
      _0x5095e2.NEG_ONE = _0x5095e2.fromInt(-0x1);
      _0x5095e2.MAX_VALUE = _0x5095e2.fromBits(-0x1, 0x7fffffff);
      _0x5095e2.MIN_VALUE = _0x5095e2.fromBits(0x0, -0x80000000);
      _0x5095e2.TWO_PWR_24_ = _0x5095e2.fromInt(16777216);
      _0x2f2a68.exports = _0x5095e2;
      _0x2f2a68.exports.Timestamp = _0x5095e2;
    },
    0x2e0: (_0x35fef6, _0xe126dc, _0x20c2c9) => {
      _0x35fef6.exports = function (_0x205388) {
        function _0x52a68a(_0x317b75) {
          let _0xc4932a;
          let _0x43737c;
          let _0x55b934;
          let _0x1e3733 = null;
          function _0x1613a3(..._0x53ec4c) {
            if (!_0x1613a3.enabled) {
              return;
            }
            const _0x325b7a = Number(new Date());
            const _0x25ce0c = _0x325b7a - (_0xc4932a || _0x325b7a);
            _0x1613a3.diff = _0x25ce0c;
            _0x1613a3.prev = _0xc4932a;
            _0x1613a3.curr = _0x325b7a;
            _0xc4932a = _0x325b7a;
            _0x53ec4c[0x0] = _0x52a68a.coerce(_0x53ec4c[0x0]);
            if ("string" != typeof _0x53ec4c[0x0]) {
              _0x53ec4c.unshift('%O');
            }
            let _0x52f8bf = 0x0;
            _0x53ec4c[0x0] = _0x53ec4c[0x0].replace(/%([a-zA-Z%])/g, (_0x5db60a, _0x40a05b) => {
              if ('%%' === _0x5db60a) {
                return '%';
              }
              _0x52f8bf++;
              const _0x246393 = _0x52a68a.formatters[_0x40a05b];
              if ("function" == typeof _0x246393) {
                const _0x30a1a7 = _0x53ec4c[_0x52f8bf];
                _0x5db60a = _0x246393.call(_0x1613a3, _0x30a1a7);
                _0x53ec4c.splice(_0x52f8bf, 0x1);
                _0x52f8bf--;
              }
              return _0x5db60a;
            });
            _0x52a68a.formatArgs.call(_0x1613a3, _0x53ec4c);
            (_0x1613a3.log || _0x52a68a.log).apply(_0x1613a3, _0x53ec4c);
          }
          _0x1613a3.namespace = _0x317b75;
          _0x1613a3.useColors = _0x52a68a.useColors();
          _0x1613a3.color = _0x52a68a.selectColor(_0x317b75);
          _0x1613a3.extend = _0x8c8df3;
          _0x1613a3.destroy = _0x52a68a.destroy;
          Object.defineProperty(_0x1613a3, "enabled", {
            'enumerable': true,
            'configurable': false,
            'get': () => null !== _0x1e3733 ? _0x1e3733 : (_0x43737c !== _0x52a68a.namespaces && (_0x43737c = _0x52a68a.namespaces, _0x55b934 = _0x52a68a.enabled(_0x317b75)), _0x55b934),
            'set': _0x4d672f => {
              _0x1e3733 = _0x4d672f;
            }
          });
          if ("function" == typeof _0x52a68a.init) {
            _0x52a68a.init(_0x1613a3);
          }
          return _0x1613a3;
        }
        function _0x8c8df3(_0x5816dc, _0x3c5368) {
          const _0x4d81f5 = _0x52a68a(this.namespace + (undefined === _0x3c5368 ? ':' : _0x3c5368) + _0x5816dc);
          _0x4d81f5.log = this.log;
          return _0x4d81f5;
        }
        function _0x4a5e5f(_0x2ee36b, _0x2a9c64) {
          let _0x3e54e8 = 0x0;
          let _0x2971e8 = 0x0;
          let _0x187050 = -0x1;
          let _0x3c98dd = 0x0;
          for (; _0x3e54e8 < _0x2ee36b.length;) {
            if (_0x2971e8 < _0x2a9c64.length && (_0x2a9c64[_0x2971e8] === _0x2ee36b[_0x3e54e8] || '*' === _0x2a9c64[_0x2971e8])) {
              if ('*' === _0x2a9c64[_0x2971e8]) {
                _0x187050 = _0x2971e8;
                _0x3c98dd = _0x3e54e8;
                _0x2971e8++;
              } else {
                _0x3e54e8++;
                _0x2971e8++;
              }
            } else {
              if (-0x1 === _0x187050) {
                return false;
              }
              _0x2971e8 = _0x187050 + 0x1;
              _0x3c98dd++;
              _0x3e54e8 = _0x3c98dd;
            }
          }
          for (; _0x2971e8 < _0x2a9c64.length && '*' === _0x2a9c64[_0x2971e8];) {
            _0x2971e8++;
          }
          return _0x2971e8 === _0x2a9c64.length;
        }
        _0x52a68a.debug = _0x52a68a;
        _0x52a68a['default'] = _0x52a68a;
        _0x52a68a.coerce = function (_0xae12b7) {
          return _0xae12b7 instanceof Error ? _0xae12b7.stack || _0xae12b7.message : _0xae12b7;
        };
        _0x52a68a.disable = function () {
          const _0x3f1080 = [..._0x52a68a.names, ..._0x52a68a.skips.map(_0x29affa => '-' + _0x29affa)].join(',');
          _0x52a68a.enable('');
          return _0x3f1080;
        };
        _0x52a68a.enable = function (_0x2e4016) {
          _0x52a68a.save(_0x2e4016);
          _0x52a68a.namespaces = _0x2e4016;
          _0x52a68a.names = [];
          _0x52a68a.skips = [];
          const _0x568f5e = ('string' == typeof _0x2e4016 ? _0x2e4016 : '').trim().replace(/\s+/g, ',').split(',').filter(Boolean);
          for (const _0x460d6a of _0x568f5e) if ('-' === _0x460d6a[0x0]) {
            _0x52a68a.skips.push(_0x460d6a.slice(0x1));
          } else {
            _0x52a68a.names.push(_0x460d6a);
          }
        };
        _0x52a68a.enabled = function (_0x5e1298) {
          for (const _0x30113a of _0x52a68a.skips) if (_0x4a5e5f(_0x5e1298, _0x30113a)) {
            return false;
          }
          for (const _0x340afb of _0x52a68a.names) if (_0x4a5e5f(_0x5e1298, _0x340afb)) {
            return true;
          }
          return false;
        };
        _0x52a68a.humanize = _0x20c2c9(0x19b9);
        _0x52a68a.destroy = function () {
          console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");
        };
        Object.keys(_0x205388).forEach(_0x42341b => {
          _0x52a68a[_0x42341b] = _0x205388[_0x42341b];
        });
        _0x52a68a.names = [];
        _0x52a68a.skips = [];
        _0x52a68a.formatters = {};
        _0x52a68a.selectColor = function (_0x18ba87) {
          let _0xb5efab = 0x0;
          for (let _0x22f4b4 = 0x0; _0x22f4b4 < _0x18ba87.length; _0x22f4b4++) {
            _0xb5efab = (_0xb5efab << 0x5) - _0xb5efab + _0x18ba87.charCodeAt(_0x22f4b4);
            _0xb5efab |= 0x0;
          }
          return _0x52a68a.colors[Math.abs(_0xb5efab) % _0x52a68a.colors.length];
        };
        _0x52a68a.enable(_0x52a68a.load());
        return _0x52a68a;
      };
    },
    0x359: _0x18ca54 => {
      'use strict';

      _0x18ca54.exports = require('os');
    },
    0x41e: _0x2bb92e => {
      'use strict';

      _0x2bb92e.exports = function (_0xe39c65) {
        if ("function" == typeof Buffer.allocUnsafe) {
          try {
            return Buffer.allocUnsafe(_0xe39c65);
          } catch (_0x31dc01) {
            return new Buffer(_0xe39c65);
          }
        }
        return new Buffer(_0xe39c65);
      };
    },
    0x4d4: (_0xdb1efc, _0x48e144) => {
      'use strict';

      _0x48e144.S = function (_0x7a8395) {
        return Object.defineProperty(function () {
          if ("function" != typeof arguments[arguments.length - 0x1]) {
            return new Promise((_0x12cbad, _0x4d520c) => {
              arguments[arguments.length] = (_0x242d93, _0x2106e1) => {
                if (_0x242d93) {
                  return _0x4d520c(_0x242d93);
                }
                _0x12cbad(_0x2106e1);
              };
              arguments.length++;
              _0x7a8395.apply(this, arguments);
            });
          }
          _0x7a8395.apply(this, arguments);
        }, "name", {
          'value': _0x7a8395.name
        });
      };
      _0x48e144.z = function (_0x1703ce) {
        return Object.defineProperty(function () {
          const _0x2ddd95 = arguments[arguments.length - 0x1];
          if ("function" != typeof _0x2ddd95) {
            return _0x1703ce.apply(this, arguments);
          }
          _0x1703ce.apply(this, arguments).then(_0x117a5a => _0x2ddd95(null, _0x117a5a), _0x2ddd95);
        }, "name", {
          'value': _0x1703ce.name
        });
      };
    },
    0x4e1: (_0x25c7c0, _0x23b799, _0x1f4bd3) => {
      'use strict';

      const _0x48d30a = _0x1f4bd3(0xe97);
      const _0x1880fb = _0x1f4bd3(0x1b10);
      const _0x412e02 = _0x1f4bd3(0xa35);
      const _0x1263e9 = "win32" === process.platform;
      function _0x3ea0c6(_0x156676) {
        ["unlink", "chmod", 'stat', "lstat", "rmdir", 'readdir'].forEach(_0x16cd5b => {
          _0x156676[_0x16cd5b] = _0x156676[_0x16cd5b] || _0x48d30a[_0x16cd5b];
          _0x156676[_0x16cd5b += "Sync"] = _0x156676[_0x16cd5b] || _0x48d30a[_0x16cd5b];
        });
        _0x156676.maxBusyTries = _0x156676.maxBusyTries || 0x3;
      }
      function _0x1edac0(_0x52c298, _0x9ffd3, _0x5537a6) {
        let _0x9af677 = 0x0;
        if ("function" == typeof _0x9ffd3) {
          _0x5537a6 = _0x9ffd3;
          _0x9ffd3 = {};
        }
        _0x412e02(_0x52c298, "rimraf: missing path");
        _0x412e02.strictEqual(typeof _0x52c298, "string", "rimraf: path should be a string");
        _0x412e02.strictEqual(typeof _0x5537a6, "function", "rimraf: callback function required");
        _0x412e02(_0x9ffd3, "rimraf: invalid options argument provided");
        _0x412e02.strictEqual(typeof _0x9ffd3, "object", "rimraf: options should be object");
        _0x3ea0c6(_0x9ffd3);
        _0x2468e4(_0x52c298, _0x9ffd3, function _0x2ea3d7(_0x4e1300) {
          if (_0x4e1300) {
            if (('EBUSY' === _0x4e1300.code || 'ENOTEMPTY' === _0x4e1300.code || "EPERM" === _0x4e1300.code) && _0x9af677 < _0x9ffd3.maxBusyTries) {
              _0x9af677++;
              return setTimeout(() => _0x2468e4(_0x52c298, _0x9ffd3, _0x2ea3d7), 0x64 * _0x9af677);
            }
            if ("ENOENT" === _0x4e1300.code) {
              _0x4e1300 = null;
            }
          }
          _0x5537a6(_0x4e1300);
        });
      }
      function _0x2468e4(_0x220e3e, _0x357d7f, _0x59c67d) {
        _0x412e02(_0x220e3e);
        _0x412e02(_0x357d7f);
        _0x412e02("function" == typeof _0x59c67d);
        _0x357d7f.lstat(_0x220e3e, (_0x4cc2c2, _0x410bcc) => _0x4cc2c2 && "ENOENT" === _0x4cc2c2.code ? _0x59c67d(null) : _0x4cc2c2 && "EPERM" === _0x4cc2c2.code && _0x1263e9 ? _0x581afc(_0x220e3e, _0x357d7f, _0x4cc2c2, _0x59c67d) : _0x410bcc && _0x410bcc.isDirectory() ? _0x4b3e85(_0x220e3e, _0x357d7f, _0x4cc2c2, _0x59c67d) : void _0x357d7f.unlink(_0x220e3e, _0x2b6d57 => {
          if (_0x2b6d57) {
            if ("ENOENT" === _0x2b6d57.code) {
              return _0x59c67d(null);
            }
            if ("EPERM" === _0x2b6d57.code) {
              return _0x1263e9 ? _0x581afc(_0x220e3e, _0x357d7f, _0x2b6d57, _0x59c67d) : _0x4b3e85(_0x220e3e, _0x357d7f, _0x2b6d57, _0x59c67d);
            }
            if ('EISDIR' === _0x2b6d57.code) {
              return _0x4b3e85(_0x220e3e, _0x357d7f, _0x2b6d57, _0x59c67d);
            }
          }
          return _0x59c67d(_0x2b6d57);
        }));
      }
      function _0x581afc(_0x5c71ac, _0x141475, _0x5a2321, _0x455676) {
        _0x412e02(_0x5c71ac);
        _0x412e02(_0x141475);
        _0x412e02("function" == typeof _0x455676);
        if (_0x5a2321) {
          _0x412e02(_0x5a2321 instanceof Error);
        }
        _0x141475.chmod(_0x5c71ac, 0x1b6, _0x28e891 => {
          if (_0x28e891) {
            _0x455676("ENOENT" === _0x28e891.code ? null : _0x5a2321);
          } else {
            _0x141475.stat(_0x5c71ac, (_0x29fb07, _0x2c5c97) => {
              if (_0x29fb07) {
                _0x455676("ENOENT" === _0x29fb07.code ? null : _0x5a2321);
              } else if (_0x2c5c97.isDirectory()) {
                _0x4b3e85(_0x5c71ac, _0x141475, _0x5a2321, _0x455676);
              } else {
                _0x141475.unlink(_0x5c71ac, _0x455676);
              }
            });
          }
        });
      }
      function _0x21c18c(_0x50d30a, _0x43b83a, _0x78e0b5) {
        let _0x5b8c73;
        _0x412e02(_0x50d30a);
        _0x412e02(_0x43b83a);
        if (_0x78e0b5) {
          _0x412e02(_0x78e0b5 instanceof Error);
        }
        try {
          _0x43b83a.chmodSync(_0x50d30a, 0x1b6);
        } catch (_0x3baa16) {
          if ("ENOENT" === _0x3baa16.code) {
            return;
          }
          throw _0x78e0b5;
        }
        try {
          _0x5b8c73 = _0x43b83a.statSync(_0x50d30a);
        } catch (_0x30ee2b) {
          if ("ENOENT" === _0x30ee2b.code) {
            return;
          }
          throw _0x78e0b5;
        }
        if (_0x5b8c73.isDirectory()) {
          _0x17585b(_0x50d30a, _0x43b83a, _0x78e0b5);
        } else {
          _0x43b83a.unlinkSync(_0x50d30a);
        }
      }
      function _0x4b3e85(_0x429c1d, _0x25357f, _0x5b6d66, _0x4655ef) {
        _0x412e02(_0x429c1d);
        _0x412e02(_0x25357f);
        if (_0x5b6d66) {
          _0x412e02(_0x5b6d66 instanceof Error);
        }
        _0x412e02('function' == typeof _0x4655ef);
        _0x25357f.rmdir(_0x429c1d, _0x402f4d => {
          if (!_0x402f4d || 'ENOTEMPTY' !== _0x402f4d.code && "EEXIST" !== _0x402f4d.code && 'EPERM' !== _0x402f4d.code) {
            if (_0x402f4d && "ENOTDIR" === _0x402f4d.code) {
              _0x4655ef(_0x5b6d66);
            } else {
              _0x4655ef(_0x402f4d);
            }
          } else {
            (function (_0x4e502b, _0x359ce1, _0x37212e) {
              _0x412e02(_0x4e502b);
              _0x412e02(_0x359ce1);
              _0x412e02("function" == typeof _0x37212e);
              _0x359ce1.readdir(_0x4e502b, (_0x27a9e5, _0x205191) => {
                if (_0x27a9e5) {
                  return _0x37212e(_0x27a9e5);
                }
                let _0x4b2474;
                let _0x319782 = _0x205191.length;
                if (0x0 === _0x319782) {
                  return _0x359ce1.rmdir(_0x4e502b, _0x37212e);
                }
                _0x205191.forEach(_0x51e8c3 => {
                  _0x1edac0(_0x1880fb.join(_0x4e502b, _0x51e8c3), _0x359ce1, _0x36c0a7 => {
                    if (!_0x4b2474) {
                      return _0x36c0a7 ? _0x37212e(_0x4b2474 = _0x36c0a7) : void (0x0 === --_0x319782 && _0x359ce1.rmdir(_0x4e502b, _0x37212e));
                    }
                  });
                });
              });
            })(_0x429c1d, _0x25357f, _0x4655ef);
          }
        });
      }
      function _0xe48838(_0x1e750c, _0x524802) {
        let _0x3456f0;
        _0x3ea0c6(_0x524802 = _0x524802 || {});
        _0x412e02(_0x1e750c, "rimraf: missing path");
        _0x412e02.strictEqual(typeof _0x1e750c, "string", "rimraf: path should be a string");
        _0x412e02(_0x524802, "rimraf: missing options");
        _0x412e02.strictEqual(typeof _0x524802, "object", "rimraf: options should be object");
        try {
          _0x3456f0 = _0x524802.lstatSync(_0x1e750c);
        } catch (_0x360246) {
          if ('ENOENT' === _0x360246.code) {
            return;
          }
          if ('EPERM' === _0x360246.code && _0x1263e9) {
            _0x21c18c(_0x1e750c, _0x524802, _0x360246);
          }
        }
        try {
          if (_0x3456f0 && _0x3456f0.isDirectory()) {
            _0x17585b(_0x1e750c, _0x524802, null);
          } else {
            _0x524802.unlinkSync(_0x1e750c);
          }
        } catch (_0x517862) {
          if ("ENOENT" === _0x517862.code) {
            return;
          }
          if ('EPERM' === _0x517862.code) {
            return _0x1263e9 ? _0x21c18c(_0x1e750c, _0x524802, _0x517862) : _0x17585b(_0x1e750c, _0x524802, _0x517862);
          }
          if ("EISDIR" !== _0x517862.code) {
            throw _0x517862;
          }
          _0x17585b(_0x1e750c, _0x524802, _0x517862);
        }
      }
      function _0x17585b(_0x47138c, _0x5b936c, _0x45a8c7) {
        _0x412e02(_0x47138c);
        _0x412e02(_0x5b936c);
        if (_0x45a8c7) {
          _0x412e02(_0x45a8c7 instanceof Error);
        }
        try {
          _0x5b936c.rmdirSync(_0x47138c);
        } catch (_0x320c55) {
          if ("ENOTDIR" === _0x320c55.code) {
            throw _0x45a8c7;
          }
          if ("ENOTEMPTY" === _0x320c55.code || 'EEXIST' === _0x320c55.code || "EPERM" === _0x320c55.code) {
            !function (_0x2d2dee, _0x26c65c) {
              _0x412e02(_0x2d2dee);
              _0x412e02(_0x26c65c);
              _0x26c65c.readdirSync(_0x2d2dee).forEach(_0x1ed633 => _0xe48838(_0x1880fb.join(_0x2d2dee, _0x1ed633), _0x26c65c));
              if (!_0x1263e9) {
                return _0x26c65c.rmdirSync(_0x2d2dee, _0x26c65c);
              }
              {
                const _0x22e58a = Date.now();
                do {
                  try {
                    return _0x26c65c.rmdirSync(_0x2d2dee, _0x26c65c);
                  } catch (_0x40b997) {}
                } while (Date.now() - _0x22e58a < 0x1f4);
              }
            }(_0x47138c, _0x5b936c);
          } else {
            if ("ENOENT" !== _0x320c55.code) {
              throw _0x320c55;
            }
          }
        }
      }
      _0x25c7c0.exports = _0x1edac0;
      _0x1edac0.sync = _0xe48838;
    },
    0x503: _0x1285fa => {
      'use strict';

      _0x1285fa.exports = function (_0x246406) {
        if (null === _0x246406 || "object" != typeof _0x246406) {
          return _0x246406;
        }
        if (_0x246406 instanceof Object) {
          var _0x51d079 = {
            '__proto__': _0x34fc9f(_0x246406)
          };
        } else {
          _0x51d079 = Object.create(null);
        }
        Object.getOwnPropertyNames(_0x246406).forEach(function (_0x4bc51d) {
          Object.defineProperty(_0x51d079, _0x4bc51d, Object.getOwnPropertyDescriptor(_0x246406, _0x4bc51d));
        });
        return _0x51d079;
      };
      var _0x34fc9f = Object.getPrototypeOf || function (_0x189559) {
        return _0x189559.__proto__;
      };
    },
    0x576: _0x566e8d => {
      'use strict';

      _0x566e8d.exports = require('vscode');
    },
    0x5f2: (_0x484e5f, _0x25abdd, _0x5d9f91) => {
      'use strict';

      const _0x1c2621 = _0x5d9f91(0x4d4).S;
      const _0x3a3f0f = _0x5d9f91(0x1b10);
      const _0x5cc180 = _0x5d9f91(0xe97);
      const _0x801b9d = _0x5d9f91(0xed6);
      const _0x3aa67f = _0x5d9f91(0x2448).pathExists;
      _0x484e5f.exports = {
        'createLink': _0x1c2621(function (_0x5415b2, _0x116ee2, _0x448a28) {
          function _0x116314(_0x1ec00e, _0x519bd0) {
            _0x5cc180.link(_0x1ec00e, _0x519bd0, _0x229624 => {
              if (_0x229624) {
                return _0x448a28(_0x229624);
              }
              _0x448a28(null);
            });
          }
          _0x3aa67f(_0x116ee2, (_0x390a3b, _0x25017b) => _0x390a3b ? _0x448a28(_0x390a3b) : _0x25017b ? _0x448a28(null) : void _0x5cc180.lstat(_0x5415b2, _0x30cccf => {
            if (_0x30cccf) {
              _0x30cccf.message = _0x30cccf.message.replace("lstat", "ensureLink");
              return _0x448a28(_0x30cccf);
            }
            const _0x5771cb = _0x3a3f0f.dirname(_0x116ee2);
            _0x3aa67f(_0x5771cb, (_0x374d9a, _0x21000d) => _0x374d9a ? _0x448a28(_0x374d9a) : _0x21000d ? _0x116314(_0x5415b2, _0x116ee2) : void _0x801b9d.mkdirs(_0x5771cb, _0x13286d => {
              if (_0x13286d) {
                return _0x448a28(_0x13286d);
              }
              _0x116314(_0x5415b2, _0x116ee2);
            }));
          }));
        }),
        'createLinkSync': function (_0x164130, _0x31dda7) {
          if (_0x5cc180.existsSync(_0x31dda7)) {
            return;
          }
          try {
            _0x5cc180.lstatSync(_0x164130);
          } catch (_0x1e2a9e) {
            _0x1e2a9e.message = _0x1e2a9e.message.replace("lstat", "ensureLink");
            throw _0x1e2a9e;
          }
          const _0x1d6d4a = _0x3a3f0f.dirname(_0x31dda7);
          if (!_0x5cc180.existsSync(_0x1d6d4a)) {
            _0x801b9d.mkdirsSync(_0x1d6d4a);
          }
          return _0x5cc180.linkSync(_0x164130, _0x31dda7);
        }
      };
    },
    0x645: (_0x32e89b, _0x1646f3, _0x4db959) => {
      'use strict';

      _0x32e89b.exports = Object.assign({}, _0x4db959(0x2221), _0x4db959(0x759), _0x4db959(0x1147), _0x4db959(0xa1f), _0x4db959(0x1890), _0x4db959(0xd30), _0x4db959(0xed6), _0x4db959(0x21a9), _0x4db959(0x997), _0x4db959(0xee3), _0x4db959(0x2448), _0x4db959(0x152e));
      const _0x284a07 = _0x4db959(0x26a8);
      if (Object.getOwnPropertyDescriptor(_0x284a07, "promises")) {
        Object.defineProperty(_0x32e89b.exports, "promises", {
          'get': () => _0x284a07.promises
        });
      }
    },
    0x68f: (_0x45540b, _0x8ed9ba, _0x21e98c) => {
      var _0x16331a = _0x21e98c(0xd62);
      var _0x598888 = _0x21e98c(0xa61);
      var _0x115961 = _0x21e98c(0xc55);
      var _0x4db7de = _0x21e98c(0x17bc);
      var _0x19de54 = _0x21e98c(0xa88);
      var _0x52babd = _0x21e98c(0xb01);
      var _0x180097 = _0x21e98c(0x1163);
      var _0xa0f903 = _0x21e98c(0xdde);
      var _0x2276dd = _0x21e98c(0x8b8);
      var _0x30e652 = _0x21e98c(0x1800);
      var _0x21c2e9 = _0x21e98c(0x1c2e);
      var _0x488128 = _0x21e98c(0x1526);
      var _0x32978c = _0x21e98c(0x20f9);
      var _0x173316 = _0x21e98c(0x1ff0);
      var _0x80b1f0 = _0x21e98c(0x2ca);
      _0x16331a.BSON_INT32_MAX = 0x7fffffff;
      _0x16331a.BSON_INT32_MIN = -0x80000000;
      _0x16331a.BSON_INT64_MAX = Math.pow(0x2, 0x3f) - 0x1;
      _0x16331a.BSON_INT64_MIN = -Math.pow(0x2, 0x3f);
      _0x16331a.JS_INT_MAX = 0x20000000000000;
      _0x16331a.JS_INT_MIN = -0x20000000000000;
      _0x16331a.Binary = _0x598888;
      _0x16331a.Code = _0x115961;
      _0x16331a.DBRef = _0x4db7de;
      _0x16331a.Decimal128 = _0x19de54;
      _0x16331a.Double = _0x52babd;
      _0x16331a.Int32 = _0x180097;
      _0x16331a.Long = _0xa0f903;
      _0x16331a.Map = _0x2276dd;
      _0x16331a.MaxKey = _0x30e652;
      _0x16331a.MinKey = _0x21c2e9;
      _0x16331a.ObjectId = _0x488128;
      _0x16331a.ObjectID = _0x488128;
      _0x16331a.BSONRegExp = _0x32978c;
      _0x16331a.Symbol = _0x173316;
      _0x16331a.Timestamp = _0x80b1f0;
      _0x45540b.exports = _0x16331a;
    },
    0x70c: (_0x3df6e6, _0x4fe4bb) => {
      _0x4fe4bb.o = function (_0x5156d9, _0x9b61e4, _0x1efc09, _0x29932c, _0x56ea59, _0x28b879) {
        var _0x26331a;
        var _0x356541;
        var _0x1985c2;
        var _0x3cd4a5 = "big" === _0x29932c;
        var _0xa5943f = 0x8 * _0x28b879 - _0x56ea59 - 0x1;
        var _0x211a08 = (0x1 << _0xa5943f) - 0x1;
        var _0x5d3935 = _0x211a08 >> 0x1;
        var _0x10b58a = 0x17 === _0x56ea59 ? Math.pow(0x2, -0x18) - Math.pow(0x2, -0x4d) : 0x0;
        var _0x2e60d2 = _0x3cd4a5 ? _0x28b879 - 0x1 : 0x0;
        var _0x363977 = _0x3cd4a5 ? -0x1 : 0x1;
        var _0x547856 = _0x9b61e4 < 0x0 || 0x0 === _0x9b61e4 && 0x1 / _0x9b61e4 < 0x0 ? 0x1 : 0x0;
        _0x9b61e4 = Math.abs(_0x9b61e4);
        for (isNaN(_0x9b61e4) || _0x9b61e4 === Infinity ? (_0x356541 = isNaN(_0x9b61e4) ? 0x1 : 0x0, _0x26331a = _0x211a08) : (_0x26331a = Math.floor(Math.log(_0x9b61e4) / Math.LN2), _0x9b61e4 * (_0x1985c2 = Math.pow(0x2, -_0x26331a)) < 0x1 && (_0x26331a--, _0x1985c2 *= 0x2), (_0x9b61e4 += _0x26331a + _0x5d3935 >= 0x1 ? _0x10b58a / _0x1985c2 : _0x10b58a * Math.pow(0x2, 0x1 - _0x5d3935)) * _0x1985c2 >= 0x2 && (_0x26331a++, _0x1985c2 /= 0x2), _0x26331a + _0x5d3935 >= _0x211a08 ? (_0x356541 = 0x0, _0x26331a = _0x211a08) : _0x26331a + _0x5d3935 >= 0x1 ? (_0x356541 = (_0x9b61e4 * _0x1985c2 - 0x1) * Math.pow(0x2, _0x56ea59), _0x26331a += _0x5d3935) : (_0x356541 = _0x9b61e4 * Math.pow(0x2, _0x5d3935 - 0x1) * Math.pow(0x2, _0x56ea59), _0x26331a = 0x0)); _0x56ea59 >= 0x8; _0x5156d9[_0x1efc09 + _0x2e60d2] = 0xff & _0x356541, _0x2e60d2 += _0x363977, _0x356541 /= 0x100, _0x56ea59 -= 0x8) {
          ;
        }
        _0x26331a = _0x26331a << _0x56ea59 | _0x356541;
        for (_0xa5943f += _0x56ea59; _0xa5943f > 0x0; _0x5156d9[_0x1efc09 + _0x2e60d2] = 0xff & _0x26331a, _0x2e60d2 += _0x363977, _0x26331a /= 0x100, _0xa5943f -= 0x8) {
          ;
        }
        _0x5156d9[_0x1efc09 + _0x2e60d2 - _0x363977] |= 0x80 * _0x547856;
      };
    },
    0x759: (_0x393d66, _0x57b704, _0x116b55) => {
      'use strict';

      _0x393d66.exports = {
        'copySync': _0x116b55(0xae)
      };
    },
    0x788: _0x30ec74 => {
      'use strict';

      function _0x4900d1(_0x22f8c8) {
        this.message = _0x22f8c8;
      }
      _0x4900d1.prototype.toString = function () {
        return "Cancel" + (this.message ? ": " + this.message : '');
      };
      _0x4900d1.prototype.__CANCEL__ = true;
      _0x30ec74.exports = _0x4900d1;
    },
    0x7cb: (_0x2acf3b, _0x5baacc, _0x5d3f16) => {
      var _0x326992 = _0x5d3f16(0x89b).Stream;
      _0x2acf3b.exports = function (_0x452b07) {
        return {
          'ReadStream': function _0x467f04(_0x554671, _0x9900c4) {
            if (!(this instanceof _0x467f04)) {
              return new _0x467f04(_0x554671, _0x9900c4);
            }
            _0x326992.call(this);
            var _0x132b6a = this;
            this.path = _0x554671;
            this.fd = null;
            this.readable = true;
            this.paused = false;
            this.flags = 'r';
            this.mode = 0x1b6;
            this.bufferSize = 0x10000;
            _0x9900c4 = _0x9900c4 || {};
            var _0x3b9258 = Object.keys(_0x9900c4);
            var _0x142adf = 0x0;
            for (var _0x46897b = _0x3b9258.length; _0x142adf < _0x46897b; _0x142adf++) {
              var _0x550144 = _0x3b9258[_0x142adf];
              this[_0x550144] = _0x9900c4[_0x550144];
            }
            if (this.encoding) {
              this.setEncoding(this.encoding);
            }
            if (undefined !== this.start) {
              if ('number' != typeof this.start) {
                throw TypeError("start must be a Number");
              }
              if (undefined === this.end) {
                this.end = Infinity;
              } else {
                if ("number" != typeof this.end) {
                  throw TypeError("end must be a Number");
                }
              }
              if (this.start > this.end) {
                throw new Error("start must be <= end");
              }
              this.pos = this.start;
            }
            if (null === this.fd) {
              _0x452b07.open(this.path, this.flags, this.mode, function (_0x587b97, _0x612591) {
                if (_0x587b97) {
                  _0x132b6a.emit("error", _0x587b97);
                  return void (_0x132b6a.readable = false);
                }
                _0x132b6a.fd = _0x612591;
                _0x132b6a.emit("open", _0x612591);
                _0x132b6a._read();
              });
            } else {
              process.nextTick(function () {
                _0x132b6a._read();
              });
            }
          },
          'WriteStream': function _0x31e504(_0x43082f, _0x4d592c) {
            if (!(this instanceof _0x31e504)) {
              return new _0x31e504(_0x43082f, _0x4d592c);
            }
            _0x326992.call(this);
            this.path = _0x43082f;
            this.fd = null;
            this.writable = true;
            this.flags = 'w';
            this.encoding = "binary";
            this.mode = 0x1b6;
            this.bytesWritten = 0x0;
            _0x4d592c = _0x4d592c || {};
            var _0x3aec3b = Object.keys(_0x4d592c);
            var _0x4ef66d = 0x0;
            for (var _0x3c2990 = _0x3aec3b.length; _0x4ef66d < _0x3c2990; _0x4ef66d++) {
              var _0x41874c = _0x3aec3b[_0x4ef66d];
              this[_0x41874c] = _0x4d592c[_0x41874c];
            }
            if (undefined !== this.start) {
              if ('number' != typeof this.start) {
                throw TypeError("start must be a Number");
              }
              if (this.start < 0x0) {
                throw new Error("start must be >= zero");
              }
              this.pos = this.start;
            }
            this.busy = false;
            this._queue = [];
            if (null === this.fd) {
              this._open = _0x452b07.open;
              this._queue.push([this._open, this.path, this.flags, this.mode, undefined]);
              this.flush();
            }
          }
        };
      };
    },
    0x7dc: (_0x3ed285, _0x2d680d, _0x1916b2) => {
      'use strict';

      var _0x5b9bb4 = _0x1916b2(0x252c);
      var _0x548da3 = ["age", "authorization", "content-length", 'content-type', "etag", 'expires', "from", 'host', "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", 'proxy-authorization', "referer", "retry-after", "user-agent"];
      _0x3ed285.exports = function (_0x163a79) {
        var _0x2806c5;
        var _0x458e01;
        var _0x377964;
        var _0x46a543 = {};
        return _0x163a79 ? (_0x5b9bb4.forEach(_0x163a79.split("\n"), function (_0xbae377) {
          _0x377964 = _0xbae377.indexOf(':');
          _0x2806c5 = _0x5b9bb4.trim(_0xbae377.substr(0x0, _0x377964)).toLowerCase();
          _0x458e01 = _0x5b9bb4.trim(_0xbae377.substr(_0x377964 + 0x1));
          if (_0x2806c5) {
            if (_0x46a543[_0x2806c5] && _0x548da3.indexOf(_0x2806c5) >= 0x0) {
              return;
            }
            _0x46a543[_0x2806c5] = "set-cookie" === _0x2806c5 ? (_0x46a543[_0x2806c5] ? _0x46a543[_0x2806c5] : []).concat([_0x458e01]) : _0x46a543[_0x2806c5] ? _0x46a543[_0x2806c5] + ", " + _0x458e01 : _0x458e01;
          }
        }), _0x46a543) : _0x46a543;
      };
    },
    0x7e2: _0x25716b => {
      'use strict';

      _0x25716b.exports = require('tty');
    },
    0x815: (_0x480ea5, _0x181821, _0x5ef891) => {
      'use strict';

      const _0x2fb83b = _0x5ef891(0x1b10);
      function _0x4009f9(_0x4852da) {
        return (_0x4852da = _0x2fb83b.normalize(_0x2fb83b.resolve(_0x4852da)).split(_0x2fb83b.sep)).length > 0x0 ? _0x4852da[0x0] : null;
      }
      const _0x4fd3b0 = /[<>:"|?*]/;
      _0x480ea5.exports = {
        'getRootPath': _0x4009f9,
        'invalidWin32Path': function (_0x21f8f3) {
          const _0x773435 = _0x4009f9(_0x21f8f3);
          _0x21f8f3 = _0x21f8f3.replace(_0x773435, '');
          return _0x4fd3b0.test(_0x21f8f3);
        }
      };
    },
    0x88c: (_0x598537, _0x477e22, _0x3e5e1e) => {
      'use strict';

      const _0x46e659 = _0x3e5e1e(0xe97);
      const _0x5192f7 = _0x3e5e1e(0x1b10);
      const _0x20bf61 = _0x3e5e1e(0xed6);
      const _0x1d1613 = _0x3e5e1e(0xb76);
      _0x598537.exports = function (_0x3d9a6c, _0x138d91, _0x4df1fd) {
        const _0x1cd6f0 = _0x5192f7.dirname(_0x3d9a6c);
        if (!_0x46e659.existsSync(_0x1cd6f0)) {
          _0x20bf61.mkdirsSync(_0x1cd6f0);
        }
        _0x1d1613.writeJsonSync(_0x3d9a6c, _0x138d91, _0x4df1fd);
      };
    },
    0x89b: _0x59c304 => {
      'use strict';

      _0x59c304.exports = require("stream");
    },
    0x8b8: _0x488c13 => {
      'use strict';

      if (undefined !== global.Map) {
        _0x488c13.exports = global.Map;
        _0x488c13.exports.Map = global.Map;
      } else {
        var _0x31be7e = function (_0xd9799f) {
          this._keys = [];
          this._values = {};
          for (var _0x1bf425 = 0x0; _0x1bf425 < _0xd9799f.length; _0x1bf425++) {
            if (null != _0xd9799f[_0x1bf425]) {
              var _0x32d647 = _0xd9799f[_0x1bf425];
              var _0x22a431 = _0x32d647[0x0];
              var _0x225949 = _0x32d647[0x1];
              this._keys.push(_0x22a431);
              this._values[_0x22a431] = {
                'v': _0x225949,
                'i': this._keys.length - 0x1
              };
            }
          }
        };
        _0x31be7e.prototype.clear = function () {
          this._keys = [];
          this._values = {};
        };
        _0x31be7e.prototype["delete"] = function (_0x5c88e7) {
          var _0x4fb754 = this._values[_0x5c88e7];
          return null != _0x4fb754 && (delete this._values[_0x5c88e7], this._keys.splice(_0x4fb754.i, 0x1), true);
        };
        _0x31be7e.prototype.entries = function () {
          var _0x52a4b2 = this;
          var _0x336cd9 = 0x0;
          return {
            'next': function () {
              var _0x5c4624 = _0x52a4b2._keys[_0x336cd9++];
              return {
                'value': undefined !== _0x5c4624 ? [_0x5c4624, _0x52a4b2._values[_0x5c4624].v] : undefined,
                'done': undefined === _0x5c4624
              };
            }
          };
        };
        _0x31be7e.prototype.forEach = function (_0x560f52, _0x14676a) {
          _0x14676a = _0x14676a || this;
          for (var _0x2358f1 = 0x0; _0x2358f1 < this._keys.length; _0x2358f1++) {
            var _0x3bfb47 = this._keys[_0x2358f1];
            _0x560f52.call(_0x14676a, this._values[_0x3bfb47].v, _0x3bfb47, _0x14676a);
          }
        };
        _0x31be7e.prototype.get = function (_0x5113d3) {
          return this._values[_0x5113d3] ? this._values[_0x5113d3].v : undefined;
        };
        _0x31be7e.prototype.has = function (_0x34f091) {
          return null != this._values[_0x34f091];
        };
        _0x31be7e.prototype.keys = function () {
          var _0x68febb = this;
          var _0x529c32 = 0x0;
          return {
            'next': function () {
              var _0x5237ea = _0x68febb._keys[_0x529c32++];
              return {
                'value': undefined !== _0x5237ea ? _0x5237ea : undefined,
                'done': undefined === _0x5237ea
              };
            }
          };
        };
        _0x31be7e.prototype.set = function (_0x5acb19, _0x205438) {
          return this._values[_0x5acb19] ? (this._values[_0x5acb19].v = _0x205438, this) : (this._keys.push(_0x5acb19), this._values[_0x5acb19] = {
            'v': _0x205438,
            'i': this._keys.length - 0x1
          }, this);
        };
        _0x31be7e.prototype.values = function () {
          var _0x99efb0 = this;
          var _0xa21e1e = 0x0;
          return {
            'next': function () {
              var _0x321e0d = _0x99efb0._keys[_0xa21e1e++];
              return {
                'value': undefined !== _0x321e0d ? _0x99efb0._values[_0x321e0d].v : undefined,
                'done': undefined === _0x321e0d
              };
            }
          };
        };
        Object.defineProperty(_0x31be7e.prototype, "size", {
          'enumerable': true,
          'get': function () {
            return this._keys.length;
          }
        });
        _0x488c13.exports = _0x31be7e;
        _0x488c13.exports.Map = _0x31be7e;
      }
    },
    0x997: (_0x45ee35, _0x1fa514, _0x1f7e2a) => {
      'use strict';

      const _0x4dfa61 = _0x1f7e2a(0x4d4).S;
      _0x45ee35.exports = {
        'move': _0x4dfa61(_0x1f7e2a(0xcf2))
      };
    },
    0x9c9: (_0x125586, _0x139edc, _0x210acd) => {
      _0x125586.exports = _0x210acd(0x1f4f);
    },
    0xa1f: (_0x711fb4, _0xca48a7, _0x30041e) => {
      'use strict';

      const _0x5db839 = _0x30041e(0x4d4).S;
      const _0x6b0dd2 = _0x30041e(0xe97);
      const _0x352a51 = _0x30041e(0x1b10);
      const _0x4c3f51 = _0x30041e(0xed6);
      const _0x211d84 = _0x30041e(0x152e);
      const _0x314ed6 = _0x5db839(function (_0x2cfc8b, _0x4ee7eb) {
        _0x4ee7eb = _0x4ee7eb || function () {};
        _0x6b0dd2.readdir(_0x2cfc8b, (_0x447a4f, _0x21f8bb) => {
          if (_0x447a4f) {
            return _0x4c3f51.mkdirs(_0x2cfc8b, _0x4ee7eb);
          }
          _0x21f8bb = _0x21f8bb.map(_0x178607 => _0x352a51.join(_0x2cfc8b, _0x178607));
          (function _0x56833a() {
            const _0x355441 = _0x21f8bb.pop();
            if (!_0x355441) {
              return _0x4ee7eb();
            }
            _0x211d84.remove(_0x355441, _0x3ba635 => {
              if (_0x3ba635) {
                return _0x4ee7eb(_0x3ba635);
              }
              _0x56833a();
            });
          })();
        });
      });
      function _0x5aa8b5(_0x528a3c) {
        let _0x491fc1;
        try {
          _0x491fc1 = _0x6b0dd2.readdirSync(_0x528a3c);
        } catch (_0x18bb6e) {
          return _0x4c3f51.mkdirsSync(_0x528a3c);
        }
        _0x491fc1.forEach(_0x3b36ef => {
          _0x3b36ef = _0x352a51.join(_0x528a3c, _0x3b36ef);
          _0x211d84.removeSync(_0x3b36ef);
        });
      }
      _0x711fb4.exports = {
        'emptyDirSync': _0x5aa8b5,
        'emptydirSync': _0x5aa8b5,
        'emptyDir': _0x314ed6,
        'emptydir': _0x314ed6
      };
    },
    0xa35: _0x3f3906 => {
      'use strict';

      _0x3f3906.exports = require("assert");
    },
    0xa61: (_0x4b8496, _0x2ab969, _0x4cd5d8) => {
      if ('undefined' != typeof global) {
        var _0x22704 = _0x4cd5d8(0xb5).Buffer;
      }
      var _0x5cc79 = _0x4cd5d8(0x1a9d);
      function _0x22ce7f(_0x1bfd51, _0x45d05a) {
        if (!(this instanceof _0x22ce7f)) {
          return new _0x22ce7f(_0x1bfd51, _0x45d05a);
        }
        if (!(null == _0x1bfd51 || "string" == typeof _0x1bfd51 || _0x22704.isBuffer(_0x1bfd51) || _0x1bfd51 instanceof Uint8Array || Array.isArray(_0x1bfd51))) {
          throw new Error("only String, Buffer, Uint8Array or Array accepted");
        }
        this._bsontype = "Binary";
        if (_0x1bfd51 instanceof Number) {
          this.sub_type = _0x1bfd51;
          this.position = 0x0;
        } else {
          this.sub_type = null == _0x45d05a ? 0x0 : _0x45d05a;
          this.position = 0x0;
        }
        if (null == _0x1bfd51 || _0x1bfd51 instanceof Number) {
          if (undefined !== _0x22704) {
            this.buffer = _0x5cc79.allocBuffer(_0x22ce7f.BUFFER_SIZE);
          } else if ("undefined" != typeof Uint8Array) {
            this.buffer = new Uint8Array(new ArrayBuffer(_0x22ce7f.BUFFER_SIZE));
          } else {
            this.buffer = new Array(_0x22ce7f.BUFFER_SIZE);
          }
          this.position = 0x0;
        } else {
          if ("string" == typeof _0x1bfd51) {
            if (undefined !== _0x22704) {
              this.buffer = _0x5cc79.toBuffer(_0x1bfd51);
            } else {
              if ("undefined" == typeof Uint8Array && "[object Array]" !== Object.prototype.toString.call(_0x1bfd51)) {
                throw new Error("only String, Buffer, Uint8Array or Array accepted");
              }
              this.buffer = _0x41e7d3(_0x1bfd51);
            }
          } else {
            this.buffer = _0x1bfd51;
          }
          this.position = _0x1bfd51.length;
        }
      }
      _0x22ce7f.prototype.put = function (_0xa3261f) {
        if (null != _0xa3261f.length && 'number' != typeof _0xa3261f && 0x1 !== _0xa3261f.length) {
          throw new Error("only accepts single character String, Uint8Array or Array");
        }
        if ("number" != typeof _0xa3261f && _0xa3261f < 0x0 || _0xa3261f > 0xff) {
          throw new Error("only accepts number in a valid unsigned byte range 0-255");
        }
        var _0x2b72a7;
        _0x2b72a7 = "string" == typeof _0xa3261f ? _0xa3261f.charCodeAt(0x0) : null != _0xa3261f.length ? _0xa3261f[0x0] : _0xa3261f;
        if (this.buffer.length > this.position) {
          this.buffer[this.position++] = _0x2b72a7;
        } else {
          if (undefined !== _0x22704 && _0x22704.isBuffer(this.buffer)) {
            var _0x2a5ff8 = _0x5cc79.allocBuffer(_0x22ce7f.BUFFER_SIZE + this.buffer.length);
            this.buffer.copy(_0x2a5ff8, 0x0, 0x0, this.buffer.length);
            this.buffer = _0x2a5ff8;
            this.buffer[this.position++] = _0x2b72a7;
          } else {
            _0x2a5ff8 = null;
            _0x2a5ff8 = "[object Uint8Array]" === Object.prototype.toString.call(this.buffer) ? new Uint8Array(new ArrayBuffer(_0x22ce7f.BUFFER_SIZE + this.buffer.length)) : new Array(_0x22ce7f.BUFFER_SIZE + this.buffer.length);
            for (var _0x5d46ed = 0x0; _0x5d46ed < this.buffer.length; _0x5d46ed++) {
              _0x2a5ff8[_0x5d46ed] = this.buffer[_0x5d46ed];
            }
            this.buffer = _0x2a5ff8;
            this.buffer[this.position++] = _0x2b72a7;
          }
        }
      };
      _0x22ce7f.prototype.write = function (_0x5a2ddf, _0x5097dc) {
        _0x5097dc = "number" == typeof _0x5097dc ? _0x5097dc : this.position;
        if (this.buffer.length < _0x5097dc + _0x5a2ddf.length) {
          var _0x25375a = null;
          if (undefined !== _0x22704 && _0x22704.isBuffer(this.buffer)) {
            _0x25375a = _0x5cc79.allocBuffer(this.buffer.length + _0x5a2ddf.length);
            this.buffer.copy(_0x25375a, 0x0, 0x0, this.buffer.length);
          } else {
            if ("[object Uint8Array]" === Object.prototype.toString.call(this.buffer)) {
              _0x25375a = new Uint8Array(new ArrayBuffer(this.buffer.length + _0x5a2ddf.length));
              for (var _0xb5a37d = 0x0; _0xb5a37d < this.position; _0xb5a37d++) {
                _0x25375a[_0xb5a37d] = this.buffer[_0xb5a37d];
              }
            }
          }
          this.buffer = _0x25375a;
        }
        if (undefined !== _0x22704 && _0x22704.isBuffer(_0x5a2ddf) && _0x22704.isBuffer(this.buffer)) {
          _0x5a2ddf.copy(this.buffer, _0x5097dc, 0x0, _0x5a2ddf.length);
          this.position = _0x5097dc + _0x5a2ddf.length > this.position ? _0x5097dc + _0x5a2ddf.length : this.position;
        } else {
          if (undefined !== _0x22704 && "string" == typeof _0x5a2ddf && _0x22704.isBuffer(this.buffer)) {
            this.buffer.write(_0x5a2ddf, _0x5097dc, "binary");
            this.position = _0x5097dc + _0x5a2ddf.length > this.position ? _0x5097dc + _0x5a2ddf.length : this.position;
          } else {
            if ("[object Uint8Array]" === Object.prototype.toString.call(_0x5a2ddf) || "[object Array]" === Object.prototype.toString.call(_0x5a2ddf) && 'string' != typeof _0x5a2ddf) {
              for (_0xb5a37d = 0x0; _0xb5a37d < _0x5a2ddf.length; _0xb5a37d++) {
                this.buffer[_0x5097dc++] = _0x5a2ddf[_0xb5a37d];
              }
              this.position = _0x5097dc > this.position ? _0x5097dc : this.position;
            } else {
              if ("string" == typeof _0x5a2ddf) {
                for (_0xb5a37d = 0x0; _0xb5a37d < _0x5a2ddf.length; _0xb5a37d++) {
                  this.buffer[_0x5097dc++] = _0x5a2ddf.charCodeAt(_0xb5a37d);
                }
                this.position = _0x5097dc > this.position ? _0x5097dc : this.position;
              }
            }
          }
        }
      };
      _0x22ce7f.prototype.read = function (_0x133307, _0x3b06cd) {
        _0x3b06cd = _0x3b06cd && _0x3b06cd > 0x0 ? _0x3b06cd : this.position;
        if (this.buffer.slice) {
          return this.buffer.slice(_0x133307, _0x133307 + _0x3b06cd);
        }
        var _0x4e30f6 = "undefined" != typeof Uint8Array ? new Uint8Array(new ArrayBuffer(_0x3b06cd)) : new Array(_0x3b06cd);
        for (var _0x84f00f = 0x0; _0x84f00f < _0x3b06cd; _0x84f00f++) {
          _0x4e30f6[_0x84f00f] = this.buffer[_0x133307++];
        }
        return _0x4e30f6;
      };
      _0x22ce7f.prototype.value = function (_0xf0e6d) {
        if ((_0xf0e6d = null != _0xf0e6d && _0xf0e6d) && undefined !== _0x22704 && _0x22704.isBuffer(this.buffer) && this.buffer.length === this.position) {
          return this.buffer;
        }
        if (undefined !== _0x22704 && _0x22704.isBuffer(this.buffer)) {
          return _0xf0e6d ? this.buffer.slice(0x0, this.position) : this.buffer.toString("binary", 0x0, this.position);
        }
        if (_0xf0e6d) {
          if (null != this.buffer.slice) {
            return this.buffer.slice(0x0, this.position);
          }
          var _0x51ee85 = "[object Uint8Array]" === Object.prototype.toString.call(this.buffer) ? new Uint8Array(new ArrayBuffer(this.position)) : new Array(this.position);
          for (var _0x2121b4 = 0x0; _0x2121b4 < this.position; _0x2121b4++) {
            _0x51ee85[_0x2121b4] = this.buffer[_0x2121b4];
          }
          return _0x51ee85;
        }
        return _0x3be609(this.buffer, 0x0, this.position);
      };
      _0x22ce7f.prototype.length = function () {
        return this.position;
      };
      _0x22ce7f.prototype.toJSON = function () {
        return null != this.buffer ? this.buffer.toString("base64") : '';
      };
      _0x22ce7f.prototype.toString = function (_0x412eb7) {
        return null != this.buffer ? this.buffer.slice(0x0, this.position).toString(_0x412eb7) : '';
      };
      var _0x41e7d3 = function (_0x2a16ac) {
        var _0x26d98b = "undefined" != typeof Uint8Array ? new Uint8Array(new ArrayBuffer(_0x2a16ac.length)) : new Array(_0x2a16ac.length);
        for (var _0x795867 = 0x0; _0x795867 < _0x2a16ac.length; _0x795867++) {
          _0x26d98b[_0x795867] = _0x2a16ac.charCodeAt(_0x795867);
        }
        return _0x26d98b;
      };
      var _0x3be609 = function (_0x5c443a, _0x4fd564, _0x402e30) {
        var _0x4107d7 = '';
        for (var _0x3fc9d6 = _0x4fd564; _0x3fc9d6 < _0x402e30; _0x3fc9d6++) {
          _0x4107d7 += String.fromCharCode(_0x5c443a[_0x3fc9d6]);
        }
        return _0x4107d7;
      };
      _0x22ce7f.BUFFER_SIZE = 0x100;
      _0x22ce7f.SUBTYPE_DEFAULT = 0x0;
      _0x22ce7f.SUBTYPE_FUNCTION = 0x1;
      _0x22ce7f.SUBTYPE_BYTE_ARRAY = 0x2;
      _0x22ce7f.SUBTYPE_UUID_OLD = 0x3;
      _0x22ce7f.SUBTYPE_UUID = 0x4;
      _0x22ce7f.SUBTYPE_MD5 = 0x5;
      _0x22ce7f.SUBTYPE_USER_DEFINED = 0x80;
      _0x4b8496.exports = _0x22ce7f;
      _0x4b8496.exports.Binary = _0x22ce7f;
    },
    0xa88: (_0x30be4e, _0xa33c15, _0x338dd8) => {
      'use strict';

      var _0xf79146 = _0x338dd8(0xdde);
      var _0x8fb615 = /^(\+|-)?(\d+|(\d*\.\d*))?(E|e)?([-+])?(\d+)?$/;
      var _0x26d0bc = /^(\+|-)?(Infinity|inf)$/i;
      var _0x41f026 = /^(\+|-)?NaN$/i;
      var _0x212c55 = -0x1820;
      var _0x3f3d66 = 0x1820;
      var _0x5dcb1c = [0x7c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0].reverse();
      var _0x580376 = [0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0].reverse();
      var _0x3f8b8b = [0x78, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0].reverse();
      var _0x1d4a4d = /^([-+])?(\d+)?$/;
      var _0x565527 = _0x338dd8(0x1a9d);
      var _0x80c041 = function (_0x4688c5) {
        var _0x2c3f52 = _0xf79146.fromNumber(0x3b9aca00);
        var _0x2ae7ef = _0xf79146.fromNumber(0x0);
        var _0x587243 = 0x0;
        if (!(_0x4688c5.parts[0x0] || _0x4688c5.parts[0x1] || _0x4688c5.parts[0x2] || _0x4688c5.parts[0x3])) {
          return {
            'quotient': _0x4688c5,
            'rem': _0x2ae7ef
          };
        }
        for (_0x587243 = 0x0; _0x587243 <= 0x3; _0x587243++) {
          _0x2ae7ef = (_0x2ae7ef = _0x2ae7ef.shiftLeft(0x20)).add(new _0xf79146(_0x4688c5.parts[_0x587243], 0x0));
          _0x4688c5.parts[_0x587243] = _0x2ae7ef.div(_0x2c3f52).low_;
          _0x2ae7ef = _0x2ae7ef.modulo(_0x2c3f52);
        }
        return {
          'quotient': _0x4688c5,
          'rem': _0x2ae7ef
        };
      };
      var _0x361002 = function (_0x1ee8db) {
        this._bsontype = "Decimal128";
        this.bytes = _0x1ee8db;
      };
      _0x361002.fromString = function (_0x2c895f) {
        var _0x5b747f;
        var _0x462437 = false;
        var _0x206402 = false;
        var _0x359fbd = false;
        var _0x218699 = 0x0;
        var _0x190c7c = 0x0;
        var _0x5c39a7 = 0x0;
        var _0x26ad68 = 0x0;
        var _0x5d9507 = 0x0;
        var _0x66b7b0 = [0x0];
        var _0x2a4d87 = 0x0;
        var _0x3b6755 = 0x0;
        var _0x30ced7 = 0x0;
        var _0xe348bd = 0x0;
        var _0x4cebfe = 0x0;
        var _0x8b5b23 = 0x0;
        var _0x111cd6 = [0x0, 0x0];
        var _0x439b22 = [0x0, 0x0];
        var _0x352684 = 0x0;
        if ((_0x2c895f = _0x2c895f.trim()).length >= 0x1b58) {
          throw new Error(_0x2c895f + " not a valid Decimal128 string");
        }
        var _0x2c2e85 = _0x2c895f.match(_0x8fb615);
        var _0x5ae926 = _0x2c895f.match(_0x26d0bc);
        var _0x2a0e62 = _0x2c895f.match(_0x41f026);
        if (!_0x2c2e85 && !_0x5ae926 && !_0x2a0e62 || 0x0 === _0x2c895f.length) {
          throw new Error(_0x2c895f + " not a valid Decimal128 string");
        }
        if (_0x2c2e85 && _0x2c2e85[0x4] && undefined === _0x2c2e85[0x2]) {
          throw new Error(_0x2c895f + " not a valid Decimal128 string");
        }
        if (!('+' !== _0x2c895f[_0x352684] && '-' !== _0x2c895f[_0x352684])) {
          _0x462437 = '-' === _0x2c895f[_0x352684++];
        }
        if (!!isNaN(parseInt(_0x2c895f[_0x352684], 0xa)) && '.' !== _0x2c895f[_0x352684]) {
          if ('i' === _0x2c895f[_0x352684] || 'I' === _0x2c895f[_0x352684]) {
            return new _0x361002(_0x565527.toBuffer(_0x462437 ? _0x580376 : _0x3f8b8b));
          }
          if ('N' === _0x2c895f[_0x352684]) {
            return new _0x361002(_0x565527.toBuffer(_0x5dcb1c));
          }
        }
        for (; !isNaN(parseInt(_0x2c895f[_0x352684], 0xa)) || '.' === _0x2c895f[_0x352684];) {
          if ('.' !== _0x2c895f[_0x352684]) {
            if (_0x2a4d87 < 0x22 && ('0' !== _0x2c895f[_0x352684] || _0x359fbd)) {
              if (!_0x359fbd) {
                _0x5d9507 = _0x190c7c;
              }
              _0x359fbd = true;
              _0x66b7b0[_0x3b6755++] = parseInt(_0x2c895f[_0x352684], 0xa);
              _0x2a4d87 += 0x1;
            }
            if (_0x359fbd) {
              _0x5c39a7 += 0x1;
            }
            if (_0x206402) {
              _0x26ad68 += 0x1;
            }
            _0x190c7c += 0x1;
            _0x352684 += 0x1;
          } else {
            if (_0x206402) {
              return new _0x361002(_0x565527.toBuffer(_0x5dcb1c));
            }
            _0x206402 = true;
            _0x352684 += 0x1;
          }
        }
        if (_0x206402 && !_0x190c7c) {
          throw new Error(_0x2c895f + " not a valid Decimal128 string");
        }
        if ('e' === _0x2c895f[_0x352684] || 'E' === _0x2c895f[_0x352684]) {
          var _0x1f2b58 = _0x2c895f.substr(++_0x352684).match(_0x1d4a4d);
          if (!_0x1f2b58 || !_0x1f2b58[0x2]) {
            return new _0x361002(_0x565527.toBuffer(_0x5dcb1c));
          }
          _0x4cebfe = parseInt(_0x1f2b58[0x0], 0xa);
          _0x352684 += _0x1f2b58[0x0].length;
        }
        if (_0x2c895f[_0x352684]) {
          return new _0x361002(_0x565527.toBuffer(_0x5dcb1c));
        }
        _0x30ced7 = 0x0;
        if (_0x2a4d87) {
          _0xe348bd = _0x2a4d87 - 0x1;
          _0x218699 = _0x5c39a7;
          if (0x0 !== _0x4cebfe && 0x1 !== _0x218699) {
            for (; '0' === _0x2c895f[_0x5d9507 + _0x218699 - 0x1];) {
              _0x218699 -= 0x1;
            }
          }
        } else {
          _0x30ced7 = 0x0;
          _0xe348bd = 0x0;
          _0x66b7b0[0x0] = 0x0;
          _0x5c39a7 = 0x1;
          _0x2a4d87 = 0x1;
          _0x218699 = 0x0;
        }
        for (_0x4cebfe <= _0x26ad68 && _0x26ad68 - _0x4cebfe > 0x4000 ? _0x4cebfe = _0x212c55 : _0x4cebfe -= _0x26ad68; _0x4cebfe > 0x17df;) {
          if ((_0xe348bd += 0x1) - _0x30ced7 > 0x22) {
            var _0xa24a0c = _0x66b7b0.join('');
            if (_0xa24a0c.match(/^0+$/)) {
              _0x4cebfe = 0x17df;
              break;
            }
            return new _0x361002(_0x565527.toBuffer(_0x462437 ? _0x580376 : _0x3f8b8b));
          }
          _0x4cebfe -= 0x1;
        }
        for (; _0x4cebfe < _0x212c55 || _0x2a4d87 < _0x5c39a7;) {
          if (0x0 === _0xe348bd) {
            _0x4cebfe = _0x212c55;
            _0x218699 = 0x0;
            break;
          }
          if (_0x2a4d87 < _0x5c39a7) {
            _0x5c39a7 -= 0x1;
          } else {
            _0xe348bd -= 0x1;
          }
          if (!(_0x4cebfe < 0x17df)) {
            if ((_0xa24a0c = _0x66b7b0.join('')).match(/^0+$/)) {
              _0x4cebfe = 0x17df;
              break;
            }
            return new _0x361002(_0x565527.toBuffer(_0x462437 ? _0x580376 : _0x3f8b8b));
          }
          _0x4cebfe += 0x1;
        }
        if (_0xe348bd - _0x30ced7 + 0x1 < _0x218699 && '0' !== _0x2c895f[_0x218699]) {
          var _0x47171b = _0x190c7c;
          if (_0x206402 && _0x4cebfe === _0x212c55) {
            _0x5d9507 += 0x1;
            _0x47171b += 0x1;
          }
          var _0xc65d7c = parseInt(_0x2c895f[_0x5d9507 + _0xe348bd + 0x1], 0xa);
          var _0x4f120c = 0x0;
          if (_0xc65d7c >= 0x5 && (_0x4f120c = 0x1, 0x5 === _0xc65d7c)) {
            _0x4f120c = _0x66b7b0[_0xe348bd] % 0x2 == 0x1;
            for (_0x8b5b23 = _0x5d9507 + _0xe348bd + 0x2; _0x8b5b23 < _0x47171b; _0x8b5b23++) {
              if (parseInt(_0x2c895f[_0x8b5b23], 0xa)) {
                _0x4f120c = 0x1;
                break;
              }
            }
          }
          if (_0x4f120c) {
            for (var _0x224468 = _0xe348bd; _0x224468 >= 0x0 && ++_0x66b7b0[_0x224468] > 0x9; _0x224468--) {
              _0x66b7b0[_0x224468] = 0x0;
              if (0x0 === _0x224468) {
                if (!(_0x4cebfe < 0x17df)) {
                  return new _0x361002(_0x565527.toBuffer(_0x462437 ? _0x580376 : _0x3f8b8b));
                }
                _0x4cebfe += 0x1;
                _0x66b7b0[_0x224468] = 0x1;
              }
            }
          }
        }
        _0x111cd6 = _0xf79146.fromNumber(0x0);
        _0x439b22 = _0xf79146.fromNumber(0x0);
        if (0x0 === _0x218699) {
          _0x111cd6 = _0xf79146.fromNumber(0x0);
          _0x439b22 = _0xf79146.fromNumber(0x0);
        } else {
          if (_0xe348bd - _0x30ced7 < 0x11) {
            _0x224468 = _0x30ced7;
            _0x439b22 = _0xf79146.fromNumber(_0x66b7b0[_0x224468++]);
            for (_0x111cd6 = new _0xf79146(0x0, 0x0); _0x224468 <= _0xe348bd; _0x224468++) {
              _0x439b22 = (_0x439b22 = _0x439b22.multiply(_0xf79146.fromNumber(0xa))).add(_0xf79146.fromNumber(_0x66b7b0[_0x224468]));
            }
          } else {
            _0x224468 = _0x30ced7;
            for (_0x111cd6 = _0xf79146.fromNumber(_0x66b7b0[_0x224468++]); _0x224468 <= _0xe348bd - 0x11; _0x224468++) {
              _0x111cd6 = (_0x111cd6 = _0x111cd6.multiply(_0xf79146.fromNumber(0xa))).add(_0xf79146.fromNumber(_0x66b7b0[_0x224468]));
            }
            for (_0x439b22 = _0xf79146.fromNumber(_0x66b7b0[_0x224468++]); _0x224468 <= _0xe348bd; _0x224468++) {
              _0x439b22 = (_0x439b22 = _0x439b22.multiply(_0xf79146.fromNumber(0xa))).add(_0xf79146.fromNumber(_0x66b7b0[_0x224468]));
            }
          }
        }
        var _0x1809de;
        var _0x35d96b;
        var _0x5c3059;
        var _0xc425d1;
        var _0x5ca66e = function (_0x584f43, _0x446810) {
          if (!_0x584f43 && !_0x446810) {
            return {
              'high': _0xf79146.fromNumber(0x0),
              'low': _0xf79146.fromNumber(0x0)
            };
          }
          var _0x4225c3 = _0x584f43.shiftRightUnsigned(0x20);
          var _0x3f3dd8 = new _0xf79146(_0x584f43.getLowBits(), 0x0);
          var _0x2d5ed0 = _0x446810.shiftRightUnsigned(0x20);
          var _0x145856 = new _0xf79146(_0x446810.getLowBits(), 0x0);
          var _0xdc1a81 = _0x4225c3.multiply(_0x2d5ed0);
          var _0x57d999 = _0x4225c3.multiply(_0x145856);
          var _0x27fd1e = _0x3f3dd8.multiply(_0x2d5ed0);
          var _0x84ea3 = _0x3f3dd8.multiply(_0x145856);
          _0xdc1a81 = _0xdc1a81.add(_0x57d999.shiftRightUnsigned(0x20));
          _0x57d999 = new _0xf79146(_0x57d999.getLowBits(), 0x0).add(_0x27fd1e).add(_0x84ea3.shiftRightUnsigned(0x20));
          return {
            'high': _0xdc1a81 = _0xdc1a81.add(_0x57d999.shiftRightUnsigned(0x20)),
            'low': _0x84ea3 = _0x57d999.shiftLeft(0x20).add(new _0xf79146(_0x84ea3.getLowBits(), 0x0))
          };
        }(_0x111cd6, _0xf79146.fromString("100000000000000000"));
        _0x5ca66e.low = _0x5ca66e.low.add(_0x439b22);
        _0x35d96b = _0x439b22;
        if ((_0x5c3059 = (_0x1809de = _0x5ca66e.low).high_ >>> 0x0) < (_0xc425d1 = _0x35d96b.high_ >>> 0x0) || _0x5c3059 === _0xc425d1 && _0x1809de.low_ >>> 0x0 < _0x35d96b.low_ >>> 0x0) {
          _0x5ca66e.high = _0x5ca66e.high.add(_0xf79146.fromNumber(0x1));
        }
        _0x5b747f = _0x4cebfe + _0x3f3d66;
        var _0x3c921f = {
          'low': _0xf79146.fromNumber(0x0),
          'high': _0xf79146.fromNumber(0x0)
        };
        if (_0x5ca66e.high.shiftRightUnsigned(0x31).and(_0xf79146.fromNumber(0x1)).equals(_0xf79146.fromNumber)) {
          _0x3c921f.high = _0x3c921f.high.or(_0xf79146.fromNumber(0x3).shiftLeft(0x3d));
          _0x3c921f.high = _0x3c921f.high.or(_0xf79146.fromNumber(_0x5b747f).and(_0xf79146.fromNumber(0x3fff).shiftLeft(0x2f)));
          _0x3c921f.high = _0x3c921f.high.or(_0x5ca66e.high.and(_0xf79146.fromNumber(0x7fffffffffff)));
        } else {
          _0x3c921f.high = _0x3c921f.high.or(_0xf79146.fromNumber(0x3fff & _0x5b747f).shiftLeft(0x31));
          _0x3c921f.high = _0x3c921f.high.or(_0x5ca66e.high.and(_0xf79146.fromNumber(0x1ffffffffffff)));
        }
        _0x3c921f.low = _0x5ca66e.low;
        if (_0x462437) {
          _0x3c921f.high = _0x3c921f.high.or(_0xf79146.fromString('9223372036854775808'));
        }
        var _0x513b6e = _0x565527.allocBuffer(0x10);
        _0x352684 = 0x0;
        _0x513b6e[_0x352684++] = 0xff & _0x3c921f.low.low_;
        _0x513b6e[_0x352684++] = _0x3c921f.low.low_ >> 0x8 & 0xff;
        _0x513b6e[_0x352684++] = _0x3c921f.low.low_ >> 0x10 & 0xff;
        _0x513b6e[_0x352684++] = _0x3c921f.low.low_ >> 0x18 & 0xff;
        _0x513b6e[_0x352684++] = 0xff & _0x3c921f.low.high_;
        _0x513b6e[_0x352684++] = _0x3c921f.low.high_ >> 0x8 & 0xff;
        _0x513b6e[_0x352684++] = _0x3c921f.low.high_ >> 0x10 & 0xff;
        _0x513b6e[_0x352684++] = _0x3c921f.low.high_ >> 0x18 & 0xff;
        _0x513b6e[_0x352684++] = 0xff & _0x3c921f.high.low_;
        _0x513b6e[_0x352684++] = _0x3c921f.high.low_ >> 0x8 & 0xff;
        _0x513b6e[_0x352684++] = _0x3c921f.high.low_ >> 0x10 & 0xff;
        _0x513b6e[_0x352684++] = _0x3c921f.high.low_ >> 0x18 & 0xff;
        _0x513b6e[_0x352684++] = 0xff & _0x3c921f.high.high_;
        _0x513b6e[_0x352684++] = _0x3c921f.high.high_ >> 0x8 & 0xff;
        _0x513b6e[_0x352684++] = _0x3c921f.high.high_ >> 0x10 & 0xff;
        _0x513b6e[_0x352684++] = _0x3c921f.high.high_ >> 0x18 & 0xff;
        return new _0x361002(_0x513b6e);
      };
      _0x3f3d66 = 0x1820;
      _0x361002.prototype.toString = function () {
        var _0x4684c2;
        var _0x73b2d3;
        var _0x416aed;
        var _0x2190f9;
        var _0x245362;
        var _0x5f4664;
        var _0x59d0d3 = 0x0;
        var _0xfbe8ab = new Array(0x24);
        for (var _0x192d75 = 0x0; _0x192d75 < _0xfbe8ab.length; _0x192d75++) {
          _0xfbe8ab[_0x192d75] = 0x0;
        }
        var _0x463ce3;
        var _0x181a82;
        var _0x536c4c;
        var _0x1329b0;
        var _0xa5df83;
        var _0xced2ba = 0x0;
        var _0x4bf3ba = false;
        var _0x4ca8fc = {
          'parts': new Array(0x4)
        };
        var _0x33ce69 = [];
        _0xced2ba = 0x0;
        var _0x2932ba = this.bytes;
        _0x2190f9 = _0x2932ba[_0xced2ba++] | _0x2932ba[_0xced2ba++] << 0x8 | _0x2932ba[_0xced2ba++] << 0x10 | _0x2932ba[_0xced2ba++] << 0x18;
        _0x416aed = _0x2932ba[_0xced2ba++] | _0x2932ba[_0xced2ba++] << 0x8 | _0x2932ba[_0xced2ba++] << 0x10 | _0x2932ba[_0xced2ba++] << 0x18;
        _0x73b2d3 = _0x2932ba[_0xced2ba++] | _0x2932ba[_0xced2ba++] << 0x8 | _0x2932ba[_0xced2ba++] << 0x10 | _0x2932ba[_0xced2ba++] << 0x18;
        _0x4684c2 = _0x2932ba[_0xced2ba++] | _0x2932ba[_0xced2ba++] << 0x8 | _0x2932ba[_0xced2ba++] << 0x10 | _0x2932ba[_0xced2ba++] << 0x18;
        _0xced2ba = 0x0;
        new _0xf79146(_0x2190f9, _0x416aed);
        if (new _0xf79146(_0x73b2d3, _0x4684c2).lessThan(_0xf79146.ZERO)) {
          _0x33ce69.push('-');
        }
        if ((_0x245362 = _0x4684c2 >> 0x1a & 0x1f) >> 0x3 == 0x3) {
          if (0x1e === _0x245362) {
            return _0x33ce69.join('') + "Infinity";
          }
          if (0x1f === _0x245362) {
            return "NaN";
          }
          _0x5f4664 = _0x4684c2 >> 0xf & 0x3fff;
          _0x536c4c = 0x8 + (_0x4684c2 >> 0xe & 0x1);
        } else {
          _0x536c4c = _0x4684c2 >> 0xe & 0x7;
          _0x5f4664 = _0x4684c2 >> 0x11 & 0x3fff;
        }
        _0x463ce3 = _0x5f4664 - _0x3f3d66;
        _0x4ca8fc.parts[0x0] = (0x3fff & _0x4684c2) + ((0xf & _0x536c4c) << 0xe);
        _0x4ca8fc.parts[0x1] = _0x73b2d3;
        _0x4ca8fc.parts[0x2] = _0x416aed;
        _0x4ca8fc.parts[0x3] = _0x2190f9;
        if (0x0 === _0x4ca8fc.parts[0x0] && 0x0 === _0x4ca8fc.parts[0x1] && 0x0 === _0x4ca8fc.parts[0x2] && 0x0 === _0x4ca8fc.parts[0x3]) {
          _0x4bf3ba = true;
        } else {
          for (_0xa5df83 = 0x3; _0xa5df83 >= 0x0; _0xa5df83--) {
            var _0x13cb6d = 0x0;
            var _0x528dfe = _0x80c041(_0x4ca8fc);
            _0x4ca8fc = _0x528dfe.quotient;
            if (_0x13cb6d = _0x528dfe.rem.low_) {
              for (_0x1329b0 = 0x8; _0x1329b0 >= 0x0; _0x1329b0--) {
                _0xfbe8ab[0x9 * _0xa5df83 + _0x1329b0] = _0x13cb6d % 0xa;
                _0x13cb6d = Math.floor(_0x13cb6d / 0xa);
              }
            }
          }
        }
        if (_0x4bf3ba) {
          _0x59d0d3 = 0x1;
          _0xfbe8ab[_0xced2ba] = 0x0;
        } else {
          _0x59d0d3 = 0x24;
          for (_0x192d75 = 0x0; !_0xfbe8ab[_0xced2ba];) {
            _0x192d75++;
            _0x59d0d3 -= 0x1;
            _0xced2ba += 0x1;
          }
        }
        if ((_0x181a82 = _0x59d0d3 - 0x1 + _0x463ce3) >= 0x22 || _0x181a82 <= -0x7 || _0x463ce3 > 0x0) {
          _0x33ce69.push(_0xfbe8ab[_0xced2ba++]);
          if (_0x59d0d3 -= 0x1) {
            _0x33ce69.push('.');
          }
          for (_0x192d75 = 0x0; _0x192d75 < _0x59d0d3; _0x192d75++) {
            _0x33ce69.push(_0xfbe8ab[_0xced2ba++]);
          }
          _0x33ce69.push('E');
          if (_0x181a82 > 0x0) {
            _0x33ce69.push('+' + _0x181a82);
          } else {
            _0x33ce69.push(_0x181a82);
          }
        } else {
          if (_0x463ce3 >= 0x0) {
            for (_0x192d75 = 0x0; _0x192d75 < _0x59d0d3; _0x192d75++) {
              _0x33ce69.push(_0xfbe8ab[_0xced2ba++]);
            }
          } else {
            var _0x3a200f = _0x59d0d3 + _0x463ce3;
            if (_0x3a200f > 0x0) {
              for (_0x192d75 = 0x0; _0x192d75 < _0x3a200f; _0x192d75++) {
                _0x33ce69.push(_0xfbe8ab[_0xced2ba++]);
              }
            } else {
              _0x33ce69.push('0');
            }
            for (_0x33ce69.push('.'); _0x3a200f++ < 0x0;) {
              _0x33ce69.push('0');
            }
            for (_0x192d75 = 0x0; _0x192d75 < _0x59d0d3 - Math.max(_0x3a200f - 0x1, 0x0); _0x192d75++) {
              _0x33ce69.push(_0xfbe8ab[_0xced2ba++]);
            }
          }
        }
        return _0x33ce69.join('');
      };
      _0x361002.prototype.toJSON = function () {
        return {
          '$numberDecimal': this.toString()
        };
      };
      _0x30be4e.exports = _0x361002;
      _0x30be4e.exports.Decimal128 = _0x361002;
    },
    0xb01: _0xad31b4 => {
      function _0x15841b(_0x395c13) {
        if (!(this instanceof _0x15841b)) {
          return new _0x15841b(_0x395c13);
        }
        this._bsontype = "Double";
        this.value = _0x395c13;
      }
      _0x15841b.prototype.valueOf = function () {
        return this.value;
      };
      _0x15841b.prototype.toJSON = function () {
        return this.value;
      };
      _0xad31b4.exports = _0x15841b;
      _0xad31b4.exports.Double = _0x15841b;
    },
    0xb41: (_0x597938, _0x4a54fa, _0x3bdb01) => {
      'use strict';

      var _0x8c78a2 = _0x3bdb01(0x252c);
      var _0x2a7a66 = _0x3bdb01(0x1b4b);
      _0x597938.exports = function (_0x3dc947, _0xeabe57, _0x5c44e5) {
        var _0x155c5a = this || _0x2a7a66;
        _0x8c78a2.forEach(_0x5c44e5, function (_0x563675) {
          _0x3dc947 = _0x563675.call(_0x155c5a, _0x3dc947, _0xeabe57);
        });
        return _0x3dc947;
      };
    },
    0xb5d: (_0x10b648, _0x7d8a3b, _0x2e6580) => {
      var _0xad50c6;
      try {
        _0xad50c6 = _0x2e6580(0xe97);
      } catch (_0x22542a) {
        _0xad50c6 = _0x2e6580(0x26a8);
      }
      function _0x127b3b(_0x392cf7, _0x53e770) {
        var _0x51fdb9;
        var _0x449b11 = "\n";
        if ("object" == typeof _0x53e770 && null !== _0x53e770) {
          if (_0x53e770.spaces) {
            _0x51fdb9 = _0x53e770.spaces;
          }
          if (_0x53e770.EOL) {
            _0x449b11 = _0x53e770.EOL;
          }
        }
        return JSON.stringify(_0x392cf7, _0x53e770 ? _0x53e770.replacer : null, _0x51fdb9).replace(/\n/g, _0x449b11) + _0x449b11;
      }
      function _0x5227da(_0x35a111) {
        if (Buffer.isBuffer(_0x35a111)) {
          _0x35a111 = _0x35a111.toString("utf8");
        }
        return _0x35a111.replace(/^\uFEFF/, '');
      }
      var _0x97978c = {
        'readFile': function (_0x15736e, _0x216884, _0x3599c8) {
          if (null == _0x3599c8) {
            _0x3599c8 = _0x216884;
            _0x216884 = {};
          }
          if ("string" == typeof _0x216884) {
            _0x216884 = {
              'encoding': _0x216884
            };
          }
          var _0x250f8e = (_0x216884 = _0x216884 || {}).fs || _0xad50c6;
          var _0x1c7755 = true;
          if ('throws' in _0x216884) {
            _0x1c7755 = _0x216884.throws;
          }
          _0x250f8e.readFile(_0x15736e, _0x216884, function (_0x586002, _0x263870) {
            if (_0x586002) {
              return _0x3599c8(_0x586002);
            }
            var _0xe3b73e;
            _0x263870 = _0x5227da(_0x263870);
            try {
              _0xe3b73e = JSON.parse(_0x263870, _0x216884 ? _0x216884.reviver : null);
            } catch (_0x294319) {
              return _0x1c7755 ? (_0x294319.message = _0x15736e + ": " + _0x294319.message, _0x3599c8(_0x294319)) : _0x3599c8(null, null);
            }
            _0x3599c8(null, _0xe3b73e);
          });
        },
        'readFileSync': function (_0x29fd62, _0x2f73fe) {
          if ("string" == typeof (_0x2f73fe = _0x2f73fe || {})) {
            _0x2f73fe = {
              'encoding': _0x2f73fe
            };
          }
          var _0x1c9f6a = _0x2f73fe.fs || _0xad50c6;
          var _0x2cb727 = true;
          if ('throws' in _0x2f73fe) {
            _0x2cb727 = _0x2f73fe.throws;
          }
          try {
            var _0x250468 = _0x1c9f6a.readFileSync(_0x29fd62, _0x2f73fe);
            _0x250468 = _0x5227da(_0x250468);
            return JSON.parse(_0x250468, _0x2f73fe.reviver);
          } catch (_0x2074f2) {
            if (_0x2cb727) {
              _0x2074f2.message = _0x29fd62 + ": " + _0x2074f2.message;
              throw _0x2074f2;
            }
            return null;
          }
        },
        'writeFile': function (_0x4c68fb, _0x420afe, _0x3954bf, _0x27a96a) {
          if (null == _0x27a96a) {
            _0x27a96a = _0x3954bf;
            _0x3954bf = {};
          }
          var _0x212aba = (_0x3954bf = _0x3954bf || {}).fs || _0xad50c6;
          var _0x46b26c = '';
          try {
            _0x46b26c = _0x127b3b(_0x420afe, _0x3954bf);
          } catch (_0x1fd40b) {
            return void (_0x27a96a && _0x27a96a(_0x1fd40b, null));
          }
          _0x212aba.writeFile(_0x4c68fb, _0x46b26c, _0x3954bf, _0x27a96a);
        },
        'writeFileSync': function (_0x1b8cf2, _0x52ac27, _0x36cb2f) {
          var _0x469086 = (_0x36cb2f = _0x36cb2f || {}).fs || _0xad50c6;
          var _0x56c5ed = _0x127b3b(_0x52ac27, _0x36cb2f);
          return _0x469086.writeFileSync(_0x1b8cf2, _0x56c5ed, _0x36cb2f);
        }
      };
      _0x10b648.exports = _0x97978c;
    },
    0xb76: (_0x200e17, _0x4ff135, _0x29928c) => {
      'use strict';

      const _0x489710 = _0x29928c(0x4d4).S;
      const _0x2caf45 = _0x29928c(0xb5d);
      _0x200e17.exports = {
        'readJson': _0x489710(_0x2caf45.readFile),
        'readJsonSync': _0x2caf45.readFileSync,
        'writeJson': _0x489710(_0x2caf45.writeFile),
        'writeJsonSync': _0x2caf45.writeFileSync
      };
    },
    0xc22: _0x242cfe => {
      'use strict';

      _0x242cfe.exports = require('zlib');
    },
    0xc55: _0x2faf89 => {
      var _0x257919 = function _0x11d5b1(_0x388803, _0x5cf572) {
        if (!(this instanceof _0x11d5b1)) {
          return new _0x11d5b1(_0x388803, _0x5cf572);
        }
        this._bsontype = "Code";
        this.code = _0x388803;
        this.scope = _0x5cf572;
      };
      _0x257919.prototype.toJSON = function () {
        return {
          'scope': this.scope,
          'code': this.code
        };
      };
      _0x2faf89.exports = _0x257919;
      _0x2faf89.exports.Code = _0x257919;
    },
    0xc5c: (_0x199c45, _0x3fb0c1, _0x543340) => {
      var _0x4e1d5b;
      var _0xa7b998;
      var _0x5a723c;
      var _0x158b39 = _0x543340(0x1b68);
      var _0x8305fe = _0x158b39.URL;
      var _0x1cea77 = _0x543340(0x21a3);
      var _0x1532e5 = _0x543340(0x163c);
      var _0x2bcf65 = _0x543340(0x89b).Writable;
      var _0x2e61fd = _0x543340(0xa35);
      var _0x5c2103 = _0x543340(0x1d53);
      _0x4e1d5b = 'undefined' != typeof process;
      _0xa7b998 = "undefined" != typeof window && "undefined" != typeof document;
      _0x5a723c = "function" == typeof Error.captureStackTrace;
      if (!(_0x4e1d5b || !_0xa7b998 && _0x5a723c)) {
        console.warn("The follow-redirects package should be excluded from browser builds.");
      }
      var _0x3c4aa3 = false;
      try {
        _0x2e61fd(new _0x8305fe(''));
      } catch (_0x56f4cd) {
        _0x3c4aa3 = "ERR_INVALID_URL" === _0x56f4cd.code;
      }
      var _0x5b922a = ["auth", 'host', "hostname", "href", "path", "pathname", 'port', 'protocol', "query", "search", "hash"];
      var _0x24b286 = ["abort", "aborted", "connect", "error", "socket", "timeout"];
      var _0x57ec88 = Object.create(null);
      _0x24b286.forEach(function (_0x4cab62) {
        _0x57ec88[_0x4cab62] = function (_0x286e56, _0x595eff, _0x3f8b90) {
          this._redirectable.emit(_0x4cab62, _0x286e56, _0x595eff, _0x3f8b90);
        };
      });
      var _0x14cae0 = _0x58bb6c("ERR_INVALID_URL", "Invalid URL", TypeError);
      var _0xcd20a4 = _0x58bb6c("ERR_FR_REDIRECTION_FAILURE", "Redirected request failed");
      var _0x4af7cd = _0x58bb6c("ERR_FR_TOO_MANY_REDIRECTS", "Maximum number of redirects exceeded", _0xcd20a4);
      var _0x29b3ef = _0x58bb6c('ERR_FR_MAX_BODY_LENGTH_EXCEEDED', "Request body larger than maxBodyLength limit");
      var _0xce41bc = _0x58bb6c("ERR_STREAM_WRITE_AFTER_END", "write after end");
      var _0x48fdcc = _0x2bcf65.prototype.destroy || _0x49d3f0;
      function _0x5b96ef(_0x23bab7, _0x5a859c) {
        _0x2bcf65.call(this);
        this._sanitizeOptions(_0x23bab7);
        this._options = _0x23bab7;
        this._ended = false;
        this._ending = false;
        this._redirectCount = 0x0;
        this._redirects = [];
        this._requestBodyLength = 0x0;
        this._requestBodyBuffers = [];
        if (_0x5a859c) {
          this.on('response', _0x5a859c);
        }
        var _0x1d59fc = this;
        this._onNativeResponse = function (_0x2a3e93) {
          try {
            _0x1d59fc._processResponse(_0x2a3e93);
          } catch (_0x27876c) {
            _0x1d59fc.emit('error', _0x27876c instanceof _0xcd20a4 ? _0x27876c : new _0xcd20a4({
              'cause': _0x27876c
            }));
          }
        };
        this._performRequest();
      }
      function _0x5c35ab(_0x19d16e) {
        var _0x588b92 = {
          'maxRedirects': 0x15,
          'maxBodyLength': 0xa00000
        };
        var _0x520a69 = {};
        Object.keys(_0x19d16e).forEach(function (_0x1094c2) {
          var _0x5313e3 = _0x1094c2 + ':';
          var _0x5f1567 = _0x520a69[_0x5313e3] = _0x19d16e[_0x1094c2];
          var _0x296958 = _0x588b92[_0x1094c2] = Object.create(_0x5f1567);
          Object.defineProperties(_0x296958, {
            'request': {
              'value': function (_0x438739, _0xa091e4, _0x26aef9) {
                if (_0x8305fe && _0x438739 instanceof _0x8305fe) {
                  _0x438739 = _0x5997d6(_0x438739);
                } else if ("string" == typeof _0x438739 || _0x438739 instanceof String) {
                  _0x438739 = _0x5997d6(_0x4303b2(_0x438739));
                } else {
                  _0x26aef9 = _0xa091e4;
                  _0xa091e4 = _0x26dde8(_0x438739);
                  _0x438739 = {
                    'protocol': _0x5313e3
                  };
                }
                if ("function" == typeof _0xa091e4) {
                  _0x26aef9 = _0xa091e4;
                  _0xa091e4 = null;
                }
                (_0xa091e4 = Object.assign({
                  'maxRedirects': 0x15,
                  'maxBodyLength': 0xa00000
                }, _0x438739, _0xa091e4)).nativeProtocols = _0x520a69;
                if (!("string" == typeof _0xa091e4.host || _0xa091e4.host instanceof String || "string" == typeof _0xa091e4.hostname || _0xa091e4.hostname instanceof String)) {
                  _0xa091e4.hostname = "::1";
                }
                _0x2e61fd.equal(_0xa091e4.protocol, _0x5313e3, "protocol mismatch");
                _0x5c2103("options", _0xa091e4);
                return new _0x5b96ef(_0xa091e4, _0x26aef9);
              },
              'configurable': true,
              'enumerable': true,
              'writable': true
            },
            'get': {
              'value': function (_0x12c90a, _0x35e8a5, _0x27bf2d) {
                var _0x3a1489 = _0x296958.request(_0x12c90a, _0x35e8a5, _0x27bf2d);
                _0x3a1489.end();
                return _0x3a1489;
              },
              'configurable': true,
              'enumerable': true,
              'writable': true
            }
          });
        });
        return _0x588b92;
      }
      function _0x49d3f0() {}
      function _0x4303b2(_0x5d95a3) {
        var _0x3badc6;
        if (_0x3c4aa3) {
          _0x3badc6 = new _0x8305fe(_0x5d95a3);
        } else {
          if (!("string" == typeof (_0x3badc6 = _0x26dde8(_0x158b39.parse(_0x5d95a3))).protocol || (_0x3badc6 = _0x26dde8(_0x158b39.parse(_0x5d95a3))).protocol instanceof String)) {
            throw new _0x14cae0({
              'input': _0x5d95a3
            });
          }
        }
        return _0x3badc6;
      }
      function _0x26dde8(_0x106b15) {
        if (/^\[/.test(_0x106b15.hostname) && !/^\[[:0-9a-f]+\]$/i.test(_0x106b15.hostname)) {
          throw new _0x14cae0({
            'input': _0x106b15.href || _0x106b15
          });
        }
        if (/^\[/.test(_0x106b15.host) && !/^\[[:0-9a-f]+\](:\d+)?$/i.test(_0x106b15.host)) {
          throw new _0x14cae0({
            'input': _0x106b15.href || _0x106b15
          });
        }
        return _0x106b15;
      }
      function _0x5997d6(_0x1fefad, _0x1bb1ec) {
        var _0xc92f1d = _0x1bb1ec || {};
        for (var _0x4b7f0a of _0x5b922a) _0xc92f1d[_0x4b7f0a] = _0x1fefad[_0x4b7f0a];
        if (_0xc92f1d.hostname.startsWith('[')) {
          _0xc92f1d.hostname = _0xc92f1d.hostname.slice(0x1, -0x1);
        }
        if ('' !== _0xc92f1d.port) {
          _0xc92f1d.port = Number(_0xc92f1d.port);
        }
        _0xc92f1d.path = _0xc92f1d.search ? _0xc92f1d.pathname + _0xc92f1d.search : _0xc92f1d.pathname;
        return _0xc92f1d;
      }
      function _0x169648(_0x104779, _0x3bb016) {
        var _0x3f897f;
        for (var _0x587dcd in _0x3bb016) if (_0x104779.test(_0x587dcd)) {
          _0x3f897f = _0x3bb016[_0x587dcd];
          delete _0x3bb016[_0x587dcd];
        }
        return null == _0x3f897f ? undefined : String(_0x3f897f).trim();
      }
      function _0x58bb6c(_0x384e35, _0x2d8a96, _0x42db7c) {
        function _0x277066(_0x46b2a2) {
          if ("function" == typeof Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
          }
          Object.assign(this, _0x46b2a2 || {});
          this.code = _0x384e35;
          this.message = this.cause ? _0x2d8a96 + ": " + this.cause.message : _0x2d8a96;
        }
        _0x277066.prototype = new (_0x42db7c || Error)();
        Object.defineProperties(_0x277066.prototype, {
          'constructor': {
            'value': _0x277066,
            'enumerable': false
          },
          'name': {
            'value': "Error [" + _0x384e35 + ']',
            'enumerable': false
          }
        });
        return _0x277066;
      }
      function _0x1b2757(_0x184cab, _0x4a3332) {
        for (var _0x28c825 of _0x24b286) _0x184cab.removeListener(_0x28c825, _0x57ec88[_0x28c825]);
        _0x184cab.on("error", _0x49d3f0);
        _0x184cab.destroy(_0x4a3332);
      }
      _0x5b96ef.prototype = Object.create(_0x2bcf65.prototype);
      _0x5b96ef.prototype.abort = function () {
        _0x1b2757(this._currentRequest);
        this._currentRequest.abort();
        this.emit("abort");
      };
      _0x5b96ef.prototype.destroy = function (_0x335e6e) {
        _0x1b2757(this._currentRequest, _0x335e6e);
        _0x48fdcc.call(this, _0x335e6e);
        return this;
      };
      _0x5b96ef.prototype.write = function (_0x277d97, _0x594f0e, _0x3046b2) {
        if (this._ending) {
          throw new _0xce41bc();
        }
        if (!("string" == typeof _0x277d97 || _0x277d97 instanceof String || "object" == typeof (_0x52f819 = _0x277d97) && "length" in _0x52f819)) {
          throw new TypeError("data should be a string, Buffer or Uint8Array");
        }
        var _0x52f819;
        if ("function" == typeof _0x594f0e) {
          _0x3046b2 = _0x594f0e;
          _0x594f0e = null;
        }
        if (0x0 !== _0x277d97.length) {
          if (this._requestBodyLength + _0x277d97.length <= this._options.maxBodyLength) {
            this._requestBodyLength += _0x277d97.length;
            this._requestBodyBuffers.push({
              'data': _0x277d97,
              'encoding': _0x594f0e
            });
            this._currentRequest.write(_0x277d97, _0x594f0e, _0x3046b2);
          } else {
            this.emit("error", new _0x29b3ef());
            this.abort();
          }
        } else if (_0x3046b2) {
          _0x3046b2();
        }
      };
      _0x5b96ef.prototype.end = function (_0x28b3a0, _0x35c997, _0x5a1ec1) {
        if ("function" == typeof _0x28b3a0) {
          _0x5a1ec1 = _0x28b3a0;
          _0x28b3a0 = _0x35c997 = null;
        } else if ("function" == typeof _0x35c997) {
          _0x5a1ec1 = _0x35c997;
          _0x35c997 = null;
        }
        if (_0x28b3a0) {
          var _0x22e0c3 = this;
          var _0x493bef = this._currentRequest;
          this.write(_0x28b3a0, _0x35c997, function () {
            _0x22e0c3._ended = true;
            _0x493bef.end(null, null, _0x5a1ec1);
          });
          this._ending = true;
        } else {
          this._ended = this._ending = true;
          this._currentRequest.end(null, null, _0x5a1ec1);
        }
      };
      _0x5b96ef.prototype.setHeader = function (_0x1597f4, _0x382da6) {
        this._options.headers[_0x1597f4] = _0x382da6;
        this._currentRequest.setHeader(_0x1597f4, _0x382da6);
      };
      _0x5b96ef.prototype.removeHeader = function (_0x38e066) {
        delete this._options.headers[_0x38e066];
        this._currentRequest.removeHeader(_0x38e066);
      };
      _0x5b96ef.prototype.setTimeout = function (_0x5dc543, _0x41a250) {
        var _0x26359c = this;
        function _0x4a3b23(_0x163799) {
          _0x163799.setTimeout(_0x5dc543);
          _0x163799.removeListener("timeout", _0x163799.destroy);
          _0x163799.addListener("timeout", _0x163799.destroy);
        }
        function _0x3d13f9(_0x34900a) {
          if (_0x26359c._timeout) {
            clearTimeout(_0x26359c._timeout);
          }
          _0x26359c._timeout = setTimeout(function () {
            _0x26359c.emit("timeout");
            _0x2ddadd();
          }, _0x5dc543);
          _0x4a3b23(_0x34900a);
        }
        function _0x2ddadd() {
          if (_0x26359c._timeout) {
            clearTimeout(_0x26359c._timeout);
            _0x26359c._timeout = null;
          }
          _0x26359c.removeListener("abort", _0x2ddadd);
          _0x26359c.removeListener("error", _0x2ddadd);
          _0x26359c.removeListener('response', _0x2ddadd);
          _0x26359c.removeListener("close", _0x2ddadd);
          if (_0x41a250) {
            _0x26359c.removeListener("timeout", _0x41a250);
          }
          if (!_0x26359c.socket) {
            _0x26359c._currentRequest.removeListener("socket", _0x3d13f9);
          }
        }
        if (_0x41a250) {
          this.on("timeout", _0x41a250);
        }
        if (this.socket) {
          _0x3d13f9(this.socket);
        } else {
          this._currentRequest.once('socket', _0x3d13f9);
        }
        this.on('socket', _0x4a3b23);
        this.on('abort', _0x2ddadd);
        this.on("error", _0x2ddadd);
        this.on("response", _0x2ddadd);
        this.on('close', _0x2ddadd);
        return this;
      };
      ["flushHeaders", 'getHeader', "setNoDelay", "setSocketKeepAlive"].forEach(function (_0x37efc6) {
        _0x5b96ef.prototype[_0x37efc6] = function (_0x1a6bca, _0x1f11b6) {
          return this._currentRequest[_0x37efc6](_0x1a6bca, _0x1f11b6);
        };
      });
      ['aborted', "connection", "socket"].forEach(function (_0x4af0e1) {
        Object.defineProperty(_0x5b96ef.prototype, _0x4af0e1, {
          'get': function () {
            return this._currentRequest[_0x4af0e1];
          }
        });
      });
      _0x5b96ef.prototype._sanitizeOptions = function (_0x579258) {
        if (!_0x579258.headers) {
          _0x579258.headers = {};
        }
        if (_0x579258.host) {
          if (!_0x579258.hostname) {
            _0x579258.hostname = _0x579258.host;
          }
          delete _0x579258.host;
        }
        if (!_0x579258.pathname && _0x579258.path) {
          var _0xa157ee = _0x579258.path.indexOf('?');
          if (_0xa157ee < 0x0) {
            _0x579258.pathname = _0x579258.path;
          } else {
            _0x579258.pathname = _0x579258.path.substring(0x0, _0xa157ee);
            _0x579258.search = _0x579258.path.substring(_0xa157ee);
          }
        }
      };
      _0x5b96ef.prototype._performRequest = function () {
        var _0x123c1b = this._options.protocol;
        var _0x5c4d3b = this._options.nativeProtocols[_0x123c1b];
        if (!_0x5c4d3b) {
          throw new TypeError("Unsupported protocol " + _0x123c1b);
        }
        if (this._options.agents) {
          var _0x47fa6f = _0x123c1b.slice(0x0, -0x1);
          this._options.agent = this._options.agents[_0x47fa6f];
        }
        var _0xa5f027 = this._currentRequest = _0x5c4d3b.request(this._options, this._onNativeResponse);
        _0xa5f027._redirectable = this;
        for (var _0x1659e5 of _0x24b286) _0xa5f027.on(_0x1659e5, _0x57ec88[_0x1659e5]);
        this._currentUrl = /^\//.test(this._options.path) ? _0x158b39.format(this._options) : this._options.path;
        if (this._isRedirect) {
          var _0x16de3b = 0x0;
          var _0x126d1d = this;
          var _0x4ff2a9 = this._requestBodyBuffers;
          !function _0x334dde(_0x5caff1) {
            if (_0xa5f027 === _0x126d1d._currentRequest) {
              if (_0x5caff1) {
                _0x126d1d.emit("error", _0x5caff1);
              } else {
                if (_0x16de3b < _0x4ff2a9.length) {
                  var _0x46e152 = _0x4ff2a9[_0x16de3b++];
                  if (!_0xa5f027.finished) {
                    _0xa5f027.write(_0x46e152.data, _0x46e152.encoding, _0x334dde);
                  }
                } else if (_0x126d1d._ended) {
                  _0xa5f027.end();
                }
              }
            }
          }();
        }
      };
      _0x5b96ef.prototype._processResponse = function (_0xe88a65) {
        var _0x215ebf = _0xe88a65.statusCode;
        if (this._options.trackRedirects) {
          this._redirects.push({
            'url': this._currentUrl,
            'headers': _0xe88a65.headers,
            'statusCode': _0x215ebf
          });
        }
        var _0x2c6b21;
        var _0x23c14f = _0xe88a65.headers.location;
        if (!_0x23c14f || false === this._options.followRedirects || _0x215ebf < 0x12c || _0x215ebf >= 0x190) {
          _0xe88a65.responseUrl = this._currentUrl;
          _0xe88a65.redirects = this._redirects;
          this.emit("response", _0xe88a65);
          return void (this._requestBodyBuffers = []);
        }
        _0x1b2757(this._currentRequest);
        _0xe88a65.destroy();
        if (++this._redirectCount > this._options.maxRedirects) {
          throw new _0x4af7cd();
        }
        var _0x257561 = this._options.beforeRedirect;
        if (_0x257561) {
          _0x2c6b21 = Object.assign({
            'Host': _0xe88a65.req.getHeader("host")
          }, this._options.headers);
        }
        var _0x2645d5 = this._options.method;
        if ((0x12d === _0x215ebf || 0x12e === _0x215ebf) && "POST" === this._options.method || 0x12f === _0x215ebf && !/^(?:GET|HEAD)$/.test(this._options.method)) {
          this._options.method = "GET";
          this._requestBodyBuffers = [];
          _0x169648(/^content-/i, this._options.headers);
        }
        var _0x35c53d = _0x169648(/^host$/i, this._options.headers);
        var _0x2c4903 = _0x4303b2(this._currentUrl);
        var _0x49a1b1 = _0x35c53d || _0x2c4903.host;
        var _0x33bc98 = /^\w+:/.test(_0x23c14f) ? this._currentUrl : _0x158b39.format(Object.assign(_0x2c4903, {
          'host': _0x49a1b1
        }));
        var _0x47c822 = _0x3c4aa3 ? new _0x8305fe(_0x23c14f, _0x33bc98) : _0x4303b2(_0x158b39.resolve(_0x33bc98, _0x23c14f));
        _0x5c2103("redirecting to", _0x47c822.href);
        this._isRedirect = true;
        _0x5997d6(_0x47c822, this._options);
        if (_0x47c822.protocol !== _0x2c4903.protocol && "https:" !== _0x47c822.protocol || _0x47c822.host !== _0x49a1b1 && !function (_0x45d87e, _0x4ab549) {
          _0x2e61fd(("string" == typeof _0x45d87e || _0x45d87e instanceof String) && ("string" == typeof _0x4ab549 || _0x4ab549 instanceof String));
          var _0x230595 = _0x45d87e.length - _0x4ab549.length - 0x1;
          return _0x230595 > 0x0 && '.' === _0x45d87e[_0x230595] && _0x45d87e.endsWith(_0x4ab549);
        }(_0x47c822.host, _0x49a1b1)) {
          _0x169648(/^(?:(?:proxy-)?authorization|cookie)$/i, this._options.headers);
        }
        if ("function" == typeof _0x257561) {
          var _0x13bd88 = {
            'headers': _0xe88a65.headers,
            'statusCode': _0x215ebf
          };
          var _0x49788c = {
            'url': _0x33bc98,
            'method': _0x2645d5,
            'headers': _0x2c6b21
          };
          _0x257561(this._options, _0x13bd88, _0x49788c);
          this._sanitizeOptions(this._options);
        }
        this._performRequest();
      };
      _0x199c45.exports = _0x5c35ab({
        'http': _0x1cea77,
        'https': _0x1532e5
      });
      _0x199c45.exports.wrap = _0x5c35ab;
    },
    0xc6c: (_0x307cc2, _0xb5d052, _0x534c6) => {
      'use strict';

      const _0x7ac252 = _0x534c6(0xe97);
      const _0x1a63c1 = _0x534c6(0x1b10);
      const _0xcee968 = _0x534c6(0x815).invalidWin32Path;
      const _0x26d3ef = parseInt("0777", 0x8);
      _0x307cc2.exports = function _0x3988c7(_0x493c86, _0x1301e0, _0x5deedc, _0xcf3150) {
        if ("function" == typeof _0x1301e0) {
          _0x5deedc = _0x1301e0;
          _0x1301e0 = {};
        } else if (!(_0x1301e0 && "object" == typeof _0x1301e0)) {
          _0x1301e0 = {
            'mode': _0x1301e0
          };
        }
        if ("win32" === process.platform && _0xcee968(_0x493c86)) {
          const _0x3bc900 = new Error(_0x493c86 + " contains invalid WIN32 path characters.");
          _0x3bc900.code = "EINVAL";
          return _0x5deedc(_0x3bc900);
        }
        let _0x25c3e0 = _0x1301e0.mode;
        const _0x2b4ccd = _0x1301e0.fs || _0x7ac252;
        if (undefined === _0x25c3e0) {
          _0x25c3e0 = _0x26d3ef & ~process.umask();
        }
        if (!_0xcf3150) {
          _0xcf3150 = null;
        }
        _0x5deedc = _0x5deedc || function () {};
        _0x493c86 = _0x1a63c1.resolve(_0x493c86);
        _0x2b4ccd.mkdir(_0x493c86, _0x25c3e0, _0x5bc33f => {
          if (!_0x5bc33f) {
            return _0x5deedc(null, _0xcf3150 = _0xcf3150 || _0x493c86);
          }
          if ("ENOENT" === _0x5bc33f.code) {
            if (_0x1a63c1.dirname(_0x493c86) === _0x493c86) {
              return _0x5deedc(_0x5bc33f);
            }
            _0x3988c7(_0x1a63c1.dirname(_0x493c86), _0x1301e0, (_0x430cca, _0x495a23) => {
              if (_0x430cca) {
                _0x5deedc(_0x430cca, _0x495a23);
              } else {
                _0x3988c7(_0x493c86, _0x1301e0, _0x5deedc, _0x495a23);
              }
            });
          } else {
            _0x2b4ccd.stat(_0x493c86, (_0x346194, _0x33bf51) => {
              if (_0x346194 || !_0x33bf51.isDirectory()) {
                _0x5deedc(_0x5bc33f, _0xcf3150);
              } else {
                _0x5deedc(null, _0xcf3150);
              }
            });
          }
        });
      };
    },
    0xc77: (_0x52d76d, _0x5712bd, _0x5a2f5c) => {
      'use strict';

      var _0x4fc34c = _0x5a2f5c(0x788);
      function _0x43410f(_0x3928e1) {
        if ("function" != typeof _0x3928e1) {
          throw new TypeError("executor must be a function.");
        }
        var _0x3fd6f9;
        this.promise = new Promise(function (_0x3dc19d) {
          _0x3fd6f9 = _0x3dc19d;
        });
        var _0x4a8048 = this;
        this.promise.then(function (_0x351f65) {
          if (_0x4a8048._listeners) {
            var _0xfb31cb;
            var _0x516069 = _0x4a8048._listeners.length;
            for (_0xfb31cb = 0x0; _0xfb31cb < _0x516069; _0xfb31cb++) {
              _0x4a8048._listeners[_0xfb31cb](_0x351f65);
            }
            _0x4a8048._listeners = null;
          }
        });
        this.promise.then = function (_0xf5972b) {
          var _0x1b61ab;
          var _0x1b1899 = new Promise(function (_0x16d59c) {
            _0x4a8048.subscribe(_0x16d59c);
            _0x1b61ab = _0x16d59c;
          }).then(_0xf5972b);
          _0x1b1899.cancel = function () {
            _0x4a8048.unsubscribe(_0x1b61ab);
          };
          return _0x1b1899;
        };
        _0x3928e1(function (_0x26710a) {
          if (!_0x4a8048.reason) {
            _0x4a8048.reason = new _0x4fc34c(_0x26710a);
            _0x3fd6f9(_0x4a8048.reason);
          }
        });
      }
      _0x43410f.prototype.throwIfRequested = function () {
        if (this.reason) {
          throw this.reason;
        }
      };
      _0x43410f.prototype.subscribe = function (_0x56515c) {
        if (this.reason) {
          _0x56515c(this.reason);
        } else if (this._listeners) {
          this._listeners.push(_0x56515c);
        } else {
          this._listeners = [_0x56515c];
        }
      };
      _0x43410f.prototype.unsubscribe = function (_0x128f05) {
        if (this._listeners) {
          var _0xaf52ba = this._listeners.indexOf(_0x128f05);
          if (-0x1 !== _0xaf52ba) {
            this._listeners.splice(_0xaf52ba, 0x1);
          }
        }
      };
      _0x43410f.source = function () {
        var _0x581966;
        return {
          'token': new _0x43410f(function (_0x416e91) {
            _0x581966 = _0x416e91;
          }),
          'cancel': _0x581966
        };
      };
      _0x52d76d.exports = _0x43410f;
    },
    0xcf2: (_0x5f2b9a, _0x3c4894, _0x8a9606) => {
      'use strict';

      const _0x5d85f6 = _0x8a9606(0xe97);
      const _0x33553f = _0x8a9606(0x1b10);
      const _0x3e4502 = _0x8a9606(0x1147).copy;
      const _0x27e0b5 = _0x8a9606(0x152e).remove;
      const _0x4813fc = _0x8a9606(0xed6).mkdirp;
      const _0x31ebed = _0x8a9606(0x2448).pathExists;
      const _0x2f5a46 = _0x8a9606(0x193e);
      function _0x219252(_0x227427, _0x32a97b, _0x5e8ed4, _0x351810) {
        _0x5d85f6.rename(_0x227427, _0x32a97b, _0x31a95f => _0x31a95f ? "EXDEV" !== _0x31a95f.code ? _0x351810(_0x31a95f) : function (_0x1c8d16, _0x1c8f46, _0x2b9456, _0x49c2e8) {
          _0x3e4502(_0x1c8d16, _0x1c8f46, {
            'overwrite': _0x2b9456,
            'errorOnExist': true
          }, _0x2c8073 => _0x2c8073 ? _0x49c2e8(_0x2c8073) : _0x27e0b5(_0x1c8d16, _0x49c2e8));
        }(_0x227427, _0x32a97b, _0x5e8ed4, _0x351810) : _0x351810());
      }
      _0x5f2b9a.exports = function (_0x4b14ec, _0x434a60, _0x482518, _0x16dd84) {
        if ('function' == typeof _0x482518) {
          _0x16dd84 = _0x482518;
          _0x482518 = {};
        }
        const _0x568f97 = _0x482518.overwrite || _0x482518.clobber || false;
        _0x2f5a46.checkPaths(_0x4b14ec, _0x434a60, "move", (_0x3be4fa, _0x2afd18) => {
          if (_0x3be4fa) {
            return _0x16dd84(_0x3be4fa);
          }
          const {
            srcStat: _0x9295a
          } = _0x2afd18;
          _0x2f5a46.checkParentPaths(_0x4b14ec, _0x9295a, _0x434a60, "move", _0x4fea90 => {
            if (_0x4fea90) {
              return _0x16dd84(_0x4fea90);
            }
            _0x4813fc(_0x33553f.dirname(_0x434a60), _0x4070b0 => _0x4070b0 ? _0x16dd84(_0x4070b0) : function (_0x33270c, _0x24d106, _0x2d1018, _0x813d61) {
              if (_0x2d1018) {
                return _0x27e0b5(_0x24d106, _0x3696fb => _0x3696fb ? _0x813d61(_0x3696fb) : _0x219252(_0x33270c, _0x24d106, _0x2d1018, _0x813d61));
              }
              _0x31ebed(_0x24d106, (_0x4aad32, _0x35dfa4) => _0x4aad32 ? _0x813d61(_0x4aad32) : _0x35dfa4 ? _0x813d61(new Error("dest already exists.")) : _0x219252(_0x33270c, _0x24d106, _0x2d1018, _0x813d61));
            }(_0x4b14ec, _0x434a60, _0x568f97, _0x16dd84));
          });
        });
      };
    },
    0xd30: (_0x55d77f, _0x4ef2c8, _0xb458e0) => {
      'use strict';

      const _0xb8f708 = _0xb458e0(0x4d4).S;
      const _0x330fd7 = _0xb458e0(0xb76);
      _0x330fd7.outputJson = _0xb8f708(_0xb458e0(0x191a));
      _0x330fd7.outputJsonSync = _0xb458e0(0x88c);
      _0x330fd7.outputJSON = _0x330fd7.outputJson;
      _0x330fd7.outputJSONSync = _0x330fd7.outputJsonSync;
      _0x330fd7.writeJSON = _0x330fd7.writeJson;
      _0x330fd7.writeJSONSync = _0x330fd7.writeJsonSync;
      _0x330fd7.readJSON = _0x330fd7.readJson;
      _0x330fd7.readJSONSync = _0x330fd7.readJsonSync;
      _0x55d77f.exports = _0x330fd7;
    },
    0xd62: (_0x14d7b1, _0x1f3e89, _0x40c74f) => {
      'use strict';

      var _0x273b07 = _0x40c74f(0x8b8);
      var _0x4e5ae8 = _0x40c74f(0xdde);
      var _0x3c0ce7 = _0x40c74f(0xb01);
      var _0xdd90cb = _0x40c74f(0x2ca);
      var _0x43c933 = _0x40c74f(0x1526);
      var _0x1227d6 = _0x40c74f(0x20f9);
      var _0x30938b = _0x40c74f(0x1ff0);
      var _0x122198 = _0x40c74f(0x1163);
      var _0x3d247a = _0x40c74f(0xc55);
      var _0x278391 = _0x40c74f(0xa88);
      var _0x12e644 = _0x40c74f(0x1c2e);
      var _0x4e191b = _0x40c74f(0x1800);
      var _0x562aea = _0x40c74f(0x17bc);
      var _0x5b0f79 = _0x40c74f(0xa61);
      var _0x421f2e = _0x40c74f(0x1e35);
      var _0x2e25e4 = _0x40c74f(0x1636);
      var _0x4d14ed = _0x40c74f(0x107e);
      var _0x385fac = _0x40c74f(0x1a9d);
      var _0x11597c = _0x385fac.allocBuffer(0x1100000);
      var _0x10dcdf = function () {};
      _0x10dcdf.prototype.serialize = function (_0x4d2255, _0x51f872) {
        var _0x32c2f8 = "boolean" == typeof (_0x51f872 = _0x51f872 || {}).checkKeys && _0x51f872.checkKeys;
        var _0xb8d29c = "boolean" == typeof _0x51f872.serializeFunctions && _0x51f872.serializeFunctions;
        var _0x4d6640 = "boolean" != typeof _0x51f872.ignoreUndefined || _0x51f872.ignoreUndefined;
        var _0x2413a2 = "number" == typeof _0x51f872.minInternalBufferSize ? _0x51f872.minInternalBufferSize : 0x1100000;
        if (_0x11597c.length < _0x2413a2) {
          _0x11597c = _0x385fac.allocBuffer(_0x2413a2);
        }
        var _0x346323 = _0x2e25e4(_0x11597c, _0x4d2255, _0x32c2f8, 0x0, 0x0, _0xb8d29c, _0x4d6640, []);
        var _0x161e21 = _0x385fac.allocBuffer(_0x346323);
        _0x11597c.copy(_0x161e21, 0x0, 0x0, _0x161e21.length);
        return _0x161e21;
      };
      _0x10dcdf.prototype.serializeWithBufferAndIndex = function (_0x154a1d, _0x3cd81b, _0x3a6c61) {
        var _0x4b12f7 = "boolean" == typeof (_0x3a6c61 = _0x3a6c61 || {}).checkKeys && _0x3a6c61.checkKeys;
        var _0x29c918 = "boolean" == typeof _0x3a6c61.serializeFunctions && _0x3a6c61.serializeFunctions;
        var _0xc596d9 = "boolean" != typeof _0x3a6c61.ignoreUndefined || _0x3a6c61.ignoreUndefined;
        var _0xc20c33 = "number" == typeof _0x3a6c61.index ? _0x3a6c61.index : 0x0;
        return _0x2e25e4(_0x3cd81b, _0x154a1d, _0x4b12f7, _0xc20c33 || 0x0, 0x0, _0x29c918, _0xc596d9) - 0x1;
      };
      _0x10dcdf.prototype.deserialize = function (_0x5e3dbf, _0x56c8f7) {
        return _0x421f2e(_0x5e3dbf, _0x56c8f7);
      };
      _0x10dcdf.prototype.calculateObjectSize = function (_0x58e5b1, _0x78abde) {
        var _0x151b78 = "boolean" == typeof (_0x78abde = _0x78abde || {}).serializeFunctions && _0x78abde.serializeFunctions;
        var _0x2a2d7a = "boolean" != typeof _0x78abde.ignoreUndefined || _0x78abde.ignoreUndefined;
        return _0x4d14ed(_0x58e5b1, _0x151b78, _0x2a2d7a);
      };
      _0x10dcdf.prototype.deserializeStream = function (_0x433157, _0x4e8a16, _0x5b3352, _0x1c77d6, _0x1d316c, _0x5a5940) {
        _0x5a5940 = null != _0x5a5940 ? _0x5a5940 : {};
        var _0x3c6812 = _0x4e8a16;
        for (var _0x53316c = 0x0; _0x53316c < _0x5b3352; _0x53316c++) {
          var _0xf686f4 = _0x433157[_0x3c6812] | _0x433157[_0x3c6812 + 0x1] << 0x8 | _0x433157[_0x3c6812 + 0x2] << 0x10 | _0x433157[_0x3c6812 + 0x3] << 0x18;
          _0x5a5940.index = _0x3c6812;
          _0x1c77d6[_0x1d316c + _0x53316c] = this.deserialize(_0x433157, _0x5a5940);
          _0x3c6812 += _0xf686f4;
        }
        return _0x3c6812;
      };
      _0x10dcdf.BSON_INT32_MAX = 0x7fffffff;
      _0x10dcdf.BSON_INT32_MIN = -0x80000000;
      _0x10dcdf.BSON_INT64_MAX = Math.pow(0x2, 0x3f) - 0x1;
      _0x10dcdf.BSON_INT64_MIN = -Math.pow(0x2, 0x3f);
      _0x10dcdf.JS_INT_MAX = 0x20000000000000;
      _0x10dcdf.JS_INT_MIN = -0x20000000000000;
      _0x10dcdf.BSON_DATA_NUMBER = 0x1;
      _0x10dcdf.BSON_DATA_STRING = 0x2;
      _0x10dcdf.BSON_DATA_OBJECT = 0x3;
      _0x10dcdf.BSON_DATA_ARRAY = 0x4;
      _0x10dcdf.BSON_DATA_BINARY = 0x5;
      _0x10dcdf.BSON_DATA_OID = 0x7;
      _0x10dcdf.BSON_DATA_BOOLEAN = 0x8;
      _0x10dcdf.BSON_DATA_DATE = 0x9;
      _0x10dcdf.BSON_DATA_NULL = 0xa;
      _0x10dcdf.BSON_DATA_REGEXP = 0xb;
      _0x10dcdf.BSON_DATA_CODE = 0xd;
      _0x10dcdf.BSON_DATA_SYMBOL = 0xe;
      _0x10dcdf.BSON_DATA_CODE_W_SCOPE = 0xf;
      _0x10dcdf.BSON_DATA_INT = 0x10;
      _0x10dcdf.BSON_DATA_TIMESTAMP = 0x11;
      _0x10dcdf.BSON_DATA_LONG = 0x12;
      _0x10dcdf.BSON_DATA_MIN_KEY = 0xff;
      _0x10dcdf.BSON_DATA_MAX_KEY = 0x7f;
      _0x10dcdf.BSON_BINARY_SUBTYPE_DEFAULT = 0x0;
      _0x10dcdf.BSON_BINARY_SUBTYPE_FUNCTION = 0x1;
      _0x10dcdf.BSON_BINARY_SUBTYPE_BYTE_ARRAY = 0x2;
      _0x10dcdf.BSON_BINARY_SUBTYPE_UUID = 0x3;
      _0x10dcdf.BSON_BINARY_SUBTYPE_MD5 = 0x4;
      _0x10dcdf.BSON_BINARY_SUBTYPE_USER_DEFINED = 0x80;
      _0x14d7b1.exports = _0x10dcdf;
      _0x14d7b1.exports.Code = _0x3d247a;
      _0x14d7b1.exports.Map = _0x273b07;
      _0x14d7b1.exports.Symbol = _0x30938b;
      _0x14d7b1.exports.BSON = _0x10dcdf;
      _0x14d7b1.exports.DBRef = _0x562aea;
      _0x14d7b1.exports.Binary = _0x5b0f79;
      _0x14d7b1.exports.ObjectID = _0x43c933;
      _0x14d7b1.exports.Long = _0x4e5ae8;
      _0x14d7b1.exports.Timestamp = _0xdd90cb;
      _0x14d7b1.exports.Double = _0x3c0ce7;
      _0x14d7b1.exports.Int32 = _0x122198;
      _0x14d7b1.exports.MinKey = _0x12e644;
      _0x14d7b1.exports.MaxKey = _0x4e191b;
      _0x14d7b1.exports.BSONRegExp = _0x1227d6;
      _0x14d7b1.exports.Decimal128 = _0x278391;
    },
    0xd8f: (_0x6031dc, _0x1d573e, _0xbda6a4) => {
      'use strict';

      var _0x10dd85 = _0xbda6a4(0x252c);
      function _0x76c5dc() {
        this.handlers = [];
      }
      _0x76c5dc.prototype.use = function (_0x1534fb, _0x4f2c87, _0x4eb098) {
        this.handlers.push({
          'fulfilled': _0x1534fb,
          'rejected': _0x4f2c87,
          'synchronous': !!_0x4eb098 && _0x4eb098.synchronous,
          'runWhen': _0x4eb098 ? _0x4eb098.runWhen : null
        });
        return this.handlers.length - 0x1;
      };
      _0x76c5dc.prototype.eject = function (_0x4e6797) {
        if (this.handlers[_0x4e6797]) {
          this.handlers[_0x4e6797] = null;
        }
      };
      _0x76c5dc.prototype.forEach = function (_0x5398bf) {
        _0x10dd85.forEach(this.handlers, function (_0x3fceb2) {
          if (null !== _0x3fceb2) {
            _0x5398bf(_0x3fceb2);
          }
        });
      };
      _0x6031dc.exports = _0x76c5dc;
    },
    0xdde: _0xaf0d89 => {
      function _0x4aa6a4(_0x5ec840, _0x4972b3) {
        if (!(this instanceof _0x4aa6a4)) {
          return new _0x4aa6a4(_0x5ec840, _0x4972b3);
        }
        this._bsontype = 'Long';
        this.low_ = 0x0 | _0x5ec840;
        this.high_ = 0x0 | _0x4972b3;
      }
      _0x4aa6a4.prototype.toInt = function () {
        return this.low_;
      };
      _0x4aa6a4.prototype.toNumber = function () {
        return this.high_ * _0x4aa6a4.TWO_PWR_32_DBL_ + this.getLowBitsUnsigned();
      };
      _0x4aa6a4.prototype.toBigInt = function () {
        return BigInt(this.toString());
      };
      _0x4aa6a4.prototype.toJSON = function () {
        return this.toString();
      };
      _0x4aa6a4.prototype.toString = function (_0xd2d8d2) {
        var _0x58b75b = _0xd2d8d2 || 0xa;
        if (_0x58b75b < 0x2 || 0x24 < _0x58b75b) {
          throw Error("radix out of range: " + _0x58b75b);
        }
        if (this.isZero()) {
          return '0';
        }
        if (this.isNegative()) {
          if (this.equals(_0x4aa6a4.MIN_VALUE)) {
            var _0xad420 = _0x4aa6a4.fromNumber(_0x58b75b);
            var _0x58a4ea = this.div(_0xad420);
            var _0x4d1b47 = _0x58a4ea.multiply(_0xad420).subtract(this);
            return _0x58a4ea.toString(_0x58b75b) + _0x4d1b47.toInt().toString(_0x58b75b);
          }
          return '-' + this.negate().toString(_0x58b75b);
        }
        var _0x4dbe5d = _0x4aa6a4.fromNumber(Math.pow(_0x58b75b, 0x6));
        _0x4d1b47 = this;
        for (var _0x47e6ac = ''; !_0x4d1b47.isZero();) {
          var _0x62964f = _0x4d1b47.div(_0x4dbe5d);
          var _0xa03b77 = _0x4d1b47.subtract(_0x62964f.multiply(_0x4dbe5d)).toInt().toString(_0x58b75b);
          if ((_0x4d1b47 = _0x62964f).isZero()) {
            return _0xa03b77 + _0x47e6ac;
          }
          for (; _0xa03b77.length < 0x6;) {
            _0xa03b77 = '0' + _0xa03b77;
          }
          _0x47e6ac = '' + _0xa03b77 + _0x47e6ac;
        }
      };
      _0x4aa6a4.prototype.getHighBits = function () {
        return this.high_;
      };
      _0x4aa6a4.prototype.getLowBits = function () {
        return this.low_;
      };
      _0x4aa6a4.prototype.getLowBitsUnsigned = function () {
        return this.low_ >= 0x0 ? this.low_ : _0x4aa6a4.TWO_PWR_32_DBL_ + this.low_;
      };
      _0x4aa6a4.prototype.getNumBitsAbs = function () {
        if (this.isNegative()) {
          return this.equals(_0x4aa6a4.MIN_VALUE) ? 0x40 : this.negate().getNumBitsAbs();
        }
        var _0x3a35d6 = 0x0 !== this.high_ ? this.high_ : this.low_;
        for (var _0x49812d = 0x1f; _0x49812d > 0x0 && !(_0x3a35d6 & 0x1 << _0x49812d); _0x49812d--) {
          ;
        }
        return 0x0 !== this.high_ ? _0x49812d + 0x21 : _0x49812d + 0x1;
      };
      _0x4aa6a4.prototype.isZero = function () {
        return 0x0 === this.high_ && 0x0 === this.low_;
      };
      _0x4aa6a4.prototype.isNegative = function () {
        return this.high_ < 0x0;
      };
      _0x4aa6a4.prototype.isOdd = function () {
        return !(0x1 & ~this.low_);
      };
      _0x4aa6a4.prototype.equals = function (_0x1ffbb4) {
        return this.high_ === _0x1ffbb4.high_ && this.low_ === _0x1ffbb4.low_;
      };
      _0x4aa6a4.prototype.notEquals = function (_0x2962f9) {
        return this.high_ !== _0x2962f9.high_ || this.low_ !== _0x2962f9.low_;
      };
      _0x4aa6a4.prototype.lessThan = function (_0x2c5b46) {
        return this.compare(_0x2c5b46) < 0x0;
      };
      _0x4aa6a4.prototype.lessThanOrEqual = function (_0x2eac36) {
        return this.compare(_0x2eac36) <= 0x0;
      };
      _0x4aa6a4.prototype.greaterThan = function (_0x24a484) {
        return this.compare(_0x24a484) > 0x0;
      };
      _0x4aa6a4.prototype.greaterThanOrEqual = function (_0xe33acb) {
        return this.compare(_0xe33acb) >= 0x0;
      };
      _0x4aa6a4.prototype.compare = function (_0x4e6824) {
        if (this.equals(_0x4e6824)) {
          return 0x0;
        }
        var _0x139ef1 = this.isNegative();
        var _0x23d324 = _0x4e6824.isNegative();
        return _0x139ef1 && !_0x23d324 ? -0x1 : !_0x139ef1 && _0x23d324 ? 0x1 : this.subtract(_0x4e6824).isNegative() ? -0x1 : 0x1;
      };
      _0x4aa6a4.prototype.negate = function () {
        return this.equals(_0x4aa6a4.MIN_VALUE) ? _0x4aa6a4.MIN_VALUE : this.not().add(_0x4aa6a4.ONE);
      };
      _0x4aa6a4.prototype.add = function (_0x1a1340) {
        var _0x7474b4 = this.high_ >>> 0x10;
        var _0x117739 = 0xffff & this.high_;
        var _0x3d9994 = this.low_ >>> 0x10;
        var _0xa57f4 = 0xffff & this.low_;
        var _0x788dee = _0x1a1340.high_ >>> 0x10;
        var _0x165c99 = 0xffff & _0x1a1340.high_;
        var _0x51d1c1 = _0x1a1340.low_ >>> 0x10;
        var _0x45e4ca = 0x0;
        var _0x40f0a9 = 0x0;
        var _0x4c7532 = 0x0;
        var _0x35dffc = 0x0;
        _0x4c7532 += (_0x35dffc += _0xa57f4 + (0xffff & _0x1a1340.low_)) >>> 0x10;
        _0x35dffc &= 0xffff;
        _0x40f0a9 += (_0x4c7532 += _0x3d9994 + _0x51d1c1) >>> 0x10;
        _0x4c7532 &= 0xffff;
        _0x45e4ca += (_0x40f0a9 += _0x117739 + _0x165c99) >>> 0x10;
        _0x40f0a9 &= 0xffff;
        _0x45e4ca += _0x7474b4 + _0x788dee;
        _0x45e4ca &= 0xffff;
        return _0x4aa6a4.fromBits(_0x4c7532 << 0x10 | _0x35dffc, _0x45e4ca << 0x10 | _0x40f0a9);
      };
      _0x4aa6a4.prototype.subtract = function (_0x329e75) {
        return this.add(_0x329e75.negate());
      };
      _0x4aa6a4.prototype.multiply = function (_0x2fa51e) {
        if (this.isZero()) {
          return _0x4aa6a4.ZERO;
        }
        if (_0x2fa51e.isZero()) {
          return _0x4aa6a4.ZERO;
        }
        if (this.equals(_0x4aa6a4.MIN_VALUE)) {
          return _0x2fa51e.isOdd() ? _0x4aa6a4.MIN_VALUE : _0x4aa6a4.ZERO;
        }
        if (_0x2fa51e.equals(_0x4aa6a4.MIN_VALUE)) {
          return this.isOdd() ? _0x4aa6a4.MIN_VALUE : _0x4aa6a4.ZERO;
        }
        if (this.isNegative()) {
          return _0x2fa51e.isNegative() ? this.negate().multiply(_0x2fa51e.negate()) : this.negate().multiply(_0x2fa51e).negate();
        }
        if (_0x2fa51e.isNegative()) {
          return this.multiply(_0x2fa51e.negate()).negate();
        }
        if (this.lessThan(_0x4aa6a4.TWO_PWR_24_) && _0x2fa51e.lessThan(_0x4aa6a4.TWO_PWR_24_)) {
          return _0x4aa6a4.fromNumber(this.toNumber() * _0x2fa51e.toNumber());
        }
        var _0x3bf6f7 = this.high_ >>> 0x10;
        var _0x195a20 = 0xffff & this.high_;
        var _0x8bc152 = this.low_ >>> 0x10;
        var _0x7f014f = 0xffff & this.low_;
        var _0xf0702f = _0x2fa51e.high_ >>> 0x10;
        var _0x923c9b = 0xffff & _0x2fa51e.high_;
        var _0x13e50e = _0x2fa51e.low_ >>> 0x10;
        var _0x147cbf = 0xffff & _0x2fa51e.low_;
        var _0xe77b1f = 0x0;
        var _0x405d5e = 0x0;
        var _0x652fce = 0x0;
        var _0x4a12af = 0x0;
        _0x652fce += (_0x4a12af += _0x7f014f * _0x147cbf) >>> 0x10;
        _0x4a12af &= 0xffff;
        _0x405d5e += (_0x652fce += _0x8bc152 * _0x147cbf) >>> 0x10;
        _0x652fce &= 0xffff;
        _0x405d5e += (_0x652fce += _0x7f014f * _0x13e50e) >>> 0x10;
        _0x652fce &= 0xffff;
        _0xe77b1f += (_0x405d5e += _0x195a20 * _0x147cbf) >>> 0x10;
        _0x405d5e &= 0xffff;
        _0xe77b1f += (_0x405d5e += _0x8bc152 * _0x13e50e) >>> 0x10;
        _0x405d5e &= 0xffff;
        _0xe77b1f += (_0x405d5e += _0x7f014f * _0x923c9b) >>> 0x10;
        _0x405d5e &= 0xffff;
        _0xe77b1f += _0x3bf6f7 * _0x147cbf + _0x195a20 * _0x13e50e + _0x8bc152 * _0x923c9b + _0x7f014f * _0xf0702f;
        _0xe77b1f &= 0xffff;
        return _0x4aa6a4.fromBits(_0x652fce << 0x10 | _0x4a12af, _0xe77b1f << 0x10 | _0x405d5e);
      };
      _0x4aa6a4.prototype.div = function (_0x4a4922) {
        if (_0x4a4922.isZero()) {
          throw Error("division by zero");
        }
        if (this.isZero()) {
          return _0x4aa6a4.ZERO;
        }
        if (this.equals(_0x4aa6a4.MIN_VALUE)) {
          if (_0x4a4922.equals(_0x4aa6a4.ONE) || _0x4a4922.equals(_0x4aa6a4.NEG_ONE)) {
            return _0x4aa6a4.MIN_VALUE;
          }
          if (_0x4a4922.equals(_0x4aa6a4.MIN_VALUE)) {
            return _0x4aa6a4.ONE;
          }
          var _0x3ba4d3 = this.shiftRight(0x1).div(_0x4a4922).shiftLeft(0x1);
          if (_0x3ba4d3.equals(_0x4aa6a4.ZERO)) {
            return _0x4a4922.isNegative() ? _0x4aa6a4.ONE : _0x4aa6a4.NEG_ONE;
          }
          var _0x49ff2f = this.subtract(_0x4a4922.multiply(_0x3ba4d3));
          return _0x3ba4d3.add(_0x49ff2f.div(_0x4a4922));
        }
        if (_0x4a4922.equals(_0x4aa6a4.MIN_VALUE)) {
          return _0x4aa6a4.ZERO;
        }
        if (this.isNegative()) {
          return _0x4a4922.isNegative() ? this.negate().div(_0x4a4922.negate()) : this.negate().div(_0x4a4922).negate();
        }
        if (_0x4a4922.isNegative()) {
          return this.div(_0x4a4922.negate()).negate();
        }
        var _0x2ca352 = _0x4aa6a4.ZERO;
        for (_0x49ff2f = this; _0x49ff2f.greaterThanOrEqual(_0x4a4922);) {
          _0x3ba4d3 = Math.max(0x1, Math.floor(_0x49ff2f.toNumber() / _0x4a4922.toNumber()));
          var _0x3194bd = Math.ceil(Math.log(_0x3ba4d3) / Math.LN2);
          var _0x3c38d6 = _0x3194bd <= 0x30 ? 0x1 : Math.pow(0x2, _0x3194bd - 0x30);
          var _0x25f88e = _0x4aa6a4.fromNumber(_0x3ba4d3);
          for (var _0x2d0497 = _0x25f88e.multiply(_0x4a4922); _0x2d0497.isNegative() || _0x2d0497.greaterThan(_0x49ff2f);) {
            _0x3ba4d3 -= _0x3c38d6;
            _0x2d0497 = (_0x25f88e = _0x4aa6a4.fromNumber(_0x3ba4d3)).multiply(_0x4a4922);
          }
          if (_0x25f88e.isZero()) {
            _0x25f88e = _0x4aa6a4.ONE;
          }
          _0x2ca352 = _0x2ca352.add(_0x25f88e);
          _0x49ff2f = _0x49ff2f.subtract(_0x2d0497);
        }
        return _0x2ca352;
      };
      _0x4aa6a4.prototype.modulo = function (_0x417295) {
        return this.subtract(this.div(_0x417295).multiply(_0x417295));
      };
      _0x4aa6a4.prototype.not = function () {
        return _0x4aa6a4.fromBits(~this.low_, ~this.high_);
      };
      _0x4aa6a4.prototype.and = function (_0x281870) {
        return _0x4aa6a4.fromBits(this.low_ & _0x281870.low_, this.high_ & _0x281870.high_);
      };
      _0x4aa6a4.prototype.or = function (_0x1b6c82) {
        return _0x4aa6a4.fromBits(this.low_ | _0x1b6c82.low_, this.high_ | _0x1b6c82.high_);
      };
      _0x4aa6a4.prototype.xor = function (_0x36df14) {
        return _0x4aa6a4.fromBits(this.low_ ^ _0x36df14.low_, this.high_ ^ _0x36df14.high_);
      };
      _0x4aa6a4.prototype.shiftLeft = function (_0x1ee72f) {
        if (0x0 == (_0x1ee72f &= 0x3f)) {
          return this;
        }
        var _0x526b66 = this.low_;
        if (_0x1ee72f < 0x20) {
          var _0x551930 = this.high_;
          return _0x4aa6a4.fromBits(_0x526b66 << _0x1ee72f, _0x551930 << _0x1ee72f | _0x526b66 >>> 0x20 - _0x1ee72f);
        }
        return _0x4aa6a4.fromBits(0x0, _0x526b66 << _0x1ee72f - 0x20);
      };
      _0x4aa6a4.prototype.shiftRight = function (_0x4fb5c8) {
        if (0x0 == (_0x4fb5c8 &= 0x3f)) {
          return this;
        }
        var _0x1720d2 = this.high_;
        if (_0x4fb5c8 < 0x20) {
          var _0x25cb47 = this.low_;
          return _0x4aa6a4.fromBits(_0x25cb47 >>> _0x4fb5c8 | _0x1720d2 << 0x20 - _0x4fb5c8, _0x1720d2 >> _0x4fb5c8);
        }
        return _0x4aa6a4.fromBits(_0x1720d2 >> _0x4fb5c8 - 0x20, _0x1720d2 >= 0x0 ? 0x0 : -0x1);
      };
      _0x4aa6a4.prototype.shiftRightUnsigned = function (_0x46efb7) {
        if (0x0 == (_0x46efb7 &= 0x3f)) {
          return this;
        }
        var _0xb4e64a = this.high_;
        if (_0x46efb7 < 0x20) {
          var _0x17bf14 = this.low_;
          return _0x4aa6a4.fromBits(_0x17bf14 >>> _0x46efb7 | _0xb4e64a << 0x20 - _0x46efb7, _0xb4e64a >>> _0x46efb7);
        }
        return 0x20 === _0x46efb7 ? _0x4aa6a4.fromBits(_0xb4e64a, 0x0) : _0x4aa6a4.fromBits(_0xb4e64a >>> _0x46efb7 - 0x20, 0x0);
      };
      _0x4aa6a4.fromInt = function (_0x1e67f3) {
        if (-0x80 <= _0x1e67f3 && _0x1e67f3 < 0x80) {
          var _0x5768e7 = _0x4aa6a4.INT_CACHE_[_0x1e67f3];
          if (_0x5768e7) {
            return _0x5768e7;
          }
        }
        var _0x477374 = new _0x4aa6a4(0x0 | _0x1e67f3, _0x1e67f3 < 0x0 ? -0x1 : 0x0);
        if (-0x80 <= _0x1e67f3 && _0x1e67f3 < 0x80) {
          _0x4aa6a4.INT_CACHE_[_0x1e67f3] = _0x477374;
        }
        return _0x477374;
      };
      _0x4aa6a4.fromNumber = function (_0x4c586a) {
        return isNaN(_0x4c586a) || !isFinite(_0x4c586a) ? _0x4aa6a4.ZERO : _0x4c586a <= -_0x4aa6a4.TWO_PWR_63_DBL_ ? _0x4aa6a4.MIN_VALUE : _0x4c586a + 0x1 >= _0x4aa6a4.TWO_PWR_63_DBL_ ? _0x4aa6a4.MAX_VALUE : _0x4c586a < 0x0 ? _0x4aa6a4.fromNumber(-_0x4c586a).negate() : new _0x4aa6a4(_0x4c586a % _0x4aa6a4.TWO_PWR_32_DBL_ | 0x0, _0x4c586a / _0x4aa6a4.TWO_PWR_32_DBL_ | 0x0);
      };
      _0x4aa6a4.fromBigInt = function (_0xfe6709) {
        return _0x4aa6a4.fromString(_0xfe6709.toString(0xa), 0xa);
      };
      _0x4aa6a4.fromBits = function (_0x338d59, _0x1ed591) {
        return new _0x4aa6a4(_0x338d59, _0x1ed591);
      };
      _0x4aa6a4.fromString = function (_0x3c109b, _0x5c048f) {
        if (0x0 === _0x3c109b.length) {
          throw Error("number format error: empty string");
        }
        var _0x341667 = _0x5c048f || 0xa;
        if (_0x341667 < 0x2 || 0x24 < _0x341667) {
          throw Error("radix out of range: " + _0x341667);
        }
        if ('-' === _0x3c109b.charAt(0x0)) {
          return _0x4aa6a4.fromString(_0x3c109b.substring(0x1), _0x341667).negate();
        }
        if (_0x3c109b.indexOf('-') >= 0x0) {
          throw Error("number format error: interior \"-\" character: " + _0x3c109b);
        }
        var _0x1e200f = _0x4aa6a4.fromNumber(Math.pow(_0x341667, 0x8));
        var _0x3e6fc3 = _0x4aa6a4.ZERO;
        for (var _0x3c8e5f = 0x0; _0x3c8e5f < _0x3c109b.length; _0x3c8e5f += 0x8) {
          var _0x54fc7f = Math.min(0x8, _0x3c109b.length - _0x3c8e5f);
          var _0x17bfed = parseInt(_0x3c109b.substring(_0x3c8e5f, _0x3c8e5f + _0x54fc7f), _0x341667);
          if (_0x54fc7f < 0x8) {
            var _0x1e78bd = _0x4aa6a4.fromNumber(Math.pow(_0x341667, _0x54fc7f));
            _0x3e6fc3 = _0x3e6fc3.multiply(_0x1e78bd).add(_0x4aa6a4.fromNumber(_0x17bfed));
          } else {
            _0x3e6fc3 = (_0x3e6fc3 = _0x3e6fc3.multiply(_0x1e200f)).add(_0x4aa6a4.fromNumber(_0x17bfed));
          }
        }
        return _0x3e6fc3;
      };
      _0x4aa6a4.INT_CACHE_ = {};
      _0x4aa6a4.TWO_PWR_16_DBL_ = 0x10000;
      _0x4aa6a4.TWO_PWR_24_DBL_ = 16777216;
      _0x4aa6a4.TWO_PWR_32_DBL_ = _0x4aa6a4.TWO_PWR_16_DBL_ * _0x4aa6a4.TWO_PWR_16_DBL_;
      _0x4aa6a4.TWO_PWR_31_DBL_ = _0x4aa6a4.TWO_PWR_32_DBL_ / 0x2;
      _0x4aa6a4.TWO_PWR_48_DBL_ = _0x4aa6a4.TWO_PWR_32_DBL_ * _0x4aa6a4.TWO_PWR_16_DBL_;
      _0x4aa6a4.TWO_PWR_64_DBL_ = _0x4aa6a4.TWO_PWR_32_DBL_ * _0x4aa6a4.TWO_PWR_32_DBL_;
      _0x4aa6a4.TWO_PWR_63_DBL_ = _0x4aa6a4.TWO_PWR_64_DBL_ / 0x2;
      _0x4aa6a4.ZERO = _0x4aa6a4.fromInt(0x0);
      _0x4aa6a4.ONE = _0x4aa6a4.fromInt(0x1);
      _0x4aa6a4.NEG_ONE = _0x4aa6a4.fromInt(-0x1);
      _0x4aa6a4.MAX_VALUE = _0x4aa6a4.fromBits(-0x1, 0x7fffffff);
      _0x4aa6a4.MIN_VALUE = _0x4aa6a4.fromBits(0x0, -0x80000000);
      _0x4aa6a4.TWO_PWR_24_ = _0x4aa6a4.fromInt(16777216);
      _0xaf0d89.exports = _0x4aa6a4;
      _0xaf0d89.exports.Long = _0x4aa6a4;
    },
    0xdfe: (_0x75f48e, _0x34644e, _0x1c89ad) => {
      'use strict';

      const _0xff6e6 = _0x1c89ad(0xe97);
      const _0x2113a5 = _0x1c89ad(0x1b10);
      const _0x3a5486 = _0x1c89ad(0x815).invalidWin32Path;
      const _0x2c86fb = parseInt("0777", 0x8);
      _0x75f48e.exports = function _0x369165(_0x470834, _0xeff3eb, _0x340914) {
        if (!(_0xeff3eb && "object" == typeof _0xeff3eb)) {
          _0xeff3eb = {
            'mode': _0xeff3eb
          };
        }
        let _0x20591e = _0xeff3eb.mode;
        const _0x7e6382 = _0xeff3eb.fs || _0xff6e6;
        if ("win32" === process.platform && _0x3a5486(_0x470834)) {
          const _0x2dd19c = new Error(_0x470834 + " contains invalid WIN32 path characters.");
          _0x2dd19c.code = "EINVAL";
          throw _0x2dd19c;
        }
        if (undefined === _0x20591e) {
          _0x20591e = _0x2c86fb & ~process.umask();
        }
        if (!_0x340914) {
          _0x340914 = null;
        }
        _0x470834 = _0x2113a5.resolve(_0x470834);
        try {
          _0x7e6382.mkdirSync(_0x470834, _0x20591e);
          _0x340914 = _0x340914 || _0x470834;
        } catch (_0x389d70) {
          if ("ENOENT" === _0x389d70.code) {
            if (_0x2113a5.dirname(_0x470834) === _0x470834) {
              throw _0x389d70;
            }
            _0x340914 = _0x369165(_0x2113a5.dirname(_0x470834), _0xeff3eb, _0x340914);
            _0x369165(_0x470834, _0xeff3eb, _0x340914);
          } else {
            let _0x5ccb0d;
            try {
              _0x5ccb0d = _0x7e6382.statSync(_0x470834);
            } catch (_0x19461d) {
              throw _0x389d70;
            }
            if (!_0x5ccb0d.isDirectory()) {
              throw _0x389d70;
            }
          }
        }
        return _0x340914;
      };
    },
    0xe97: (_0x2369fb, _0x2c9c72, _0x122f51) => {
      var _0x340301;
      var _0x2ee2b2;
      var _0x4c323c = _0x122f51(0x26a8);
      var _0x31d0d5 = _0x122f51(0x1a45);
      var _0x3698c5 = _0x122f51(0x7cb);
      var _0x585baa = _0x122f51(0x503);
      var _0x5d3d29 = _0x122f51(0x233f);
      function _0x359677(_0x32b873, _0xb31025) {
        Object.defineProperty(_0x32b873, _0x340301, {
          'get': function () {
            return _0xb31025;
          }
        });
      }
      if ("function" == typeof Symbol && "function" == typeof Symbol["for"]) {
        _0x340301 = Symbol["for"]("graceful-fs.queue");
        _0x2ee2b2 = Symbol["for"]('graceful-fs.previous');
      } else {
        _0x340301 = "___graceful-fs.queue";
        _0x2ee2b2 = "___graceful-fs.previous";
      }
      var _0x1d69c0;
      var _0x440344 = function () {};
      if (_0x5d3d29.debuglog) {
        _0x440344 = _0x5d3d29.debuglog("gfs4");
      } else if (/\bgfs4\b/i.test(process.env.NODE_DEBUG || '')) {
        _0x440344 = function () {
          var _0x27c82d = _0x5d3d29.format.apply(_0x5d3d29, arguments);
          _0x27c82d = "GFS4: " + _0x27c82d.split(/\n/).join("\nGFS4: ");
          console.error(_0x27c82d);
        };
      }
      if (!_0x4c323c[_0x340301]) {
        var _0x2f4837 = global[_0x340301] || [];
        _0x359677(_0x4c323c, _0x2f4837);
        _0x4c323c.close = function (_0x254822) {
          function _0x5df42c(_0x2082e9, _0x4234aa) {
            return _0x254822.call(_0x4c323c, _0x2082e9, function (_0x45990f) {
              if (!_0x45990f) {
                _0xb8c7cc();
              }
              if ("function" == typeof _0x4234aa) {
                _0x4234aa.apply(this, arguments);
              }
            });
          }
          Object.defineProperty(_0x5df42c, _0x2ee2b2, {
            'value': _0x254822
          });
          return _0x5df42c;
        }(_0x4c323c.close);
        _0x4c323c.closeSync = function (_0x24749b) {
          function _0x1a52f4(_0x44fa5d) {
            _0x24749b.apply(_0x4c323c, arguments);
            _0xb8c7cc();
          }
          Object.defineProperty(_0x1a52f4, _0x2ee2b2, {
            'value': _0x24749b
          });
          return _0x1a52f4;
        }(_0x4c323c.closeSync);
        if (/\bgfs4\b/i.test(process.env.NODE_DEBUG || '')) {
          process.on('exit', function () {
            _0x440344(_0x4c323c[_0x340301]);
            _0x122f51(0xa35).equal(_0x4c323c[_0x340301].length, 0x0);
          });
        }
      }
      function _0x20917a(_0x394cf7) {
        _0x31d0d5(_0x394cf7);
        _0x394cf7.gracefulify = _0x20917a;
        _0x394cf7.createReadStream = function (_0x40e0ea, _0xf8bdf8) {
          return new _0x394cf7.ReadStream(_0x40e0ea, _0xf8bdf8);
        };
        _0x394cf7.createWriteStream = function (_0x3fa0a7, _0xba7f0b) {
          return new _0x394cf7.WriteStream(_0x3fa0a7, _0xba7f0b);
        };
        var _0xc64af1 = _0x394cf7.readFile;
        _0x394cf7.readFile = function (_0x1274b4, _0x1a0011, _0x259bd7) {
          if ("function" == typeof _0x1a0011) {
            _0x259bd7 = _0x1a0011;
            _0x1a0011 = null;
          }
          return function _0x1217bb(_0x542485, _0x407004, _0x36517d, _0x5a33a2) {
            return _0xc64af1(_0x542485, _0x407004, function (_0x2dafe9) {
              if (!_0x2dafe9 || "EMFILE" !== _0x2dafe9.code && 'ENFILE' !== _0x2dafe9.code) {
                if ("function" == typeof _0x36517d) {
                  _0x36517d.apply(this, arguments);
                }
              } else {
                _0x1a0e9e([_0x1217bb, [_0x542485, _0x407004, _0x36517d], _0x2dafe9, _0x5a33a2 || Date.now(), Date.now()]);
              }
            });
          }(_0x1274b4, _0x1a0011, _0x259bd7);
        };
        var _0x317005 = _0x394cf7.writeFile;
        _0x394cf7.writeFile = function (_0x1c26a8, _0x45e243, _0x1cb73f, _0x1639b4) {
          if ('function' == typeof _0x1cb73f) {
            _0x1639b4 = _0x1cb73f;
            _0x1cb73f = null;
          }
          return function _0x5da35e(_0x5cf858, _0x5e78ad, _0x1504db, _0x25a0f1, _0x10046d) {
            return _0x317005(_0x5cf858, _0x5e78ad, _0x1504db, function (_0x3a9d1b) {
              if (!_0x3a9d1b || 'EMFILE' !== _0x3a9d1b.code && "ENFILE" !== _0x3a9d1b.code) {
                if ('function' == typeof _0x25a0f1) {
                  _0x25a0f1.apply(this, arguments);
                }
              } else {
                _0x1a0e9e([_0x5da35e, [_0x5cf858, _0x5e78ad, _0x1504db, _0x25a0f1], _0x3a9d1b, _0x10046d || Date.now(), Date.now()]);
              }
            });
          }(_0x1c26a8, _0x45e243, _0x1cb73f, _0x1639b4);
        };
        var _0x3d9e6f = _0x394cf7.appendFile;
        if (_0x3d9e6f) {
          _0x394cf7.appendFile = function (_0x3ec727, _0x19bbeb, _0x20004d, _0x1e0c93) {
            if ("function" == typeof _0x20004d) {
              _0x1e0c93 = _0x20004d;
              _0x20004d = null;
            }
            return function _0x3d7741(_0x77ece8, _0x3f79d3, _0x436107, _0x7730ef, _0x34efc2) {
              return _0x3d9e6f(_0x77ece8, _0x3f79d3, _0x436107, function (_0x2bd3b1) {
                if (!_0x2bd3b1 || "EMFILE" !== _0x2bd3b1.code && "ENFILE" !== _0x2bd3b1.code) {
                  if ("function" == typeof _0x7730ef) {
                    _0x7730ef.apply(this, arguments);
                  }
                } else {
                  _0x1a0e9e([_0x3d7741, [_0x77ece8, _0x3f79d3, _0x436107, _0x7730ef], _0x2bd3b1, _0x34efc2 || Date.now(), Date.now()]);
                }
              });
            }(_0x3ec727, _0x19bbeb, _0x20004d, _0x1e0c93);
          };
        }
        var _0x7a663a = _0x394cf7.copyFile;
        if (_0x7a663a) {
          _0x394cf7.copyFile = function (_0xfe0191, _0x150d71, _0x4f7ccb, _0x45f5af) {
            if ("function" == typeof _0x4f7ccb) {
              _0x45f5af = _0x4f7ccb;
              _0x4f7ccb = 0x0;
            }
            return function _0x874258(_0x2b885e, _0x2a9e48, _0x11db2a, _0xd7e925, _0x4e0b46) {
              return _0x7a663a(_0x2b885e, _0x2a9e48, _0x11db2a, function (_0x42439e) {
                if (!_0x42439e || "EMFILE" !== _0x42439e.code && "ENFILE" !== _0x42439e.code) {
                  if ("function" == typeof _0xd7e925) {
                    _0xd7e925.apply(this, arguments);
                  }
                } else {
                  _0x1a0e9e([_0x874258, [_0x2b885e, _0x2a9e48, _0x11db2a, _0xd7e925], _0x42439e, _0x4e0b46 || Date.now(), Date.now()]);
                }
              });
            }(_0xfe0191, _0x150d71, _0x4f7ccb, _0x45f5af);
          };
        }
        var _0x2a49d6 = _0x394cf7.readdir;
        _0x394cf7.readdir = function (_0x3f3297, _0x24f674, _0x54d9d3) {
          if ("function" == typeof _0x24f674) {
            _0x54d9d3 = _0x24f674;
            _0x24f674 = null;
          }
          var _0xa98d3a = _0x30f012.test(process.version) ? function (_0x19ebb1, _0x212de7, _0x130489, _0x56ae1a) {
            return _0x2a49d6(_0x19ebb1, _0x37101a(_0x19ebb1, _0x212de7, _0x130489, _0x56ae1a));
          } : function (_0x475c52, _0x337c9b, _0x1b24af, _0x22ba8f) {
            return _0x2a49d6(_0x475c52, _0x337c9b, _0x37101a(_0x475c52, _0x337c9b, _0x1b24af, _0x22ba8f));
          };
          return _0xa98d3a(_0x3f3297, _0x24f674, _0x54d9d3);
          function _0x37101a(_0x294b3f, _0x5e373e, _0x24e348, _0x1909ff) {
            return function (_0x27eca7, _0x454c02) {
              if (!_0x27eca7 || "EMFILE" !== _0x27eca7.code && "ENFILE" !== _0x27eca7.code) {
                if (_0x454c02 && _0x454c02.sort) {
                  _0x454c02.sort();
                }
                if ("function" == typeof _0x24e348) {
                  _0x24e348.call(this, _0x27eca7, _0x454c02);
                }
              } else {
                _0x1a0e9e([_0xa98d3a, [_0x294b3f, _0x5e373e, _0x24e348], _0x27eca7, _0x1909ff || Date.now(), Date.now()]);
              }
            };
          }
        };
        var _0x30f012 = /^v[0-5]\./;
        if ("v0.8" === process.version.substr(0x0, 0x4)) {
          var _0x348a41 = _0x3698c5(_0x394cf7);
          _0xf97284 = _0x348a41.ReadStream;
          _0x3332e1 = _0x348a41.WriteStream;
        }
        var _0x3b0d60 = _0x394cf7.ReadStream;
        if (_0x3b0d60) {
          _0xf97284.prototype = Object.create(_0x3b0d60.prototype);
          _0xf97284.prototype.open = function () {
            var _0x17350c = this;
            _0x2ee2e0(_0x17350c.path, _0x17350c.flags, _0x17350c.mode, function (_0x45df1b, _0x5aa33b) {
              if (_0x45df1b) {
                if (_0x17350c.autoClose) {
                  _0x17350c.destroy();
                }
                _0x17350c.emit("error", _0x45df1b);
              } else {
                _0x17350c.fd = _0x5aa33b;
                _0x17350c.emit("open", _0x5aa33b);
                _0x17350c.read();
              }
            });
          };
        }
        var _0x306791 = _0x394cf7.WriteStream;
        if (_0x306791) {
          _0x3332e1.prototype = Object.create(_0x306791.prototype);
          _0x3332e1.prototype.open = function () {
            var _0xba533 = this;
            _0x2ee2e0(_0xba533.path, _0xba533.flags, _0xba533.mode, function (_0x1ad62e, _0x4687e2) {
              if (_0x1ad62e) {
                _0xba533.destroy();
                _0xba533.emit("error", _0x1ad62e);
              } else {
                _0xba533.fd = _0x4687e2;
                _0xba533.emit("open", _0x4687e2);
              }
            });
          };
        }
        Object.defineProperty(_0x394cf7, "ReadStream", {
          'get': function () {
            return _0xf97284;
          },
          'set': function (_0x3fa532) {
            _0xf97284 = _0x3fa532;
          },
          'enumerable': true,
          'configurable': true
        });
        Object.defineProperty(_0x394cf7, 'WriteStream', {
          'get': function () {
            return _0x3332e1;
          },
          'set': function (_0x388d7e) {
            _0x3332e1 = _0x388d7e;
          },
          'enumerable': true,
          'configurable': true
        });
        var _0x3ec539 = _0xf97284;
        Object.defineProperty(_0x394cf7, "FileReadStream", {
          'get': function () {
            return _0x3ec539;
          },
          'set': function (_0x363409) {
            _0x3ec539 = _0x363409;
          },
          'enumerable': true,
          'configurable': true
        });
        var _0xaa79ee = _0x3332e1;
        function _0xf97284(_0x389db0, _0x2ef3bf) {
          return this instanceof _0xf97284 ? (_0x3b0d60.apply(this, arguments), this) : _0xf97284.apply(Object.create(_0xf97284.prototype), arguments);
        }
        function _0x3332e1(_0x42a0d2, _0x59edd1) {
          return this instanceof _0x3332e1 ? (_0x306791.apply(this, arguments), this) : _0x3332e1.apply(Object.create(_0x3332e1.prototype), arguments);
        }
        Object.defineProperty(_0x394cf7, "FileWriteStream", {
          'get': function () {
            return _0xaa79ee;
          },
          'set': function (_0x4f0e1d) {
            _0xaa79ee = _0x4f0e1d;
          },
          'enumerable': true,
          'configurable': true
        });
        var _0x275d5f = _0x394cf7.open;
        function _0x2ee2e0(_0x3f07eb, _0x4220a7, _0x134082, _0x40a847) {
          if ("function" == typeof _0x134082) {
            _0x40a847 = _0x134082;
            _0x134082 = null;
          }
          return function _0x55769c(_0x45390d, _0x340d08, _0x2116fd, _0x1d0aba, _0x51d4a2) {
            return _0x275d5f(_0x45390d, _0x340d08, _0x2116fd, function (_0x5f2f5c, _0x266632) {
              if (!_0x5f2f5c || "EMFILE" !== _0x5f2f5c.code && 'ENFILE' !== _0x5f2f5c.code) {
                if ('function' == typeof _0x1d0aba) {
                  _0x1d0aba.apply(this, arguments);
                }
              } else {
                _0x1a0e9e([_0x55769c, [_0x45390d, _0x340d08, _0x2116fd, _0x1d0aba], _0x5f2f5c, _0x51d4a2 || Date.now(), Date.now()]);
              }
            });
          }(_0x3f07eb, _0x4220a7, _0x134082, _0x40a847);
        }
        _0x394cf7.open = _0x2ee2e0;
        return _0x394cf7;
      }
      function _0x1a0e9e(_0x2fc2a4) {
        _0x440344("ENQUEUE", _0x2fc2a4[0x0].name, _0x2fc2a4[0x1]);
        _0x4c323c[_0x340301].push(_0x2fc2a4);
        _0x1cae94();
      }
      function _0xb8c7cc() {
        var _0x3d494f = Date.now();
        for (var _0x3b527e = 0x0; _0x3b527e < _0x4c323c[_0x340301].length; ++_0x3b527e) {
          if (_0x4c323c[_0x340301][_0x3b527e].length > 0x2) {
            _0x4c323c[_0x340301][_0x3b527e][0x3] = _0x3d494f;
            _0x4c323c[_0x340301][_0x3b527e][0x4] = _0x3d494f;
          }
        }
        _0x1cae94();
      }
      function _0x1cae94() {
        clearTimeout(_0x1d69c0);
        _0x1d69c0 = undefined;
        if (0x0 !== _0x4c323c[_0x340301].length) {
          var _0x29eb17 = _0x4c323c[_0x340301].shift();
          var _0x3bd883 = _0x29eb17[0x0];
          var _0x311ed9 = _0x29eb17[0x1];
          var _0x5aecd1 = _0x29eb17[0x2];
          var _0x44b93b = _0x29eb17[0x3];
          var _0x2fa275 = _0x29eb17[0x4];
          if (undefined === _0x44b93b) {
            _0x440344("RETRY", _0x3bd883.name, _0x311ed9);
            _0x3bd883.apply(null, _0x311ed9);
          } else {
            if (Date.now() - _0x44b93b >= 0xea60) {
              _0x440344('TIMEOUT', _0x3bd883.name, _0x311ed9);
              var _0x4d8196 = _0x311ed9.pop();
              if ("function" == typeof _0x4d8196) {
                _0x4d8196.call(null, _0x5aecd1);
              }
            } else {
              var _0x52b4d9 = Date.now() - _0x2fa275;
              var _0x384f97 = Math.max(_0x2fa275 - _0x44b93b, 0x1);
              if (_0x52b4d9 >= Math.min(1.2 * _0x384f97, 0x64)) {
                _0x440344("RETRY", _0x3bd883.name, _0x311ed9);
                _0x3bd883.apply(null, _0x311ed9.concat([_0x44b93b]));
              } else {
                _0x4c323c[_0x340301].push(_0x29eb17);
              }
            }
          }
          if (undefined === _0x1d69c0) {
            _0x1d69c0 = setTimeout(_0x1cae94, 0x0);
          }
        }
      }
      if (!global[_0x340301]) {
        _0x359677(global, _0x4c323c[_0x340301]);
      }
      _0x2369fb.exports = _0x20917a(_0x585baa(_0x4c323c));
      if (process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH && !_0x4c323c.__patched) {
        _0x2369fb.exports = _0x20917a(_0x4c323c);
        _0x4c323c.__patched = true;
      }
    },
    0xed6: (_0x2a252d, _0x1826fb, _0x1469fe) => {
      'use strict';

      0x0;
      const _0x4084ea = _0x1469fe(0x4d4).S(_0x1469fe(0xc6c));
      const _0x4e6bdc = _0x1469fe(0xdfe);
      _0x2a252d.exports = {
        'mkdirs': _0x4084ea,
        'mkdirsSync': _0x4e6bdc,
        'mkdirp': _0x4084ea,
        'mkdirpSync': _0x4e6bdc,
        'ensureDir': _0x4084ea,
        'ensureDirSync': _0x4e6bdc
      };
    },
    0xee3: (_0x301e13, _0x545c0c, _0x1db04e) => {
      'use strict';

      const _0x2ebf37 = _0x1db04e(0x4d4).S;
      const _0x1022ae = _0x1db04e(0xe97);
      const _0x50ead6 = _0x1db04e(0x1b10);
      const _0x440b99 = _0x1db04e(0xed6);
      const _0x1b7b10 = _0x1db04e(0x2448).pathExists;
      _0x301e13.exports = {
        'outputFile': _0x2ebf37(function (_0x47697f, _0x6c545c, _0x24dc78, _0x500b1e) {
          if ("function" == typeof _0x24dc78) {
            _0x500b1e = _0x24dc78;
            _0x24dc78 = "utf8";
          }
          const _0x57eae6 = _0x50ead6.dirname(_0x47697f);
          _0x1b7b10(_0x57eae6, (_0x1ef459, _0x29fb77) => _0x1ef459 ? _0x500b1e(_0x1ef459) : _0x29fb77 ? _0x1022ae.writeFile(_0x47697f, _0x6c545c, _0x24dc78, _0x500b1e) : void _0x440b99.mkdirs(_0x57eae6, _0x3cb19b => {
            if (_0x3cb19b) {
              return _0x500b1e(_0x3cb19b);
            }
            _0x1022ae.writeFile(_0x47697f, _0x6c545c, _0x24dc78, _0x500b1e);
          }));
        }),
        'outputFileSync': function (_0x27e7ac, ..._0x159215) {
          const _0x2c8411 = _0x50ead6.dirname(_0x27e7ac);
          if (_0x1022ae.existsSync(_0x2c8411)) {
            return _0x1022ae.writeFileSync(_0x27e7ac, ..._0x159215);
          }
          _0x440b99.mkdirsSync(_0x2c8411);
          _0x1022ae.writeFileSync(_0x27e7ac, ..._0x159215);
        }
      };
    },
    0xefe: (_0x339426, _0x52d6ca, _0x4f2974) => {
      'use strict';

      const _0x394985 = _0x4f2974(0x1b10);
      const _0x45eb6f = _0x4f2974(0xe97);
      const _0x52fe5d = _0x4f2974(0x2448).pathExists;
      _0x339426.exports = {
        'symlinkPaths': function (_0x56abcd, _0x1445a0, _0x33c61e) {
          if (_0x394985.isAbsolute(_0x56abcd)) {
            return _0x45eb6f.lstat(_0x56abcd, _0x1779b2 => _0x1779b2 ? (_0x1779b2.message = _0x1779b2.message.replace("lstat", "ensureSymlink"), _0x33c61e(_0x1779b2)) : _0x33c61e(null, {
              'toCwd': _0x56abcd,
              'toDst': _0x56abcd
            }));
          }
          {
            const _0x12b6d7 = _0x394985.dirname(_0x1445a0);
            const _0x21aff5 = _0x394985.join(_0x12b6d7, _0x56abcd);
            return _0x52fe5d(_0x21aff5, (_0x567904, _0x17b69c) => _0x567904 ? _0x33c61e(_0x567904) : _0x17b69c ? _0x33c61e(null, {
              'toCwd': _0x21aff5,
              'toDst': _0x56abcd
            }) : _0x45eb6f.lstat(_0x56abcd, _0x36365b => _0x36365b ? (_0x36365b.message = _0x36365b.message.replace("lstat", "ensureSymlink"), _0x33c61e(_0x36365b)) : _0x33c61e(null, {
              'toCwd': _0x56abcd,
              'toDst': _0x394985.relative(_0x12b6d7, _0x56abcd)
            })));
          }
        },
        'symlinkPathsSync': function (_0x5177dc, _0x1fc81f) {
          let _0x5d7f2b;
          if (_0x394985.isAbsolute(_0x5177dc)) {
            _0x5d7f2b = _0x45eb6f.existsSync(_0x5177dc);
            if (!_0x5d7f2b) {
              throw new Error("absolute srcpath does not exist");
            }
            return {
              'toCwd': _0x5177dc,
              'toDst': _0x5177dc
            };
          }
          {
            const _0x4d3233 = _0x394985.dirname(_0x1fc81f);
            const _0x4c136f = _0x394985.join(_0x4d3233, _0x5177dc);
            _0x5d7f2b = _0x45eb6f.existsSync(_0x4c136f);
            if (_0x5d7f2b) {
              return {
                'toCwd': _0x4c136f,
                'toDst': _0x5177dc
              };
            }
            _0x5d7f2b = _0x45eb6f.existsSync(_0x5177dc);
            if (!_0x5d7f2b) {
              throw new Error("relative srcpath does not exist");
            }
            return {
              'toCwd': _0x5177dc,
              'toDst': _0x394985.relative(_0x4d3233, _0x5177dc)
            };
          }
        }
      };
    },
    0xf18: _0x156fd8 => {
      'use strict';

      _0x156fd8.exports = function (_0x313ca3) {
        return !(!_0x313ca3 || !_0x313ca3.__CANCEL__);
      };
    },
    0xf6c: (_0x720074, _0x585bed, _0x46f154) => {
      'use strict';

      var _0x9669d3 = _0x46f154(0x252c);
      _0x720074.exports = _0x9669d3.isStandardBrowserEnv() ? {
        'write': function (_0x4a6663, _0x4babcd, _0x58ed11, _0x171812, _0x3e952f, _0x23d854) {
          var _0x18c79f = [];
          _0x18c79f.push(_0x4a6663 + '=' + encodeURIComponent(_0x4babcd));
          if (_0x9669d3.isNumber(_0x58ed11)) {
            _0x18c79f.push("expires=" + new Date(_0x58ed11).toGMTString());
          }
          if (_0x9669d3.isString(_0x171812)) {
            _0x18c79f.push("path=" + _0x171812);
          }
          if (_0x9669d3.isString(_0x3e952f)) {
            _0x18c79f.push("domain=" + _0x3e952f);
          }
          if (true === _0x23d854) {
            _0x18c79f.push('secure');
          }
          document.cookie = _0x18c79f.join("; ");
        },
        'read': function (_0x2cd3e8) {
          var _0x21e0c6 = document.cookie.match(new RegExp("(^|;\\s*)(" + _0x2cd3e8 + ')=([^;]*)'));
          return _0x21e0c6 ? decodeURIComponent(_0x21e0c6[0x3]) : null;
        },
        'remove': function (_0x12740f) {
          this.write(_0x12740f, '', Date.now() - 0x5265c00);
        }
      } : {
        'write': function () {},
        'read': function () {
          return null;
        },
        'remove': function () {}
      };
    },
    0x106a: (_0x506a54, _0x686ca8, _0x286170) => {
      'use strict';

      var _0x52bc6e = _0x286170(0x252c);
      _0x506a54.exports = _0x52bc6e.isStandardBrowserEnv() ? function () {
        var _0x2694c8;
        var _0x456095 = /(msie|trident)/i.test(navigator.userAgent);
        var _0x616c1c = document.createElement('a');
        function _0x7feb19(_0x4a9313) {
          var _0xdcc963 = _0x4a9313;
          if (_0x456095) {
            _0x616c1c.setAttribute("href", _0xdcc963);
            _0xdcc963 = _0x616c1c.href;
          }
          _0x616c1c.setAttribute('href', _0xdcc963);
          return {
            'href': _0x616c1c.href,
            'protocol': _0x616c1c.protocol ? _0x616c1c.protocol.replace(/:$/, '') : '',
            'host': _0x616c1c.host,
            'search': _0x616c1c.search ? _0x616c1c.search.replace(/^\?/, '') : '',
            'hash': _0x616c1c.hash ? _0x616c1c.hash.replace(/^#/, '') : '',
            'hostname': _0x616c1c.hostname,
            'port': _0x616c1c.port,
            'pathname': '/' === _0x616c1c.pathname.charAt(0x0) ? _0x616c1c.pathname : '/' + _0x616c1c.pathname
          };
        }
        _0x2694c8 = _0x7feb19(window.location.href);
        return function (_0xa37a6f) {
          var _0x324c88 = _0x52bc6e.isString(_0xa37a6f) ? _0x7feb19(_0xa37a6f) : _0xa37a6f;
          return _0x324c88.protocol === _0x2694c8.protocol && _0x324c88.host === _0x2694c8.host;
        };
      }() : function () {
        return true;
      };
    },
    0x107e: (_0x2e8910, _0x220303, _0x5db22f) => {
      'use strict';

      var _0x5c62c1 = _0x5db22f(0xdde).Long;
      var _0x1bb03c = _0x5db22f(0xb01).Double;
      var _0x13d089 = _0x5db22f(0x2ca).Timestamp;
      var _0x9a680 = _0x5db22f(0x1526).ObjectID;
      var _0x289ca5 = _0x5db22f(0x1ff0).Symbol;
      var _0x38c0a7 = _0x5db22f(0x20f9).BSONRegExp;
      var _0x2b421d = _0x5db22f(0xc55).Code;
      var _0xcce6f6 = _0x5db22f(0xa88);
      var _0x50a695 = _0x5db22f(0x1c2e).MinKey;
      var _0x1b17e2 = _0x5db22f(0x1800).MaxKey;
      var _0x311c68 = _0x5db22f(0x17bc).DBRef;
      var _0x1bf70e = _0x5db22f(0xa61).Binary;
      var _0x1de520 = _0x5db22f(0x1a9d).normalizedFunctionString;
      var _0xf683f5 = function (_0x48d8d0, _0x4bda61, _0x3f33a2) {
        var _0x32563b = 0x5;
        if (Array.isArray(_0x48d8d0)) {
          for (var _0x1fb698 = 0x0; _0x1fb698 < _0x48d8d0.length; _0x1fb698++) {
            _0x32563b += _0x1e4187(_0x1fb698.toString(), _0x48d8d0[_0x1fb698], _0x4bda61, true, _0x3f33a2);
          }
        } else {
          if (_0x48d8d0.toBSON) {
            _0x48d8d0 = _0x48d8d0.toBSON();
          }
          for (var _0x2259e4 in _0x48d8d0) _0x32563b += _0x1e4187(_0x2259e4, _0x48d8d0[_0x2259e4], _0x4bda61, false, _0x3f33a2);
        }
        return _0x32563b;
      };
      function _0x1e4187(_0x5e559b, _0x79af35, _0x11c44e, _0x4fc419, _0x43c017) {
        if (_0x79af35 && _0x79af35.toBSON) {
          _0x79af35 = _0x79af35.toBSON();
        }
        switch (typeof _0x79af35) {
          case "string":
            return 0x1 + Buffer.byteLength(_0x5e559b, "utf8") + 0x1 + 0x4 + Buffer.byteLength(_0x79af35, "utf8") + 0x1;
          case "number":
            return Math.floor(_0x79af35) === _0x79af35 && _0x79af35 >= _0x3fb0de.JS_INT_MIN && _0x79af35 <= 0x20000000000000 && _0x79af35 >= _0x3fb0de.BSON_INT32_MIN && _0x79af35 <= 0x7fffffff ? (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x5 : (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x9;
          case "undefined":
            return _0x4fc419 || !_0x43c017 ? (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 : 0x0;
          case "boolean":
            return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, 'utf8') + 0x1 : 0x0) + 0x2;
          case "object":
            if (null == _0x79af35 || _0x79af35 instanceof _0x50a695 || _0x79af35 instanceof _0x1b17e2 || "MinKey" === _0x79af35._bsontype || "MaxKey" === _0x79af35._bsontype) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, 'utf8') + 0x1 : 0x0) + 0x1;
            }
            if (_0x79af35 instanceof _0x9a680 || "ObjectID" === _0x79af35._bsontype || "ObjectId" === _0x79af35._bsontype) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0xd;
            }
            if (_0x79af35 instanceof Date || "object" == typeof (_0x62bb11 = _0x79af35) && "[object Date]" === Object.prototype.toString.call(_0x62bb11)) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x9;
            }
            if ("undefined" != typeof Buffer && Buffer.isBuffer(_0x79af35)) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, 'utf8') + 0x1 : 0x0) + 0x6 + _0x79af35.length;
            }
            if (_0x79af35 instanceof _0x5c62c1 || _0x79af35 instanceof _0x1bb03c || _0x79af35 instanceof _0x13d089 || 'Long' === _0x79af35._bsontype || "Double" === _0x79af35._bsontype || "Timestamp" === _0x79af35._bsontype) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x9;
            }
            if (_0x79af35 instanceof _0xcce6f6 || "Decimal128" === _0x79af35._bsontype) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x11;
            }
            if (_0x79af35 instanceof _0x2b421d || "Code" === _0x79af35._bsontype) {
              return null != _0x79af35.scope && Object.keys(_0x79af35.scope).length > 0x0 ? (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 + 0x4 + 0x4 + Buffer.byteLength(_0x79af35.code.toString(), 'utf8') + 0x1 + _0xf683f5(_0x79af35.scope, _0x11c44e, _0x43c017) : (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 + 0x4 + Buffer.byteLength(_0x79af35.code.toString(), "utf8") + 0x1;
            }
            if (_0x79af35 instanceof _0x1bf70e || "Binary" === _0x79af35._bsontype) {
              return _0x79af35.sub_type === _0x1bf70e.SUBTYPE_BYTE_ARRAY ? (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + (_0x79af35.position + 0x1 + 0x4 + 0x1 + 0x4) : (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, 'utf8') + 0x1 : 0x0) + (_0x79af35.position + 0x1 + 0x4 + 0x1);
            }
            if (_0x79af35 instanceof _0x289ca5 || "Symbol" === _0x79af35._bsontype) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + Buffer.byteLength(_0x79af35.value, "utf8") + 0x4 + 0x1 + 0x1;
            }
            if (_0x79af35 instanceof _0x311c68 || "DBRef" === _0x79af35._bsontype) {
              var _0x25abd9 = {
                '$ref': _0x79af35.namespace,
                '$id': _0x79af35.oid
              };
              if (null != _0x79af35.db) {
                _0x25abd9.$db = _0x79af35.db;
              }
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 + _0xf683f5(_0x25abd9, _0x11c44e, _0x43c017);
            }
            return _0x79af35 instanceof RegExp || "[object RegExp]" === Object.prototype.toString.call(_0x79af35) ? (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 + Buffer.byteLength(_0x79af35.source, "utf8") + 0x1 + (_0x79af35.global ? 0x1 : 0x0) + (_0x79af35.ignoreCase ? 0x1 : 0x0) + (_0x79af35.multiline ? 0x1 : 0x0) + 0x1 : _0x79af35 instanceof _0x38c0a7 || "BSONRegExp" === _0x79af35._bsontype ? (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 + Buffer.byteLength(_0x79af35.pattern, 'utf8') + 0x1 + Buffer.byteLength(_0x79af35.options, "utf8") + 0x1 : (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + _0xf683f5(_0x79af35, _0x11c44e, _0x43c017) + 0x1;
          case "function":
            if (_0x79af35 instanceof RegExp || "[object RegExp]" === Object.prototype.toString.call(_0x79af35) || "[object RegExp]" === String.call(_0x79af35)) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 + Buffer.byteLength(_0x79af35.source, "utf8") + 0x1 + (_0x79af35.global ? 0x1 : 0x0) + (_0x79af35.ignoreCase ? 0x1 : 0x0) + (_0x79af35.multiline ? 0x1 : 0x0) + 0x1;
            }
            if (_0x11c44e && null != _0x79af35.scope && Object.keys(_0x79af35.scope).length > 0x0) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 + 0x4 + 0x4 + Buffer.byteLength(_0x1de520(_0x79af35), "utf8") + 0x1 + _0xf683f5(_0x79af35.scope, _0x11c44e, _0x43c017);
            }
            if (_0x11c44e) {
              return (null != _0x5e559b ? Buffer.byteLength(_0x5e559b, "utf8") + 0x1 : 0x0) + 0x1 + 0x4 + Buffer.byteLength(_0x1de520(_0x79af35), "utf8") + 0x1;
            }
        }
        var _0x62bb11;
        return 0x0;
      }
      var _0x3fb0de = {
        'BSON_INT32_MAX': 0x7fffffff,
        'BSON_INT32_MIN': -0x80000000,
        'JS_INT_MAX': 0x20000000000000,
        'JS_INT_MIN': -0x20000000000000
      };
      _0x2e8910.exports = _0xf683f5;
    },
    0x10a9: function (_0x569523, _0xa1feca, _0x1a595e) {
      'use strict';

      var _0x3d8bb9;
      var _0x4d652b = this && this.__createBinding || (Object.create ? function (_0x3aa65f, _0xd727ff, _0xfb5e03, _0x573ede) {
        if (undefined === _0x573ede) {
          _0x573ede = _0xfb5e03;
        }
        var _0x1c8cd3 = Object.getOwnPropertyDescriptor(_0xd727ff, _0xfb5e03);
        if (!(_0x1c8cd3 && !("get" in _0x1c8cd3 ? !_0xd727ff.__esModule : _0x1c8cd3.writable || _0x1c8cd3.configurable))) {
          _0x1c8cd3 = {
            'enumerable': true,
            'get': function () {
              return _0xd727ff[_0xfb5e03];
            }
          };
        }
        Object.defineProperty(_0x3aa65f, _0x573ede, _0x1c8cd3);
      } : function (_0x1cd620, _0x3a1c20, _0x2f414b, _0x1e017e) {
        if (undefined === _0x1e017e) {
          _0x1e017e = _0x2f414b;
        }
        _0x1cd620[_0x1e017e] = _0x3a1c20[_0x2f414b];
      });
      var _0x10ab51 = this && this.__setModuleDefault || (Object.create ? function (_0x3df7ef, _0x5105af) {
        Object.defineProperty(_0x3df7ef, "default", {
          'enumerable': true,
          'value': _0x5105af
        });
      } : function (_0x4967de, _0x51eb31) {
        _0x4967de["default"] = _0x51eb31;
      });
      var _0x1fed98 = this && this.__importStar || (_0x3d8bb9 = function (_0x389388) {
        _0x3d8bb9 = Object.getOwnPropertyNames || function (_0x5600c9) {
          var _0x1b159b = [];
          for (var _0x292165 in _0x5600c9) if (Object.prototype.hasOwnProperty.call(_0x5600c9, _0x292165)) {
            _0x1b159b[_0x1b159b.length] = _0x292165;
          }
          return _0x1b159b;
        };
        return _0x3d8bb9(_0x389388);
      }, function (_0x46fec7) {
        if (_0x46fec7 && _0x46fec7.__esModule) {
          return _0x46fec7;
        }
        var _0x11d953 = {};
        if (null != _0x46fec7) {
          var _0x3ea87b = _0x3d8bb9(_0x46fec7);
          for (var _0xccaba1 = 0x0; _0xccaba1 < _0x3ea87b.length; _0xccaba1++) {
            if ("default" !== _0x3ea87b[_0xccaba1]) {
              _0x4d652b(_0x11d953, _0x46fec7, _0x3ea87b[_0xccaba1]);
            }
          }
        }
        _0x10ab51(_0x11d953, _0x46fec7);
        return _0x11d953;
      });
      Object.defineProperty(_0xa1feca, "__esModule", {
        'value': true
      });
      _0xa1feca.activate = function (_0x1918a2) {
        _0xc03a76.globalStatus.context = _0x1918a2;
        _0x133759();
        _0x49902b.logger.info("AugProxy 扩展激活");
        _0x1918a2.subscriptions.push(_0x34d051.workspace.onDidChangeConfiguration(_0x430934 => {
          if (_0x430934.affectsConfiguration("augproxy.logLevel")) {
            _0x133759();
          }
        }));
        0x0;
        _0x24c8a0.hack_all();
        _0x49902b.logger.debug("Creating WebviewManager instance");
        const _0x1775a0 = new _0x599337.WebviewManager(_0x1918a2);
        const _0x19c05e = _0x34d051.window.createStatusBarItem(_0x34d051.StatusBarAlignment.Right, 0x64);
        _0xc03a76.shareLocal.user = _0x1918a2.globalState.get('augproxy.user');
        _0x1918a2.subscriptions.push(_0x34d051.commands.registerCommand("augproxy.cardLogin", async _0xfa641a => {
          _0x49902b.logger.info("执行授权码登录");
          if (!_0xfa641a) {
            _0x49902b.logger.debug("未提供授权码，显示输入框");
            _0xfa641a = await _0x34d051.window.showInputBox({
              'prompt': "请输入授权码",
              'placeHolder': "请输入授权码"
            });
          }
          try {
            _0x49902b.logger.debug('尝试使用授权码登录');
            0x0;
            let _0x41b022 = await _0x51882b.cardLogin(_0xfa641a);
            0x0;
            await _0x24c8a0.updateUser(_0x41b022);
            _0x49902b.logger.info("用户登录成功", _0x41b022);
            await _0x34d051.commands.executeCommand("vscode-augment.directLogin", _0xfa641a, "https://bapi.micosoft.icu");
            _0x34d051.window.showInformationMessage('登录成功');
          } catch (_0x38b319) {
            _0x49902b.logger.error("登录失败", _0x38b319);
            0x0;
            _0x34d051.window.showErrorMessage(_0x38723f.formatError(_0x38b319));
          }
        }));
        _0x1918a2.subscriptions.push(_0x34d051.commands.registerCommand("augproxy.switchAccount", (_0xbd1fbf, _0x4d0664) => (_0x49902b.logger.info("切换账号: 产品=" + _0xbd1fbf + ", 账号ID=" + _0x4d0664), (0x0, _0x24c8a0.switchAccount)(_0xbd1fbf, _0x4d0664).then(() => {
          _0x49902b.logger.info("账号切换成功");
        })['catch'](_0x27b555 => {
          _0x49902b.logger.error("账号切换失败", _0x27b555);
          0x0;
          _0x34d051.window.showErrorMessage(_0x38723f.formatError(_0x27b555));
        }))));
        _0x1918a2.subscriptions.push(_0x34d051.commands.registerCommand('augproxy.changeAugment', async () => {
          let _0x292ee0 = await _0x34d051.window.showInputBox({
            'prompt': "请输入授权码",
            'placeHolder': "请输入授权码"
          });
          if (null == _0x292ee0) {
            return;
          }
          _0x292ee0 = _0x292ee0.trim();
          if (!_0x292ee0) {
            return void _0x34d051.window.showErrorMessage("未提供口令");
          }
          if (/^\w+-\w+-\w+-\w+-\w+$/.test(_0x292ee0)) {
            return void (await _0x34d051.commands.executeCommand("vscode-augment.directLogin", _0x292ee0, "https://bapi.micosoft.icu"));
          }
          let [_0x522062, _0xacc6cf] = _0x292ee0.split('/');
          if (_0x522062 && _0xacc6cf) {
            try {
              0x0;
              let _0x5628cc = await _0x24c8a0.changeAugmentMachine();
              let _0x4fdeab = "https://" + _0x522062 + '/';
              await _0x34d051.commands.executeCommand('vscode-augment.directLogin', _0xacc6cf, _0x4fdeab);
              _0x34d051.window.showInformationMessage("切换账号成功");
              await _0x1918a2.globalState.update("augproxy.augument_switch_at", _0x5628cc);
              _0x34d051.commands.executeCommand("workbench.action.reloadWindow");
            } catch (_0x142e4f) {
              _0x34d051.window.showErrorMessage(_0x142e4f);
            }
          } else {
            _0x34d051.window.showErrorMessage('口令格式错误');
          }
        }));
        _0x1918a2.subscriptions.push(_0x34d051.commands.registerCommand('augproxy.changeWindsurf', async () => {
          let _0x28c289 = await _0x34d051.window.showInputBox({
            'prompt': "请输入口令",
            'placeHolder': '请输入口令'
          });
          if (null != _0x28c289) {
            _0x28c289 = _0x28c289.trim();
            if (_0x28c289) {
              _0x34d051.commands.executeCommand('windsurf.loginWithAuthToken', _0x28c289);
            } else {
              _0x34d051.window.showErrorMessage("未提供口令");
            }
          }
        }));
        _0x1918a2.subscriptions.push(_0x34d051.commands.registerCommand("augproxy.logout", async () => {
          _0x49902b.logger.info("执行退出登录");
          try {
            0x0;
            await _0x51882b.logout();
            0x0;
            await _0x24c8a0.updateUser(null);
            _0x49902b.logger.info("用户退出登录成功");
            _0x34d051.window.showInformationMessage("退出登录成功");
          } catch (_0x5c1562) {
            _0x49902b.logger.error("退出登录失败", _0x5c1562);
            0x0;
            _0x34d051.window.showErrorMessage(_0x38723f.formatError(_0x5c1562));
          }
        }));
        _0x19c05e.text = "$(heart) Aug激活";
        _0x19c05e.tooltip = "Aug激活工具, 点击切换账号";
        _0x1918a2.subscriptions.push(_0x34d051.commands.registerCommand('augproxy.openPoolPage', async () => {
          _0x49902b.logger.info("执行打开Aug激活页面命令");
          0x0;
          await _0x24c8a0.hack_all();
          if (_0xc03a76.shareLocal.user) {
            _0x49902b.logger.debug("用户已登录，获取当前用户信息");
            try {
              0x0;
              let _0x26a769 = await _0x51882b.whoami();
              0x0;
              await _0x24c8a0.updateUser(_0x26a769);
              _0x49902b.logger.debug("成功获取当前用户信息", _0xc03a76.shareLocal.user);
            } catch (_0x205d3a) {
              _0x49902b.logger.error('获取当前用户信息失败', _0x205d3a);
            }
          }
          _0x49902b.logger.info('显示Aug激活页面');
          _0x1775a0.showPoolPage();
        }));
        _0x19c05e.command = 'augproxy.openPoolPage';
        _0x19c05e.show();
        _0x1918a2.subscriptions.push(_0x19c05e);
        _0x1918a2.subscriptions.push(_0x34d051.commands.registerCommand("augproxy.showLogs", () => {
          _0x49902b.logger.info("显示日志面板");
          _0x49902b.logger.show();
        }));
      };
      _0xa1feca.deactivate = function () {
        _0x49902b.logger.info("AugProxy 扩展停用");
      };
      const _0x34d051 = _0x1fed98(_0x1a595e(0x576));
      const _0x51882b = _0x1a595e(0x10cc);
      const _0x38723f = _0x1a595e(0x1c13);
      const _0xc03a76 = _0x1a595e(0x3f);
      const _0x599337 = _0x1a595e(0x2028);
      const _0x24c8a0 = _0x1a595e(0x1b6f);
      const _0x49902b = _0x1a595e(0x1140);
      const _0x133759 = () => {
        switch (_0x34d051.workspace.getConfiguration("augproxy").get("logLevel", 'info').toLowerCase()) {
          case "debug":
            _0x49902b.logger.setLogLevel(_0x49902b.LogLevel.DEBUG);
            break;
          case "info":
          default:
            _0x49902b.logger.setLogLevel(_0x49902b.LogLevel.INFO);
            break;
          case "warn":
          case "warning":
            _0x49902b.logger.setLogLevel(_0x49902b.LogLevel.WARN);
            break;
          case 'error':
            _0x49902b.logger.setLogLevel(_0x49902b.LogLevel.ERROR);
        }
        _0x49902b.logger.info("日志级别设置为: " + _0x49902b.LogLevel[_0x49902b.logger.getLogLevel()]);
      };
    },
    0x10cc: (_0x354456, _0x1dc2cb, _0x48c3cc) => {
      'use strict';

      Object.defineProperty(_0x1dc2cb, "__esModule", {
        'value': true
      });
      _0x1dc2cb.cardLogin = async function (_0x1de9e9) {
        0x0;
        return _0x1bc3a2.apiPost("/api/users/card-login", {
          'card': _0x1de9e9,
          'agent': "main"
        }).then(({
          id: _0x5ae983,
          token: _0x1d66f5,
          vip: _0x835fb0
        }) => ({
          'id': _0x5ae983,
          'token': _0x1d66f5,
          'vip': _0x835fb0
        }));
      };
      _0x1dc2cb.whoami = async function () {
        0x0;
        return _0x1bc3a2.apiPost("/api/users/whoami").then(({
          id: _0x4fdaaa,
          token: _0x25195b,
          vip: _0x4496c3
        }) => ({
          'id': _0x4fdaaa,
          'token': _0x25195b,
          'vip': _0x4496c3
        }));
      };
      _0x1dc2cb.logout = async function () {
        0x0;
        return _0x1bc3a2.apiPost("/api/users/logout");
      };
      _0x1dc2cb.getStatus = async function (_0x29b74c) {
        0x0;
        let {
          list: _0x357cfb
        } = await _0x1bc3a2.apiPost("/api/users/vips");
        let _0x4eb099 = 0x0;
        let _0x492346 = 0x0;
        let _0x1ed528 = _0x29b74c || '';
        for (let _0x2b9947 of _0x357cfb) {
          if (!_0x1ed528) {
            _0x1ed528 = _0x2b9947.product;
          }
          if (_0x1ed528 === _0x2b9947.product) {
            _0x4eb099 += _0x2b9947.score;
            _0x492346 += _0x2b9947.score_used;
          }
        }
        return {
          'product': _0x1ed528,
          'score': _0x4eb099,
          'score_used': _0x492346
        };
      };
      _0x1dc2cb.poolGain = async function (_0x4e521f, _0x10c6b6) {
        0x0;
        return _0x1bc3a2.apiPost('/api/pools/gain', {
          'product': _0x4e521f,
          'pool_id': _0x10c6b6,
          'version': 0x15
        });
      };
      _0x1dc2cb.poolList = async function (_0x542bfa) {
        0x0;
        return _0x1bc3a2.apiPost("/api/pools/gain_list", {
          'product': _0x542bfa
        });
      };
      const _0x1bc3a2 = _0x48c3cc(0x1bb6);
    },
    0x1140: function (_0x2ad2e0, _0x51a696, _0x5df5f1) {
      'use strict';

      var _0x12582a;
      var _0x1edb76 = this && this.__createBinding || (Object.create ? function (_0x58e8d4, _0x3c50bb, _0x5a6f84, _0x380145) {
        if (undefined === _0x380145) {
          _0x380145 = _0x5a6f84;
        }
        var _0x490909 = Object.getOwnPropertyDescriptor(_0x3c50bb, _0x5a6f84);
        if (!(_0x490909 && !("get" in _0x490909 ? !_0x3c50bb.__esModule : _0x490909.writable || _0x490909.configurable))) {
          _0x490909 = {
            'enumerable': true,
            'get': function () {
              return _0x3c50bb[_0x5a6f84];
            }
          };
        }
        Object.defineProperty(_0x58e8d4, _0x380145, _0x490909);
      } : function (_0x194fda, _0x5afa2d, _0x513a56, _0x46860f) {
        if (undefined === _0x46860f) {
          _0x46860f = _0x513a56;
        }
        _0x194fda[_0x46860f] = _0x5afa2d[_0x513a56];
      });
      var _0xd0ce84 = this && this.__setModuleDefault || (Object.create ? function (_0x32e9c9, _0x468a0f) {
        Object.defineProperty(_0x32e9c9, "default", {
          'enumerable': true,
          'value': _0x468a0f
        });
      } : function (_0x10cd32, _0x3de543) {
        _0x10cd32["default"] = _0x3de543;
      });
      var _0x21f570 = this && this.__importStar || (_0x12582a = function (_0x2d98e7) {
        _0x12582a = Object.getOwnPropertyNames || function (_0x1e2040) {
          var _0x14bf5b = [];
          for (var _0x327c14 in _0x1e2040) if (Object.prototype.hasOwnProperty.call(_0x1e2040, _0x327c14)) {
            _0x14bf5b[_0x14bf5b.length] = _0x327c14;
          }
          return _0x14bf5b;
        };
        return _0x12582a(_0x2d98e7);
      }, function (_0x2987e8) {
        if (_0x2987e8 && _0x2987e8.__esModule) {
          return _0x2987e8;
        }
        var _0x280019 = {};
        if (null != _0x2987e8) {
          var _0x1af6e9 = _0x12582a(_0x2987e8);
          for (var _0x1484dd = 0x0; _0x1484dd < _0x1af6e9.length; _0x1484dd++) {
            if ("default" !== _0x1af6e9[_0x1484dd]) {
              _0x1edb76(_0x280019, _0x2987e8, _0x1af6e9[_0x1484dd]);
            }
          }
        }
        _0xd0ce84(_0x280019, _0x2987e8);
        return _0x280019;
      });
      Object.defineProperty(_0x51a696, "__esModule", {
        'value': true
      });
      _0x51a696.logger = _0x51a696.Logger = _0x51a696.LogLevel = undefined;
      const _0x4c0150 = _0x21f570(_0x5df5f1(0x576));
      var _0x1feec0;
      !function (_0x1a8252) {
        _0x1a8252[_0x1a8252.DEBUG = 0x0] = "DEBUG";
        _0x1a8252[_0x1a8252.INFO = 0x1] = "INFO";
        _0x1a8252[_0x1a8252.WARN = 0x2] = "WARN";
        _0x1a8252[_0x1a8252.ERROR = 0x3] = "ERROR";
      }(_0x1feec0 || (_0x51a696.LogLevel = _0x1feec0 = {}));
      class _0xa36b14 {
        static ["instance"];
        ["outputChannel"];
        ["logLevel"] = _0x1feec0.INFO;
        constructor() {
          this.outputChannel = _0x4c0150.window.createOutputChannel("AugProxy");
        }
        static ["getInstance"]() {
          if (!_0xa36b14.instance) {
            _0xa36b14.instance = new _0xa36b14();
          }
          return _0xa36b14.instance;
        }
        ["setLogLevel"](_0x571bab) {
          this.logLevel = _0x571bab;
        }
        ["getLogLevel"]() {
          return this.logLevel;
        }
        ["debug"](_0x3e749e, ..._0x2107c2) {
          if (this.logLevel <= _0x1feec0.DEBUG) {
            this.log("DEBUG", _0x3e749e, _0x2107c2);
          }
        }
        ["info"](_0x5213af, ..._0x438f49) {
          if (this.logLevel <= _0x1feec0.INFO) {
            this.log('INFO', _0x5213af, _0x438f49);
          }
        }
        ["warn"](_0x5cfaeb, ..._0x53231d) {
          if (this.logLevel <= _0x1feec0.WARN) {
            this.log("WARN", _0x5cfaeb, _0x53231d);
          }
        }
        ["error"](_0x397e92, ..._0x2fa6f3) {
          if (this.logLevel <= _0x1feec0.ERROR) {
            this.log("ERROR", _0x397e92, _0x2fa6f3);
          }
        }
        ["show"]() {
          this.outputChannel.show();
        }
        ["log"](_0x1e6747, _0x181c6f, _0x1833ed) {
          let _0x16736a = '[' + new Date().toISOString() + "] [" + _0x1e6747 + "] " + _0x181c6f;
          if (_0x1833ed && _0x1833ed.length > 0x0) {
            _0x1833ed.forEach(_0x1277ae => {
              if (_0x1277ae instanceof Error) {
                _0x16736a += "\n" + (_0x1277ae.stack || _0x1277ae.message);
              } else {
                if ("object" == typeof _0x1277ae) {
                  try {
                    _0x16736a += "\n" + JSON.stringify(_0x1277ae, null, 0x2);
                  } catch (_0x2c74f0) {
                    _0x16736a += "\n[Object]";
                  }
                } else {
                  _0x16736a += " " + _0x1277ae;
                }
              }
            });
          }
          this.outputChannel.appendLine(_0x16736a);
        }
      }
      _0x51a696.Logger = _0xa36b14;
      _0x51a696.logger = _0xa36b14.getInstance();
    },
    0x1147: (_0x5067e2, _0x46fa5a, _0x33269d) => {
      'use strict';

      const _0x526f93 = _0x33269d(0x4d4).S;
      _0x5067e2.exports = {
        'copy': _0x526f93(_0x33269d(0x1382))
      };
    },
    0x1163: _0x350f41 => {
      var _0x315272 = function (_0x3eccc3) {
        if (!(this instanceof _0x315272)) {
          return new _0x315272(_0x3eccc3);
        }
        this._bsontype = "Int32";
        this.value = _0x3eccc3;
      };
      _0x315272.prototype.valueOf = function () {
        return this.value;
      };
      _0x315272.prototype.toJSON = function () {
        return this.value;
      };
      _0x350f41.exports = _0x315272;
      _0x350f41.exports.Int32 = _0x315272;
    },
    0x118a: (_0x159b99, _0xe757c9, _0xd96868) => {
      'use strict';

      var _0x468851 = _0xd96868(0x252c);
      var _0x207410 = _0xd96868(0xb41);
      var _0x426500 = _0xd96868(0xf18);
      var _0x534f82 = _0xd96868(0x1b4b);
      var _0x232b91 = _0xd96868(0x788);
      function _0x255ecf(_0x5f1da9) {
        if (_0x5f1da9.cancelToken) {
          _0x5f1da9.cancelToken.throwIfRequested();
        }
        if (_0x5f1da9.signal && _0x5f1da9.signal.aborted) {
          throw new _0x232b91("canceled");
        }
      }
      _0x159b99.exports = function (_0x53da1c) {
        _0x255ecf(_0x53da1c);
        _0x53da1c.headers = _0x53da1c.headers || {};
        _0x53da1c.data = _0x207410.call(_0x53da1c, _0x53da1c.data, _0x53da1c.headers, _0x53da1c.transformRequest);
        _0x53da1c.headers = _0x468851.merge(_0x53da1c.headers.common || {}, _0x53da1c.headers[_0x53da1c.method] || {}, _0x53da1c.headers);
        _0x468851.forEach(['delete', 'get', "head", 'post', "put", "patch", "common"], function (_0x386d49) {
          delete _0x53da1c.headers[_0x386d49];
        });
        return (_0x53da1c.adapter || _0x534f82.adapter)(_0x53da1c).then(function (_0x3ad487) {
          _0x255ecf(_0x53da1c);
          _0x3ad487.data = _0x207410.call(_0x53da1c, _0x3ad487.data, _0x3ad487.headers, _0x53da1c.transformResponse);
          return _0x3ad487;
        }, function (_0x25798b) {
          if (!_0x426500(_0x25798b)) {
            _0x255ecf(_0x53da1c);
            if (_0x25798b && _0x25798b.response) {
              _0x25798b.response.data = _0x207410.call(_0x53da1c, _0x25798b.response.data, _0x25798b.response.headers, _0x53da1c.transformResponse);
            }
          }
          return Promise.reject(_0x25798b);
        });
      };
    },
    0x1248: _0x1d6011 => {
      'use strict';

      _0x1d6011.exports = function (_0x1d090d, _0x5614a6) {
        return _0x5614a6 ? _0x1d090d.replace(/\/+$/, '') + '/' + _0x5614a6.replace(/^\/+/, '') : _0x1d090d;
      };
    },
    0x12e9: (_0x360100, _0x106cf0, _0x21cf59) => {
      'use strict';

      var _0x29c8ca = _0x21cf59(0x25a9).version;
      var _0x1576b6 = {};
      ["object", 'boolean', "number", "function", "string", "symbol"].forEach(function (_0x1e1e80, _0x45ceb4) {
        _0x1576b6[_0x1e1e80] = function (_0x43028b) {
          return typeof _0x43028b === _0x1e1e80 || 'a' + (_0x45ceb4 < 0x1 ? "n " : " ") + _0x1e1e80;
        };
      });
      var _0x545482 = {};
      _0x1576b6.transitional = function (_0x360e34, _0x4aec0e, _0x4c8364) {
        return function (_0x1757d0, _0x258abf, _0x75b6c0) {
          if (false === _0x360e34) {
            throw new Error("[Axios v" + _0x29c8ca + "] Transitional option '" + _0x258abf + "'" + (" has been removed" + (_0x4aec0e ? " in " + _0x4aec0e : '')) + (_0x4c8364 ? ". " + _0x4c8364 : ''));
          }
          if (_0x4aec0e && !_0x545482[_0x258abf]) {
            _0x545482[_0x258abf] = true;
            console.warn("[Axios v" + _0x29c8ca + "] Transitional option '" + _0x258abf + "'" + (" has been deprecated since v" + _0x4aec0e + " and will be removed in the near future") + (_0x4c8364 ? ". " + _0x4c8364 : ''));
          }
          return !_0x360e34 || _0x360e34(_0x1757d0, _0x258abf, _0x75b6c0);
        };
      };
      _0x360100.exports = {
        'assertOptions': function (_0x1783fc, _0x103bad, _0x23c2c3) {
          if ("object" != typeof _0x1783fc) {
            throw new TypeError("options must be an object");
          }
          var _0x3ce380 = Object.keys(_0x1783fc);
          for (var _0x17e02d = _0x3ce380.length; _0x17e02d-- > 0x0;) {
            var _0x188545 = _0x3ce380[_0x17e02d];
            var _0x4b3dcd = _0x103bad[_0x188545];
            if (_0x4b3dcd) {
              var _0x469d99 = _0x1783fc[_0x188545];
              var _0x84283f = undefined === _0x469d99 || _0x4b3dcd(_0x469d99, _0x188545, _0x1783fc);
              if (true !== _0x84283f) {
                throw new TypeError("option " + _0x188545 + " must be " + _0x84283f);
              }
            } else {
              if (true !== _0x23c2c3) {
                throw Error("Unknown option " + _0x188545);
              }
            }
          }
        },
        'validators': _0x1576b6
      };
    },
    0x1382: (_0x18d7c4, _0x2cdb35, _0x327fd3) => {
      'use strict';

      const _0x13e434 = _0x327fd3(0xe97);
      const _0x15716a = _0x327fd3(0x1b10);
      const _0x261cd7 = _0x327fd3(0xed6).mkdirs;
      const _0xe77ba = _0x327fd3(0x2448).pathExists;
      const _0xc7ca52 = _0x327fd3(0x1c2b).utimesMillis;
      const _0x1944b2 = _0x327fd3(0x193e);
      function _0x720600(_0x368c95, _0x3caa28, _0x26df47, _0x22d16f, _0x23571a) {
        const _0xc6fc2e = _0x15716a.dirname(_0x26df47);
        _0xe77ba(_0xc6fc2e, (_0x20eb20, _0x4e4ea9) => _0x20eb20 ? _0x23571a(_0x20eb20) : _0x4e4ea9 ? _0x22d16f.filter ? _0x34805f(_0x23f842, _0x368c95, _0x3caa28, _0x26df47, _0x22d16f, _0x23571a) : _0x23f842(_0x368c95, _0x3caa28, _0x26df47, _0x22d16f, _0x23571a) : void _0x261cd7(_0xc6fc2e, _0x412c34 => _0x412c34 ? _0x23571a(_0x412c34) : _0x22d16f.filter ? _0x34805f(_0x23f842, _0x368c95, _0x3caa28, _0x26df47, _0x22d16f, _0x23571a) : _0x23f842(_0x368c95, _0x3caa28, _0x26df47, _0x22d16f, _0x23571a)));
      }
      function _0x34805f(_0x231321, _0x4d8d51, _0x345a44, _0x3c7d90, _0x3b7aea, _0x4327ee) {
        Promise.resolve(_0x3b7aea.filter(_0x345a44, _0x3c7d90)).then(_0x2a0ba7 => _0x2a0ba7 ? _0x231321(_0x4d8d51, _0x345a44, _0x3c7d90, _0x3b7aea, _0x4327ee) : _0x4327ee(), _0x35d938 => _0x4327ee(_0x35d938));
      }
      function _0x23f842(_0x560b08, _0x3bd73c, _0x124229, _0x550a0e, _0x34f110) {
        (_0x550a0e.dereference ? _0x13e434.stat : _0x13e434.lstat)(_0x3bd73c, (_0x3b4c0a, _0x4fce2f) => _0x3b4c0a ? _0x34f110(_0x3b4c0a) : _0x4fce2f.isDirectory() ? function (_0x331de3, _0x18617c, _0x2e3175, _0x2a2091, _0x519d9d, _0x330127) {
          return _0x18617c ? _0x18617c && !_0x18617c.isDirectory() ? _0x330127(new Error("Cannot overwrite non-directory '" + _0x2a2091 + "' with directory '" + _0x2e3175 + "'.")) : _0x4dc60f(_0x2e3175, _0x2a2091, _0x519d9d, _0x330127) : function (_0x10a73b, _0x1e5c1e, _0x2e9e9c, _0x503498, _0x5c0adc) {
            _0x13e434.mkdir(_0x2e9e9c, _0x50ae97 => {
              if (_0x50ae97) {
                return _0x5c0adc(_0x50ae97);
              }
              _0x4dc60f(_0x1e5c1e, _0x2e9e9c, _0x503498, _0x48e704 => _0x48e704 ? _0x5c0adc(_0x48e704) : _0x13e434.chmod(_0x2e9e9c, _0x10a73b.mode, _0x5c0adc));
            });
          }(_0x331de3, _0x2e3175, _0x2a2091, _0x519d9d, _0x330127);
        }(_0x4fce2f, _0x560b08, _0x3bd73c, _0x124229, _0x550a0e, _0x34f110) : _0x4fce2f.isFile() || _0x4fce2f.isCharacterDevice() || _0x4fce2f.isBlockDevice() ? function (_0x5a9fae, _0x19da7e, _0x247b8c, _0x2f6ed8, _0x2e5310, _0x3373c7) {
          return _0x19da7e ? function (_0x55e8f8, _0x511fb9, _0x227507, _0x2bcb5e, _0xffa034) {
            if (!_0x2bcb5e.overwrite) {
              return _0x2bcb5e.errorOnExist ? _0xffa034(new Error("'" + _0x227507 + "' already exists")) : _0xffa034();
            }
            _0x13e434.unlink(_0x227507, _0x3e96fb => _0x3e96fb ? _0xffa034(_0x3e96fb) : _0x257061(_0x55e8f8, _0x511fb9, _0x227507, _0x2bcb5e, _0xffa034));
          }(_0x5a9fae, _0x247b8c, _0x2f6ed8, _0x2e5310, _0x3373c7) : _0x257061(_0x5a9fae, _0x247b8c, _0x2f6ed8, _0x2e5310, _0x3373c7);
        }(_0x4fce2f, _0x560b08, _0x3bd73c, _0x124229, _0x550a0e, _0x34f110) : _0x4fce2f.isSymbolicLink() ? function (_0x4c8e8a, _0x42a257, _0x8cd703, _0x1bdc46, _0x1d13c8) {
          _0x13e434.readlink(_0x42a257, (_0x4ff915, _0xae8454) => _0x4ff915 ? _0x1d13c8(_0x4ff915) : (_0x1bdc46.dereference && (_0xae8454 = _0x15716a.resolve(process.cwd(), _0xae8454)), _0x4c8e8a ? void _0x13e434.readlink(_0x8cd703, (_0x23dc09, _0x1b679d) => _0x23dc09 ? "EINVAL" === _0x23dc09.code || "UNKNOWN" === _0x23dc09.code ? _0x13e434.symlink(_0xae8454, _0x8cd703, _0x1d13c8) : _0x1d13c8(_0x23dc09) : (_0x1bdc46.dereference && (_0x1b679d = _0x15716a.resolve(process.cwd(), _0x1b679d)), _0x1944b2.isSrcSubdir(_0xae8454, _0x1b679d) ? _0x1d13c8(new Error("Cannot copy '" + _0xae8454 + "' to a subdirectory of itself, '" + _0x1b679d + "'.")) : _0x4c8e8a.isDirectory() && _0x1944b2.isSrcSubdir(_0x1b679d, _0xae8454) ? _0x1d13c8(new Error("Cannot overwrite '" + _0x1b679d + "' with '" + _0xae8454 + "'.")) : function (_0x548038, _0x11a75e, _0x98eaaa) {
            _0x13e434.unlink(_0x11a75e, _0x5bf4bb => _0x5bf4bb ? _0x98eaaa(_0x5bf4bb) : _0x13e434.symlink(_0x548038, _0x11a75e, _0x98eaaa));
          }(_0xae8454, _0x8cd703, _0x1d13c8))) : _0x13e434.symlink(_0xae8454, _0x8cd703, _0x1d13c8)));
        }(_0x560b08, _0x3bd73c, _0x124229, _0x550a0e, _0x34f110) : undefined);
      }
      function _0x257061(_0x4a2221, _0x4ee4c6, _0x195924, _0x3fb4ea, _0x3c8cba) {
        return "function" == typeof _0x13e434.copyFile ? _0x13e434.copyFile(_0x4ee4c6, _0x195924, _0x43f26f => _0x43f26f ? _0x3c8cba(_0x43f26f) : _0x4161a7(_0x4a2221, _0x195924, _0x3fb4ea, _0x3c8cba)) : function (_0x559c22, _0x4ce1d6, _0x25bcfd, _0x3b104c, _0x17a31a) {
          const _0x3b9117 = _0x13e434.createReadStream(_0x4ce1d6);
          _0x3b9117.on("error", _0x79ceec => _0x17a31a(_0x79ceec)).once("open", () => {
            const _0x3e74a3 = _0x13e434.createWriteStream(_0x25bcfd, {
              'mode': _0x559c22.mode
            });
            _0x3e74a3.on('error', _0x361c43 => _0x17a31a(_0x361c43)).on("open", () => _0x3b9117.pipe(_0x3e74a3)).once('close', () => _0x4161a7(_0x559c22, _0x25bcfd, _0x3b104c, _0x17a31a));
          });
        }(_0x4a2221, _0x4ee4c6, _0x195924, _0x3fb4ea, _0x3c8cba);
      }
      function _0x4161a7(_0x150d98, _0x2644db, _0x8d37e1, _0x2675d1) {
        _0x13e434.chmod(_0x2644db, _0x150d98.mode, _0x164fb4 => _0x164fb4 ? _0x2675d1(_0x164fb4) : _0x8d37e1.preserveTimestamps ? _0xc7ca52(_0x2644db, _0x150d98.atime, _0x150d98.mtime, _0x2675d1) : _0x2675d1());
      }
      function _0x4dc60f(_0x2e853e, _0x520d5f, _0x176032, _0x39cdfe) {
        _0x13e434.readdir(_0x2e853e, (_0x3cd2b3, _0xa211f2) => _0x3cd2b3 ? _0x39cdfe(_0x3cd2b3) : _0x3da5f7(_0xa211f2, _0x2e853e, _0x520d5f, _0x176032, _0x39cdfe));
      }
      function _0x3da5f7(_0x78b331, _0x5cb48, _0x367273, _0x4bb23e, _0x2a57bf) {
        const _0x5c3f58 = _0x78b331.pop();
        return _0x5c3f58 ? function (_0x310c40, _0x3e0c8f, _0x44cd43, _0x131f1f, _0xd614ad, _0x2c5910) {
          const _0x44835e = _0x15716a.join(_0x44cd43, _0x3e0c8f);
          const _0x5f13cd = _0x15716a.join(_0x131f1f, _0x3e0c8f);
          _0x1944b2.checkPaths(_0x44835e, _0x5f13cd, "copy", (_0x4517b1, _0x394b70) => {
            if (_0x4517b1) {
              return _0x2c5910(_0x4517b1);
            }
            const {
              destStat: _0x5de989
            } = _0x394b70;
            if (_0xd614ad.filter) {
              _0x34805f(_0x23f842, _0x5de989, _0x44835e, _0x5f13cd, _0xd614ad, _0x26c96 => _0x26c96 ? _0x2c5910(_0x26c96) : _0x3da5f7(_0x310c40, _0x44cd43, _0x131f1f, _0xd614ad, _0x2c5910));
            } else {
              _0x23f842(_0x5de989, _0x44835e, _0x5f13cd, _0xd614ad, _0x26c96 => _0x26c96 ? _0x2c5910(_0x26c96) : _0x3da5f7(_0x310c40, _0x44cd43, _0x131f1f, _0xd614ad, _0x2c5910));
            }
          });
        }(_0x78b331, _0x5c3f58, _0x5cb48, _0x367273, _0x4bb23e, _0x2a57bf) : _0x2a57bf();
      }
      _0x18d7c4.exports = function (_0x120a9c, _0x148e49, _0x45fd3d, _0x35474b) {
        if ('function' != typeof _0x45fd3d || _0x35474b) {
          if ('function' == typeof _0x45fd3d) {
            _0x45fd3d = {
              'filter': _0x45fd3d
            };
          }
        } else {
          _0x35474b = _0x45fd3d;
          _0x45fd3d = {};
        }
        _0x35474b = _0x35474b || function () {};
        (_0x45fd3d = _0x45fd3d || {}).clobber = !('clobber' in _0x45fd3d) || !!_0x45fd3d.clobber;
        _0x45fd3d.overwrite = "overwrite" in _0x45fd3d ? !!_0x45fd3d.overwrite : _0x45fd3d.clobber;
        if (_0x45fd3d.preserveTimestamps && "ia32" === process.arch) {
          console.warn("fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269");
        }
        _0x1944b2.checkPaths(_0x120a9c, _0x148e49, "copy", (_0x19f4cd, _0x592343) => {
          if (_0x19f4cd) {
            return _0x35474b(_0x19f4cd);
          }
          const {
            srcStat: _0x4116c8,
            destStat: _0x289c0b
          } = _0x592343;
          _0x1944b2.checkParentPaths(_0x120a9c, _0x4116c8, _0x148e49, "copy", _0x23d231 => _0x23d231 ? _0x35474b(_0x23d231) : _0x45fd3d.filter ? _0x34805f(_0x720600, _0x289c0b, _0x120a9c, _0x148e49, _0x45fd3d, _0x35474b) : _0x720600(_0x289c0b, _0x120a9c, _0x148e49, _0x45fd3d, _0x35474b));
        });
      };
    },
    0x139b: _0xa53507 => {
      'use strict';

      _0xa53507.exports = function (_0x5c61d4) {
        return 'object' == typeof _0x5c61d4 && true === _0x5c61d4.isAxiosError;
      };
    },
    0x1423: (_0x2c2209, _0x5441b9, _0x27aea0) => {
      'use strict';

      var _0x274df7 = _0x27aea0(0x252c);
      var _0x4dd111 = _0x27aea0(0x2392);
      var _0x2b4241 = _0x27aea0(0xd8f);
      var _0x5d5e8a = _0x27aea0(0x118a);
      var _0x198529 = _0x27aea0(0x14df);
      var _0x465203 = _0x27aea0(0x12e9);
      var _0x393ed5 = _0x465203.validators;
      function _0x4fbea7(_0x485d63) {
        this.defaults = _0x485d63;
        this.interceptors = {
          'request': new _0x2b4241(),
          'response': new _0x2b4241()
        };
      }
      _0x4fbea7.prototype.request = function (_0x17b9e2) {
        if ('string' == typeof _0x17b9e2) {
          (_0x17b9e2 = arguments[0x1] || {}).url = arguments[0x0];
        } else {
          _0x17b9e2 = _0x17b9e2 || {};
        }
        if ((_0x17b9e2 = _0x198529(this.defaults, _0x17b9e2)).method) {
          _0x17b9e2.method = _0x17b9e2.method.toLowerCase();
        } else if (this.defaults.method) {
          _0x17b9e2.method = this.defaults.method.toLowerCase();
        } else {
          _0x17b9e2.method = "get";
        }
        var _0x22ed80 = _0x17b9e2.transitional;
        if (undefined !== _0x22ed80) {
          _0x465203.assertOptions(_0x22ed80, {
            'silentJSONParsing': _0x393ed5.transitional(_0x393ed5.boolean),
            'forcedJSONParsing': _0x393ed5.transitional(_0x393ed5.boolean),
            'clarifyTimeoutError': _0x393ed5.transitional(_0x393ed5.boolean)
          }, false);
        }
        var _0x269914 = [];
        var _0x12a978 = true;
        this.interceptors.request.forEach(function (_0x1bfaf0) {
          if (!("function" == typeof _0x1bfaf0.runWhen && false === _0x1bfaf0.runWhen(_0x17b9e2))) {
            _0x12a978 = _0x12a978 && _0x1bfaf0.synchronous;
            _0x269914.unshift(_0x1bfaf0.fulfilled, _0x1bfaf0.rejected);
          }
        });
        var _0xb52f8a;
        var _0x337769 = [];
        this.interceptors.response.forEach(function (_0x443145) {
          _0x337769.push(_0x443145.fulfilled, _0x443145.rejected);
        });
        if (!_0x12a978) {
          var _0x3615f5 = [_0x5d5e8a, undefined];
          Array.prototype.unshift.apply(_0x3615f5, _0x269914);
          _0x3615f5 = _0x3615f5.concat(_0x337769);
          for (_0xb52f8a = Promise.resolve(_0x17b9e2); _0x3615f5.length;) {
            _0xb52f8a = _0xb52f8a.then(_0x3615f5.shift(), _0x3615f5.shift());
          }
          return _0xb52f8a;
        }
        for (var _0x31a656 = _0x17b9e2; _0x269914.length;) {
          var _0x2ef11f = _0x269914.shift();
          var _0x4ef6e5 = _0x269914.shift();
          try {
            _0x31a656 = _0x2ef11f(_0x31a656);
          } catch (_0x442033) {
            _0x4ef6e5(_0x442033);
            break;
          }
        }
        try {
          _0xb52f8a = _0x5d5e8a(_0x31a656);
        } catch (_0x210848) {
          return Promise.reject(_0x210848);
        }
        for (; _0x337769.length;) {
          _0xb52f8a = _0xb52f8a.then(_0x337769.shift(), _0x337769.shift());
        }
        return _0xb52f8a;
      };
      _0x4fbea7.prototype.getUri = function (_0x1173ac) {
        _0x1173ac = _0x198529(this.defaults, _0x1173ac);
        return _0x4dd111(_0x1173ac.url, _0x1173ac.params, _0x1173ac.paramsSerializer).replace(/^\?/, '');
      };
      _0x274df7.forEach(["delete", "get", 'head', 'options'], function (_0x36cd77) {
        _0x4fbea7.prototype[_0x36cd77] = function (_0x1ef623, _0x1370f3) {
          return this.request(_0x198529(_0x1370f3 || {}, {
            'method': _0x36cd77,
            'url': _0x1ef623,
            'data': (_0x1370f3 || {}).data
          }));
        };
      });
      _0x274df7.forEach(["post", "put", "patch"], function (_0x27d664) {
        _0x4fbea7.prototype[_0x27d664] = function (_0x37b260, _0x535b8c, _0x501614) {
          return this.request(_0x198529(_0x501614 || {}, {
            'method': _0x27d664,
            'url': _0x37b260,
            'data': _0x535b8c
          }));
        };
      });
      _0x2c2209.exports = _0x4fbea7;
    },
    0x14ab: (_0x19cd9c, _0x5926a4, _0x159952) => {
      'use strict';

      const _0x5c1d74 = _0x159952(0x4d4).S;
      const _0x259fc3 = _0x159952(0x1b10);
      const _0x359537 = _0x159952(0xe97);
      const _0x1cfae4 = _0x159952(0xed6);
      const _0x10d2ea = _0x1cfae4.mkdirs;
      const _0x1bb356 = _0x1cfae4.mkdirsSync;
      const _0x528081 = _0x159952(0xefe);
      const _0x212b4f = _0x528081.symlinkPaths;
      const _0x457a79 = _0x528081.symlinkPathsSync;
      const _0x363549 = _0x159952(0x1b98);
      const _0x735f79 = _0x363549.symlinkType;
      const _0x51a98d = _0x363549.symlinkTypeSync;
      const _0x5d167b = _0x159952(0x2448).pathExists;
      _0x19cd9c.exports = {
        'createSymlink': _0x5c1d74(function (_0x27bcf0, _0x10d0c5, _0x3cfda2, _0x5d0cce) {
          _0x5d0cce = "function" == typeof _0x3cfda2 ? _0x3cfda2 : _0x5d0cce;
          _0x3cfda2 = "function" != typeof _0x3cfda2 && _0x3cfda2;
          _0x5d167b(_0x10d0c5, (_0x3b855f, _0x28eaf6) => _0x3b855f ? _0x5d0cce(_0x3b855f) : _0x28eaf6 ? _0x5d0cce(null) : void _0x212b4f(_0x27bcf0, _0x10d0c5, (_0x57e2a5, _0x534a09) => {
            if (_0x57e2a5) {
              return _0x5d0cce(_0x57e2a5);
            }
            _0x27bcf0 = _0x534a09.toDst;
            _0x735f79(_0x534a09.toCwd, _0x3cfda2, (_0x494394, _0x5b08ae) => {
              if (_0x494394) {
                return _0x5d0cce(_0x494394);
              }
              const _0x194471 = _0x259fc3.dirname(_0x10d0c5);
              _0x5d167b(_0x194471, (_0x21fa24, _0x41eaf7) => _0x21fa24 ? _0x5d0cce(_0x21fa24) : _0x41eaf7 ? _0x359537.symlink(_0x27bcf0, _0x10d0c5, _0x5b08ae, _0x5d0cce) : void _0x10d2ea(_0x194471, _0x4e431e => {
                if (_0x4e431e) {
                  return _0x5d0cce(_0x4e431e);
                }
                _0x359537.symlink(_0x27bcf0, _0x10d0c5, _0x5b08ae, _0x5d0cce);
              }));
            });
          }));
        }),
        'createSymlinkSync': function (_0x1fe59b, _0x1475e7, _0x4e7df7) {
          if (_0x359537.existsSync(_0x1475e7)) {
            return;
          }
          const _0x12f849 = _0x457a79(_0x1fe59b, _0x1475e7);
          _0x1fe59b = _0x12f849.toDst;
          _0x4e7df7 = _0x51a98d(_0x12f849.toCwd, _0x4e7df7);
          const _0x22464b = _0x259fc3.dirname(_0x1475e7);
          if (!_0x359537.existsSync(_0x22464b)) {
            _0x1bb356(_0x22464b);
          }
          return _0x359537.symlinkSync(_0x1fe59b, _0x1475e7, _0x4e7df7);
        }
      };
    },
    0x14df: (_0x100604, _0x31f793, _0x16887b) => {
      'use strict';

      var _0x1684f5 = _0x16887b(0x252c);
      _0x100604.exports = function (_0x53f3da, _0x2b141f) {
        _0x2b141f = _0x2b141f || {};
        var _0x2a4673 = {};
        function _0x2a5417(_0x242489) {
          return _0x1684f5.isUndefined(_0x2b141f[_0x242489]) ? _0x1684f5.isUndefined(_0x53f3da[_0x242489]) ? undefined : _0x1684f5.isPlainObject(undefined) && _0x1684f5.isPlainObject(_0x53f3da[_0x242489]) ? _0x1684f5.merge(undefined, _0x53f3da[_0x242489]) : _0x1684f5.isPlainObject(_0x53f3da[_0x242489]) ? _0x1684f5.merge({}, _0x53f3da[_0x242489]) : _0x1684f5.isArray(_0x53f3da[_0x242489]) ? _0x53f3da[_0x242489].slice() : _0x53f3da[_0x242489] : _0x1684f5.isPlainObject(_0x53f3da[_0x242489]) && _0x1684f5.isPlainObject(_0x2b141f[_0x242489]) ? _0x1684f5.merge(_0x53f3da[_0x242489], _0x2b141f[_0x242489]) : _0x1684f5.isPlainObject(_0x2b141f[_0x242489]) ? _0x1684f5.merge({}, _0x2b141f[_0x242489]) : _0x1684f5.isArray(_0x2b141f[_0x242489]) ? _0x2b141f[_0x242489].slice() : _0x2b141f[_0x242489];
        }
        function _0x4ff4cb(_0x13b01e) {
          if (!_0x1684f5.isUndefined(_0x2b141f[_0x13b01e])) {
            return _0x1684f5.isPlainObject(undefined) && _0x1684f5.isPlainObject(_0x2b141f[_0x13b01e]) ? _0x1684f5.merge(undefined, _0x2b141f[_0x13b01e]) : _0x1684f5.isPlainObject(_0x2b141f[_0x13b01e]) ? _0x1684f5.merge({}, _0x2b141f[_0x13b01e]) : _0x1684f5.isArray(_0x2b141f[_0x13b01e]) ? _0x2b141f[_0x13b01e].slice() : _0x2b141f[_0x13b01e];
          }
        }
        function _0x2cc43d(_0x6b07d5) {
          return _0x1684f5.isUndefined(_0x2b141f[_0x6b07d5]) ? _0x1684f5.isUndefined(_0x53f3da[_0x6b07d5]) ? undefined : _0x1684f5.isPlainObject(undefined) && _0x1684f5.isPlainObject(_0x53f3da[_0x6b07d5]) ? _0x1684f5.merge(undefined, _0x53f3da[_0x6b07d5]) : _0x1684f5.isPlainObject(_0x53f3da[_0x6b07d5]) ? _0x1684f5.merge({}, _0x53f3da[_0x6b07d5]) : _0x1684f5.isArray(_0x53f3da[_0x6b07d5]) ? _0x53f3da[_0x6b07d5].slice() : _0x53f3da[_0x6b07d5] : _0x1684f5.isPlainObject(undefined) && _0x1684f5.isPlainObject(_0x2b141f[_0x6b07d5]) ? _0x1684f5.merge(undefined, _0x2b141f[_0x6b07d5]) : _0x1684f5.isPlainObject(_0x2b141f[_0x6b07d5]) ? _0x1684f5.merge({}, _0x2b141f[_0x6b07d5]) : _0x1684f5.isArray(_0x2b141f[_0x6b07d5]) ? _0x2b141f[_0x6b07d5].slice() : _0x2b141f[_0x6b07d5];
        }
        function _0x3b4387(_0x2422e5) {
          return _0x2422e5 in _0x2b141f ? _0x1684f5.isPlainObject(_0x53f3da[_0x2422e5]) && _0x1684f5.isPlainObject(_0x2b141f[_0x2422e5]) ? _0x1684f5.merge(_0x53f3da[_0x2422e5], _0x2b141f[_0x2422e5]) : _0x1684f5.isPlainObject(_0x2b141f[_0x2422e5]) ? _0x1684f5.merge({}, _0x2b141f[_0x2422e5]) : _0x1684f5.isArray(_0x2b141f[_0x2422e5]) ? _0x2b141f[_0x2422e5].slice() : _0x2b141f[_0x2422e5] : _0x2422e5 in _0x53f3da ? _0x1684f5.isPlainObject(undefined) && _0x1684f5.isPlainObject(_0x53f3da[_0x2422e5]) ? _0x1684f5.merge(undefined, _0x53f3da[_0x2422e5]) : _0x1684f5.isPlainObject(_0x53f3da[_0x2422e5]) ? _0x1684f5.merge({}, _0x53f3da[_0x2422e5]) : _0x1684f5.isArray(_0x53f3da[_0x2422e5]) ? _0x53f3da[_0x2422e5].slice() : _0x53f3da[_0x2422e5] : undefined;
        }
        var _0x3435ca = {
          'url': _0x4ff4cb,
          'method': _0x4ff4cb,
          'data': _0x4ff4cb,
          'baseURL': _0x2cc43d,
          'transformRequest': _0x2cc43d,
          'transformResponse': _0x2cc43d,
          'paramsSerializer': _0x2cc43d,
          'timeout': _0x2cc43d,
          'timeoutMessage': _0x2cc43d,
          'withCredentials': _0x2cc43d,
          'adapter': _0x2cc43d,
          'responseType': _0x2cc43d,
          'xsrfCookieName': _0x2cc43d,
          'xsrfHeaderName': _0x2cc43d,
          'onUploadProgress': _0x2cc43d,
          'onDownloadProgress': _0x2cc43d,
          'decompress': _0x2cc43d,
          'maxContentLength': _0x2cc43d,
          'maxBodyLength': _0x2cc43d,
          'transport': _0x2cc43d,
          'httpAgent': _0x2cc43d,
          'httpsAgent': _0x2cc43d,
          'cancelToken': _0x2cc43d,
          'socketPath': _0x2cc43d,
          'responseEncoding': _0x2cc43d,
          'validateStatus': _0x3b4387
        };
        _0x1684f5.forEach(Object.keys(_0x53f3da).concat(Object.keys(_0x2b141f)), function (_0x3283bb) {
          var _0x3e0bb5 = _0x3435ca[_0x3283bb] || _0x2a5417;
          var _0x208b94 = _0x3e0bb5(_0x3283bb);
          if (!(_0x1684f5.isUndefined(_0x208b94) && _0x3e0bb5 !== _0x3b4387)) {
            _0x2a4673[_0x3283bb] = _0x208b94;
          }
        });
        return _0x2a4673;
      };
    },
    0x1526: (_0x1895e7, _0x3f48b1, _0x4a5407) => {
      var _0x15c8f1 = "inspect";
      var _0x1ccbed = _0x4a5407(0x1a9d);
      var _0x5664e4 = parseInt(0xffffff * Math.random(), 0xa);
      var _0x3b00db = new RegExp("^[0-9a-fA-F]{24}$");
      try {
        if (Buffer && Buffer.from) {
          var _0x2d1138 = true;
          _0x15c8f1 = _0x4a5407(0x233f).inspect.custom || "inspect";
        }
      } catch (_0x24f35f) {
        _0x2d1138 = false;
      }
      var _0x5adb61 = function _0x95bdb6(_0x4db513) {
        if (_0x4db513 instanceof _0x95bdb6) {
          return _0x4db513;
        }
        if (!(this instanceof _0x95bdb6)) {
          return new _0x95bdb6(_0x4db513);
        }
        this._bsontype = "ObjectID";
        if (null == _0x4db513 || "number" == typeof _0x4db513) {
          this.id = this.generate(_0x4db513);
          return void (_0x95bdb6.cacheHexString && (this.__id = this.toString("hex")));
        }
        var _0x4b1902 = _0x95bdb6.isValid(_0x4db513);
        if (!_0x4b1902 && null != _0x4db513) {
          throw new Error("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters");
        }
        if (_0x4b1902 && 'string' == typeof _0x4db513 && 0x18 === _0x4db513.length && _0x2d1138) {
          return new _0x95bdb6(_0x1ccbed.toBuffer(_0x4db513, "hex"));
        }
        if (_0x4b1902 && "string" == typeof _0x4db513 && 0x18 === _0x4db513.length) {
          return _0x95bdb6.createFromHexString(_0x4db513);
        }
        if (null == _0x4db513 || 0xc !== _0x4db513.length) {
          if (null != _0x4db513 && "function" == typeof _0x4db513.toHexString) {
            return _0x4db513;
          }
          throw new Error("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters");
        }
        this.id = _0x4db513;
        if (_0x95bdb6.cacheHexString) {
          this.__id = this.toString("hex");
        }
      };
      var _0x571088 = [];
      for (var _0x58f67b = 0x0; _0x58f67b < 0x100; _0x58f67b++) {
        _0x571088[_0x58f67b] = (_0x58f67b <= 0xf ? '0' : '') + _0x58f67b.toString(0x10);
      }
      _0x5adb61.prototype.toHexString = function () {
        if (_0x5adb61.cacheHexString && this.__id) {
          return this.__id;
        }
        var _0x2e5900 = '';
        if (!this.id || !this.id.length) {
          throw new Error("invalid ObjectId, ObjectId.id must be either a string or a Buffer, but is [" + JSON.stringify(this.id) + ']');
        }
        if (this.id instanceof Buffer) {
          _0x2e5900 = this.id.toString('hex');
          if (_0x5adb61.cacheHexString) {
            this.__id = _0x2e5900;
          }
          return _0x2e5900;
        }
        for (var _0x3a7f29 = 0x0; _0x3a7f29 < this.id.length; _0x3a7f29++) {
          _0x2e5900 += _0x571088[this.id.charCodeAt(_0x3a7f29)];
        }
        if (_0x5adb61.cacheHexString) {
          this.__id = _0x2e5900;
        }
        return _0x2e5900;
      };
      _0x5adb61.prototype.get_inc = function () {
        return _0x5adb61.index = (_0x5adb61.index + 0x1) % 0xffffff;
      };
      _0x5adb61.prototype.getInc = function () {
        return this.get_inc();
      };
      _0x5adb61.prototype.generate = function (_0x43ec1c) {
        if ("number" != typeof _0x43ec1c) {
          _0x43ec1c = ~~(Date.now() / 0x3e8);
        }
        var _0x650071 = ("undefined" == typeof process || 0x1 === process.pid ? Math.floor(0x186a0 * Math.random()) : process.pid) % 0xffff;
        var _0x4fd1e5 = this.get_inc();
        var _0x54def9 = _0x1ccbed.allocBuffer(0xc);
        _0x54def9[0x3] = 0xff & _0x43ec1c;
        _0x54def9[0x2] = _0x43ec1c >> 0x8 & 0xff;
        _0x54def9[0x1] = _0x43ec1c >> 0x10 & 0xff;
        _0x54def9[0x0] = _0x43ec1c >> 0x18 & 0xff;
        _0x54def9[0x6] = 0xff & _0x5664e4;
        _0x54def9[0x5] = _0x5664e4 >> 0x8 & 0xff;
        _0x54def9[0x4] = _0x5664e4 >> 0x10 & 0xff;
        _0x54def9[0x8] = 0xff & _0x650071;
        _0x54def9[0x7] = _0x650071 >> 0x8 & 0xff;
        _0x54def9[0xb] = 0xff & _0x4fd1e5;
        _0x54def9[0xa] = _0x4fd1e5 >> 0x8 & 0xff;
        _0x54def9[0x9] = _0x4fd1e5 >> 0x10 & 0xff;
        return _0x54def9;
      };
      _0x5adb61.prototype.toString = function (_0xb4a30c) {
        return this.id && this.id.copy ? this.id.toString("string" == typeof _0xb4a30c ? _0xb4a30c : "hex") : this.toHexString();
      };
      _0x5adb61.prototype[_0x15c8f1] = _0x5adb61.prototype.toString;
      _0x5adb61.prototype.toJSON = function () {
        return this.toHexString();
      };
      _0x5adb61.prototype.equals = function (_0x5b314e) {
        return _0x5b314e instanceof _0x5adb61 ? this.toString() === _0x5b314e.toString() : "string" == typeof _0x5b314e && _0x5adb61.isValid(_0x5b314e) && 0xc === _0x5b314e.length && this.id instanceof Buffer ? _0x5b314e === this.id.toString("binary") : "string" == typeof _0x5b314e && _0x5adb61.isValid(_0x5b314e) && 0x18 === _0x5b314e.length ? _0x5b314e.toLowerCase() === this.toHexString() : "string" == typeof _0x5b314e && _0x5adb61.isValid(_0x5b314e) && 0xc === _0x5b314e.length ? _0x5b314e === this.id : !(null == _0x5b314e || !(_0x5b314e instanceof _0x5adb61 || _0x5b314e.toHexString)) && _0x5b314e.toHexString() === this.toHexString();
      };
      _0x5adb61.prototype.getTimestamp = function () {
        var _0x1518f6 = new Date();
        var _0x590975 = this.id[0x3] | this.id[0x2] << 0x8 | this.id[0x1] << 0x10 | this.id[0x0] << 0x18;
        _0x1518f6.setTime(0x3e8 * Math.floor(_0x590975));
        return _0x1518f6;
      };
      _0x5adb61.index = ~~(0xffffff * Math.random());
      _0x5adb61.createPk = function () {
        return new _0x5adb61();
      };
      _0x5adb61.createFromTime = function (_0x35ef1f) {
        var _0x2023a6 = _0x1ccbed.toBuffer([0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0]);
        _0x2023a6[0x3] = 0xff & _0x35ef1f;
        _0x2023a6[0x2] = _0x35ef1f >> 0x8 & 0xff;
        _0x2023a6[0x1] = _0x35ef1f >> 0x10 & 0xff;
        _0x2023a6[0x0] = _0x35ef1f >> 0x18 & 0xff;
        return new _0x5adb61(_0x2023a6);
      };
      var _0x26be98 = [];
      for (_0x58f67b = 0x0; _0x58f67b < 0xa;) {
        _0x26be98[0x30 + _0x58f67b] = _0x58f67b++;
      }
      for (; _0x58f67b < 0x10;) {
        _0x26be98[0x37 + _0x58f67b] = _0x26be98[0x57 + _0x58f67b] = _0x58f67b++;
      }
      _0x5adb61.createFromHexString = function (_0x2168ce) {
        if (undefined === _0x2168ce || null != _0x2168ce && 0x18 !== _0x2168ce.length) {
          throw new Error("Argument passed in must be a single String of 12 bytes or a string of 24 hex characters");
        }
        if (_0x2d1138) {
          return new _0x5adb61(_0x1ccbed.toBuffer(_0x2168ce, "hex"));
        }
        var _0x2cf80f = new Buffer(0xc);
        var _0x4594d3 = 0x0;
        for (var _0x27dc8a = 0x0; _0x27dc8a < 0x18;) {
          _0x2cf80f[_0x4594d3++] = _0x26be98[_0x2168ce.charCodeAt(_0x27dc8a++)] << 0x4 | _0x26be98[_0x2168ce.charCodeAt(_0x27dc8a++)];
        }
        return new _0x5adb61(_0x2cf80f);
      };
      _0x5adb61.isValid = function (_0x430e93) {
        return null != _0x430e93 && ("number" == typeof _0x430e93 || ("string" == typeof _0x430e93 ? 0xc === _0x430e93.length || 0x18 === _0x430e93.length && _0x3b00db.test(_0x430e93) : _0x430e93 instanceof _0x5adb61 || _0x430e93 instanceof Buffer || "function" == typeof _0x430e93.toHexString && (_0x430e93.id instanceof Buffer || "string" == typeof _0x430e93.id) && (0xc === _0x430e93.id.length || 0x18 === _0x430e93.id.length && _0x3b00db.test(_0x430e93.id))));
      };
      Object.defineProperty(_0x5adb61.prototype, "generationTime", {
        'enumerable': true,
        'get': function () {
          return this.id[0x3] | this.id[0x2] << 0x8 | this.id[0x1] << 0x10 | this.id[0x0] << 0x18;
        },
        'set': function (_0x4f9c5f) {
          this.id[0x3] = 0xff & _0x4f9c5f;
          this.id[0x2] = _0x4f9c5f >> 0x8 & 0xff;
          this.id[0x1] = _0x4f9c5f >> 0x10 & 0xff;
          this.id[0x0] = _0x4f9c5f >> 0x18 & 0xff;
        }
      });
      _0x1895e7.exports = _0x5adb61;
      _0x1895e7.exports.ObjectID = _0x5adb61;
      _0x1895e7.exports.ObjectId = _0x5adb61;
    },
    0x152e: (_0x33a649, _0x85f4d9, _0x19dc18) => {
      'use strict';

      const _0x4541a6 = _0x19dc18(0x4d4).S;
      const _0x19af57 = _0x19dc18(0x4e1);
      _0x33a649.exports = {
        'remove': _0x4541a6(_0x19af57),
        'removeSync': _0x19af57.sync
      };
    },
    0x1549: _0x2f48ce => {
      'use strict';

      _0x2f48ce.exports = function (_0x2da612, _0x1135fb, _0x5cabb9, _0x363fec, _0x327efa) {
        _0x2da612.config = _0x1135fb;
        if (_0x5cabb9) {
          _0x2da612.code = _0x5cabb9;
        }
        _0x2da612.request = _0x363fec;
        _0x2da612.response = _0x327efa;
        _0x2da612.isAxiosError = true;
        _0x2da612.toJSON = function () {
          return {
            'message': this.message,
            'name': this.name,
            'description': this.description,
            'number': this.number,
            'fileName': this.fileName,
            'lineNumber': this.lineNumber,
            'columnNumber': this.columnNumber,
            'stack': this.stack,
            'config': this.config,
            'code': this.code,
            'status': this.response && this.response.status ? this.response.status : null
          };
        };
        return _0x2da612;
      };
    },
    0x15d8: (_0x40b5c5, _0x1f42c5, _0xea1853) => {
      'use strict';

      var _0x21fccd = _0xea1853(0x252c);
      var _0x16f05e = _0xea1853(0x1d62);
      var _0xc1ed9f = _0xea1853(0xf6c);
      var _0x21cbe4 = _0xea1853(0x2392);
      var _0x5f10be = _0xea1853(0x258f);
      var _0x2a1422 = _0xea1853(0x7dc);
      var _0x43c039 = _0xea1853(0x106a);
      var _0x47c609 = _0xea1853(0x1e53);
      var _0x1dab60 = _0xea1853(0x1b4b);
      var _0x111ee2 = _0xea1853(0x788);
      _0x40b5c5.exports = function (_0x4f3ca8) {
        return new Promise(function (_0x18b695, _0x2c3297) {
          var _0x544784;
          var _0x4177e8 = _0x4f3ca8.data;
          var _0x4344b2 = _0x4f3ca8.headers;
          var _0x5cdc41 = _0x4f3ca8.responseType;
          function _0x569ce9() {
            if (_0x4f3ca8.cancelToken) {
              _0x4f3ca8.cancelToken.unsubscribe(_0x544784);
            }
            if (_0x4f3ca8.signal) {
              _0x4f3ca8.signal.removeEventListener('abort', _0x544784);
            }
          }
          if (_0x21fccd.isFormData(_0x4177e8)) {
            delete _0x4344b2["Content-Type"];
          }
          var _0x4bbff6 = new XMLHttpRequest();
          if (_0x4f3ca8.auth) {
            var _0x457649 = _0x4f3ca8.auth.username || '';
            var _0x2b0eb2 = _0x4f3ca8.auth.password ? unescape(encodeURIComponent(_0x4f3ca8.auth.password)) : '';
            _0x4344b2.Authorization = "Basic " + btoa(_0x457649 + ':' + _0x2b0eb2);
          }
          var _0x2e26b8 = _0x5f10be(_0x4f3ca8.baseURL, _0x4f3ca8.url);
          function _0x393454() {
            if (_0x4bbff6) {
              var _0x1861e8 = "getAllResponseHeaders" in _0x4bbff6 ? _0x2a1422(_0x4bbff6.getAllResponseHeaders()) : null;
              var _0x38a0b3 = {
                'data': _0x5cdc41 && "text" !== _0x5cdc41 && "json" !== _0x5cdc41 ? _0x4bbff6.response : _0x4bbff6.responseText,
                'status': _0x4bbff6.status,
                'statusText': _0x4bbff6.statusText,
                'headers': _0x1861e8,
                'config': _0x4f3ca8,
                'request': _0x4bbff6
              };
              _0x16f05e(function (_0x41555a) {
                _0x18b695(_0x41555a);
                _0x569ce9();
              }, function (_0x4e9e46) {
                _0x2c3297(_0x4e9e46);
                _0x569ce9();
              }, _0x38a0b3);
              _0x4bbff6 = null;
            }
          }
          _0x4bbff6.open(_0x4f3ca8.method.toUpperCase(), _0x21cbe4(_0x2e26b8, _0x4f3ca8.params, _0x4f3ca8.paramsSerializer), true);
          _0x4bbff6.timeout = _0x4f3ca8.timeout;
          if ("onloadend" in _0x4bbff6) {
            _0x4bbff6.onloadend = _0x393454;
          } else {
            _0x4bbff6.onreadystatechange = function () {
              if (_0x4bbff6 && 0x4 === _0x4bbff6.readyState && (0x0 !== _0x4bbff6.status || _0x4bbff6.responseURL && 0x0 === _0x4bbff6.responseURL.indexOf('file:'))) {
                setTimeout(_0x393454);
              }
            };
          }
          _0x4bbff6.onabort = function () {
            if (_0x4bbff6) {
              _0x2c3297(_0x47c609("Request aborted", _0x4f3ca8, "ECONNABORTED", _0x4bbff6));
              _0x4bbff6 = null;
            }
          };
          _0x4bbff6.onerror = function () {
            _0x2c3297(_0x47c609("Network Error", _0x4f3ca8, null, _0x4bbff6));
            _0x4bbff6 = null;
          };
          _0x4bbff6.ontimeout = function () {
            var _0x3dbf44 = _0x4f3ca8.timeout ? "timeout of " + _0x4f3ca8.timeout + "ms exceeded" : "timeout exceeded";
            var _0x15ca14 = _0x4f3ca8.transitional || _0x1dab60.transitional;
            if (_0x4f3ca8.timeoutErrorMessage) {
              _0x3dbf44 = _0x4f3ca8.timeoutErrorMessage;
            }
            _0x2c3297(_0x47c609(_0x3dbf44, _0x4f3ca8, _0x15ca14.clarifyTimeoutError ? "ETIMEDOUT" : 'ECONNABORTED', _0x4bbff6));
            _0x4bbff6 = null;
          };
          if (_0x21fccd.isStandardBrowserEnv()) {
            var _0xb7c0fa = (_0x4f3ca8.withCredentials || _0x43c039(_0x2e26b8)) && _0x4f3ca8.xsrfCookieName ? _0xc1ed9f.read(_0x4f3ca8.xsrfCookieName) : undefined;
            if (_0xb7c0fa) {
              _0x4344b2[_0x4f3ca8.xsrfHeaderName] = _0xb7c0fa;
            }
          }
          if ("setRequestHeader" in _0x4bbff6) {
            _0x21fccd.forEach(_0x4344b2, function (_0x58b67e, _0x2dd189) {
              if (undefined === _0x4177e8 && "content-type" === _0x2dd189.toLowerCase()) {
                delete _0x4344b2[_0x2dd189];
              } else {
                _0x4bbff6.setRequestHeader(_0x2dd189, _0x58b67e);
              }
            });
          }
          if (!_0x21fccd.isUndefined(_0x4f3ca8.withCredentials)) {
            _0x4bbff6.withCredentials = !!_0x4f3ca8.withCredentials;
          }
          if (_0x5cdc41 && "json" !== _0x5cdc41) {
            _0x4bbff6.responseType = _0x4f3ca8.responseType;
          }
          if ("function" == typeof _0x4f3ca8.onDownloadProgress) {
            _0x4bbff6.addEventListener("progress", _0x4f3ca8.onDownloadProgress);
          }
          if ("function" == typeof _0x4f3ca8.onUploadProgress && _0x4bbff6.upload) {
            _0x4bbff6.upload.addEventListener("progress", _0x4f3ca8.onUploadProgress);
          }
          if (_0x4f3ca8.cancelToken || _0x4f3ca8.signal) {
            _0x544784 = function (_0x195053) {
              if (_0x4bbff6) {
                _0x2c3297(!_0x195053 || _0x195053 && _0x195053.type ? new _0x111ee2('canceled') : _0x195053);
                _0x4bbff6.abort();
                _0x4bbff6 = null;
              }
            };
            if (_0x4f3ca8.cancelToken) {
              _0x4f3ca8.cancelToken.subscribe(_0x544784);
            }
            if (_0x4f3ca8.signal) {
              if (_0x4f3ca8.signal.aborted) {
                _0x544784();
              } else {
                _0x4f3ca8.signal.addEventListener("abort", _0x544784);
              }
            }
          }
          if (!_0x4177e8) {
            _0x4177e8 = null;
          }
          _0x4bbff6.send(_0x4177e8);
        });
      };
    },
    0x1636: (_0x5b339a, _0x4b5722, _0x5a9028) => {
      'use strict';

      var _0x7dac18 = _0x5a9028(0x70c).o;
      var _0xeecba4 = _0x5a9028(0xdde).Long;
      var _0x4c3864 = _0x5a9028(0x8b8);
      var _0xaeb8ba = _0x5a9028(0xa61).Binary;
      var _0x5ade4a = _0x5a9028(0x1a9d).normalizedFunctionString;
      var _0xb875ce = /\x00/;
      var _0xd7aeef = ["$db", "$ref", "$id", "$clusterTime"];
      var _0x2a7636 = function (_0x411c8c, _0x406052, _0x585752, _0x5a2035, _0x289d63) {
        _0x411c8c[_0x5a2035++] = 0x2;
        var _0x11070c = _0x289d63 ? _0x411c8c.write(_0x406052, _0x5a2035, "ascii") : _0x411c8c.write(_0x406052, _0x5a2035, "utf8");
        _0x411c8c[(_0x5a2035 = _0x5a2035 + _0x11070c + 0x1) - 0x1] = 0x0;
        var _0x2bf80b = _0x411c8c.write(_0x585752, _0x5a2035 + 0x4, "utf8");
        _0x411c8c[_0x5a2035 + 0x3] = _0x2bf80b + 0x1 >> 0x18 & 0xff;
        _0x411c8c[_0x5a2035 + 0x2] = _0x2bf80b + 0x1 >> 0x10 & 0xff;
        _0x411c8c[_0x5a2035 + 0x1] = _0x2bf80b + 0x1 >> 0x8 & 0xff;
        _0x411c8c[_0x5a2035] = _0x2bf80b + 0x1 & 0xff;
        _0x5a2035 = _0x5a2035 + 0x4 + _0x2bf80b;
        _0x411c8c[_0x5a2035++] = 0x0;
        return _0x5a2035;
      };
      var _0x3683c9 = function (_0x17b66a, _0x1e2057, _0x37124b, _0xd34444, _0x31b19f) {
        if (Math.floor(_0x37124b) === _0x37124b && _0x37124b >= _0x16a4a2.JS_INT_MIN && _0x37124b <= _0x16a4a2.JS_INT_MAX) {
          if (_0x37124b >= _0x16a4a2.BSON_INT32_MIN && _0x37124b <= 0x7fffffff) {
            _0x17b66a[_0xd34444++] = 0x10;
            var _0x1ce13d = _0x31b19f ? _0x17b66a.write(_0x1e2057, _0xd34444, 'ascii') : _0x17b66a.write(_0x1e2057, _0xd34444, "utf8");
            _0xd34444 += _0x1ce13d;
            _0x17b66a[_0xd34444++] = 0x0;
            _0x17b66a[_0xd34444++] = 0xff & _0x37124b;
            _0x17b66a[_0xd34444++] = _0x37124b >> 0x8 & 0xff;
            _0x17b66a[_0xd34444++] = _0x37124b >> 0x10 & 0xff;
            _0x17b66a[_0xd34444++] = _0x37124b >> 0x18 & 0xff;
          } else {
            if (_0x37124b >= _0x16a4a2.JS_INT_MIN && _0x37124b <= _0x16a4a2.JS_INT_MAX) {
              _0x17b66a[_0xd34444++] = 0x1;
              _0xd34444 += _0x1ce13d = _0x31b19f ? _0x17b66a.write(_0x1e2057, _0xd34444, "ascii") : _0x17b66a.write(_0x1e2057, _0xd34444, "utf8");
              _0x17b66a[_0xd34444++] = 0x0;
              _0x7dac18(_0x17b66a, _0x37124b, _0xd34444, "little", 0x34, 0x8);
              _0xd34444 += 0x8;
            } else {
              _0x17b66a[_0xd34444++] = 0x12;
              _0xd34444 += _0x1ce13d = _0x31b19f ? _0x17b66a.write(_0x1e2057, _0xd34444, "ascii") : _0x17b66a.write(_0x1e2057, _0xd34444, "utf8");
              _0x17b66a[_0xd34444++] = 0x0;
              var _0x1feb3b = _0xeecba4.fromNumber(_0x37124b);
              var _0x15b3d4 = _0x1feb3b.getLowBits();
              var _0x454d45 = _0x1feb3b.getHighBits();
              _0x17b66a[_0xd34444++] = 0xff & _0x15b3d4;
              _0x17b66a[_0xd34444++] = _0x15b3d4 >> 0x8 & 0xff;
              _0x17b66a[_0xd34444++] = _0x15b3d4 >> 0x10 & 0xff;
              _0x17b66a[_0xd34444++] = _0x15b3d4 >> 0x18 & 0xff;
              _0x17b66a[_0xd34444++] = 0xff & _0x454d45;
              _0x17b66a[_0xd34444++] = _0x454d45 >> 0x8 & 0xff;
              _0x17b66a[_0xd34444++] = _0x454d45 >> 0x10 & 0xff;
              _0x17b66a[_0xd34444++] = _0x454d45 >> 0x18 & 0xff;
            }
          }
        } else {
          _0x17b66a[_0xd34444++] = 0x1;
          _0xd34444 += _0x1ce13d = _0x31b19f ? _0x17b66a.write(_0x1e2057, _0xd34444, "ascii") : _0x17b66a.write(_0x1e2057, _0xd34444, "utf8");
          _0x17b66a[_0xd34444++] = 0x0;
          _0x7dac18(_0x17b66a, _0x37124b, _0xd34444, 'little', 0x34, 0x8);
          _0xd34444 += 0x8;
        }
        return _0xd34444;
      };
      var _0x102289 = function (_0x3059f9, _0x2a6194, _0x9ba59a, _0x46ea36, _0x3e9d83) {
        _0x3059f9[_0x46ea36++] = 0xa;
        _0x46ea36 += _0x3e9d83 ? _0x3059f9.write(_0x2a6194, _0x46ea36, "ascii") : _0x3059f9.write(_0x2a6194, _0x46ea36, 'utf8');
        _0x3059f9[_0x46ea36++] = 0x0;
        return _0x46ea36;
      };
      var _0x1c695c = function (_0x492dba, _0x3bb3ad, _0x667bce, _0x58d8ef, _0x2f3fd7) {
        _0x492dba[_0x58d8ef++] = 0x8;
        _0x58d8ef += _0x2f3fd7 ? _0x492dba.write(_0x3bb3ad, _0x58d8ef, "ascii") : _0x492dba.write(_0x3bb3ad, _0x58d8ef, 'utf8');
        _0x492dba[_0x58d8ef++] = 0x0;
        _0x492dba[_0x58d8ef++] = _0x667bce ? 0x1 : 0x0;
        return _0x58d8ef;
      };
      var _0x1c1af5 = function (_0x24616a, _0x574fef, _0x319de0, _0x51a40a, _0x43f6a8) {
        _0x24616a[_0x51a40a++] = 0x9;
        _0x51a40a += _0x43f6a8 ? _0x24616a.write(_0x574fef, _0x51a40a, 'ascii') : _0x24616a.write(_0x574fef, _0x51a40a, "utf8");
        _0x24616a[_0x51a40a++] = 0x0;
        var _0x4b08fb = _0xeecba4.fromNumber(_0x319de0.getTime());
        var _0x2db839 = _0x4b08fb.getLowBits();
        var _0x5f3927 = _0x4b08fb.getHighBits();
        _0x24616a[_0x51a40a++] = 0xff & _0x2db839;
        _0x24616a[_0x51a40a++] = _0x2db839 >> 0x8 & 0xff;
        _0x24616a[_0x51a40a++] = _0x2db839 >> 0x10 & 0xff;
        _0x24616a[_0x51a40a++] = _0x2db839 >> 0x18 & 0xff;
        _0x24616a[_0x51a40a++] = 0xff & _0x5f3927;
        _0x24616a[_0x51a40a++] = _0x5f3927 >> 0x8 & 0xff;
        _0x24616a[_0x51a40a++] = _0x5f3927 >> 0x10 & 0xff;
        _0x24616a[_0x51a40a++] = _0x5f3927 >> 0x18 & 0xff;
        return _0x51a40a;
      };
      var _0x7564f0 = function (_0x81f26c, _0x2bb28c, _0x57c9c3, _0xc24062, _0x597d67) {
        _0x81f26c[_0xc24062++] = 0xb;
        _0xc24062 += _0x597d67 ? _0x81f26c.write(_0x2bb28c, _0xc24062, "ascii") : _0x81f26c.write(_0x2bb28c, _0xc24062, "utf8");
        _0x81f26c[_0xc24062++] = 0x0;
        if (_0x57c9c3.source && null != _0x57c9c3.source.match(_0xb875ce)) {
          throw Error("value " + _0x57c9c3.source + " must not contain null bytes");
        }
        _0xc24062 += _0x81f26c.write(_0x57c9c3.source, _0xc24062, "utf8");
        _0x81f26c[_0xc24062++] = 0x0;
        if (_0x57c9c3.global) {
          _0x81f26c[_0xc24062++] = 0x73;
        }
        if (_0x57c9c3.ignoreCase) {
          _0x81f26c[_0xc24062++] = 0x69;
        }
        if (_0x57c9c3.multiline) {
          _0x81f26c[_0xc24062++] = 0x6d;
        }
        _0x81f26c[_0xc24062++] = 0x0;
        return _0xc24062;
      };
      var _0x4b45dd = function (_0x316052, _0x37c51b, _0xb7dfcb, _0x53a573, _0x244794) {
        _0x316052[_0x53a573++] = 0xb;
        _0x53a573 += _0x244794 ? _0x316052.write(_0x37c51b, _0x53a573, 'ascii') : _0x316052.write(_0x37c51b, _0x53a573, "utf8");
        _0x316052[_0x53a573++] = 0x0;
        if (null != _0xb7dfcb.pattern.match(_0xb875ce)) {
          throw Error("pattern " + _0xb7dfcb.pattern + " must not contain null bytes");
        }
        _0x53a573 += _0x316052.write(_0xb7dfcb.pattern, _0x53a573, "utf8");
        _0x316052[_0x53a573++] = 0x0;
        _0x53a573 += _0x316052.write(_0xb7dfcb.options.split('').sort().join(''), _0x53a573, "utf8");
        _0x316052[_0x53a573++] = 0x0;
        return _0x53a573;
      };
      var _0x3380df = function (_0x193582, _0x54f608, _0x421a83, _0x1739eb, _0x41f593) {
        if (null === _0x421a83) {
          _0x193582[_0x1739eb++] = 0xa;
        } else if ("MinKey" === _0x421a83._bsontype) {
          _0x193582[_0x1739eb++] = 0xff;
        } else {
          _0x193582[_0x1739eb++] = 0x7f;
        }
        _0x1739eb += _0x41f593 ? _0x193582.write(_0x54f608, _0x1739eb, 'ascii') : _0x193582.write(_0x54f608, _0x1739eb, "utf8");
        _0x193582[_0x1739eb++] = 0x0;
        return _0x1739eb;
      };
      var _0x309cb9 = function (_0x2afa56, _0x3a0363, _0x1c2ebf, _0x482850, _0x25c137) {
        _0x2afa56[_0x482850++] = 0x7;
        _0x482850 += _0x25c137 ? _0x2afa56.write(_0x3a0363, _0x482850, 'ascii') : _0x2afa56.write(_0x3a0363, _0x482850, "utf8");
        _0x2afa56[_0x482850++] = 0x0;
        if ("string" == typeof _0x1c2ebf.id) {
          _0x2afa56.write(_0x1c2ebf.id, _0x482850, "binary");
        } else {
          if (!_0x1c2ebf.id || !_0x1c2ebf.id.copy) {
            throw new Error("object [" + JSON.stringify(_0x1c2ebf) + "] is not a valid ObjectId");
          }
          _0x1c2ebf.id.copy(_0x2afa56, _0x482850, 0x0, 0xc);
        }
        return _0x482850 + 0xc;
      };
      var _0x462e69 = function (_0x5cbeb7, _0x136574, _0x3dbd52, _0x1cd006, _0x38b3b1) {
        _0x5cbeb7[_0x1cd006++] = 0x5;
        _0x1cd006 += _0x38b3b1 ? _0x5cbeb7.write(_0x136574, _0x1cd006, 'ascii') : _0x5cbeb7.write(_0x136574, _0x1cd006, "utf8");
        _0x5cbeb7[_0x1cd006++] = 0x0;
        var _0x224bbd = _0x3dbd52.length;
        _0x5cbeb7[_0x1cd006++] = 0xff & _0x224bbd;
        _0x5cbeb7[_0x1cd006++] = _0x224bbd >> 0x8 & 0xff;
        _0x5cbeb7[_0x1cd006++] = _0x224bbd >> 0x10 & 0xff;
        _0x5cbeb7[_0x1cd006++] = _0x224bbd >> 0x18 & 0xff;
        _0x5cbeb7[_0x1cd006++] = 0x0;
        _0x3dbd52.copy(_0x5cbeb7, _0x1cd006, 0x0, _0x224bbd);
        return _0x1cd006 + _0x224bbd;
      };
      var _0x58e200 = function (_0x59d300, _0x3513c9, _0x5d44e3, _0x51db3c, _0x5e8dbc, _0x3cb7f2, _0x433520, _0x1930bb, _0x392925, _0x4ce03f) {
        for (var _0x1fe126 = 0x0; _0x1fe126 < _0x4ce03f.length; _0x1fe126++) {
          if (_0x4ce03f[_0x1fe126] === _0x5d44e3) {
            throw new Error("cyclic dependency detected");
          }
        }
        _0x4ce03f.push(_0x5d44e3);
        _0x59d300[_0x51db3c++] = Array.isArray(_0x5d44e3) ? 0x4 : 0x3;
        _0x51db3c += _0x392925 ? _0x59d300.write(_0x3513c9, _0x51db3c, "ascii") : _0x59d300.write(_0x3513c9, _0x51db3c, 'utf8');
        _0x59d300[_0x51db3c++] = 0x0;
        var _0x42d8e0 = _0x671f0f(_0x59d300, _0x5d44e3, _0x5e8dbc, _0x51db3c, _0x3cb7f2 + 0x1, _0x433520, _0x1930bb, _0x4ce03f);
        _0x4ce03f.pop();
        return _0x42d8e0;
      };
      var _0x286e0c = function (_0x5926f5, _0x16fcb8, _0x37d0f2, _0x3c8c95, _0xf54456) {
        _0x5926f5[_0x3c8c95++] = 0x13;
        _0x3c8c95 += _0xf54456 ? _0x5926f5.write(_0x16fcb8, _0x3c8c95, "ascii") : _0x5926f5.write(_0x16fcb8, _0x3c8c95, 'utf8');
        _0x5926f5[_0x3c8c95++] = 0x0;
        _0x37d0f2.bytes.copy(_0x5926f5, _0x3c8c95, 0x0, 0x10);
        return _0x3c8c95 + 0x10;
      };
      var _0x5aadef = function (_0x16ea1e, _0x20d5e7, _0x1f1ddc, _0x1f7d74, _0x17e683) {
        _0x16ea1e[_0x1f7d74++] = "Long" === _0x1f1ddc._bsontype ? 0x12 : 0x11;
        _0x1f7d74 += _0x17e683 ? _0x16ea1e.write(_0x20d5e7, _0x1f7d74, "ascii") : _0x16ea1e.write(_0x20d5e7, _0x1f7d74, 'utf8');
        _0x16ea1e[_0x1f7d74++] = 0x0;
        var _0x10b9d7 = _0x1f1ddc.getLowBits();
        var _0xfc4c05 = _0x1f1ddc.getHighBits();
        _0x16ea1e[_0x1f7d74++] = 0xff & _0x10b9d7;
        _0x16ea1e[_0x1f7d74++] = _0x10b9d7 >> 0x8 & 0xff;
        _0x16ea1e[_0x1f7d74++] = _0x10b9d7 >> 0x10 & 0xff;
        _0x16ea1e[_0x1f7d74++] = _0x10b9d7 >> 0x18 & 0xff;
        _0x16ea1e[_0x1f7d74++] = 0xff & _0xfc4c05;
        _0x16ea1e[_0x1f7d74++] = _0xfc4c05 >> 0x8 & 0xff;
        _0x16ea1e[_0x1f7d74++] = _0xfc4c05 >> 0x10 & 0xff;
        _0x16ea1e[_0x1f7d74++] = _0xfc4c05 >> 0x18 & 0xff;
        return _0x1f7d74;
      };
      var _0x1f541f = function (_0x3a9c93, _0x11c391, _0x176041, _0x4fcdca, _0x13da48) {
        _0x3a9c93[_0x4fcdca++] = 0x10;
        _0x4fcdca += _0x13da48 ? _0x3a9c93.write(_0x11c391, _0x4fcdca, "ascii") : _0x3a9c93.write(_0x11c391, _0x4fcdca, 'utf8');
        _0x3a9c93[_0x4fcdca++] = 0x0;
        _0x3a9c93[_0x4fcdca++] = 0xff & _0x176041;
        _0x3a9c93[_0x4fcdca++] = _0x176041 >> 0x8 & 0xff;
        _0x3a9c93[_0x4fcdca++] = _0x176041 >> 0x10 & 0xff;
        _0x3a9c93[_0x4fcdca++] = _0x176041 >> 0x18 & 0xff;
        return _0x4fcdca;
      };
      var _0x22ecbe = function (_0x6a3880, _0x152d29, _0x47d9d9, _0x5524ae, _0x10e9f9) {
        _0x6a3880[_0x5524ae++] = 0x1;
        _0x5524ae += _0x10e9f9 ? _0x6a3880.write(_0x152d29, _0x5524ae, "ascii") : _0x6a3880.write(_0x152d29, _0x5524ae, "utf8");
        _0x6a3880[_0x5524ae++] = 0x0;
        _0x7dac18(_0x6a3880, _0x47d9d9, _0x5524ae, "little", 0x34, 0x8);
        return _0x5524ae + 0x8;
      };
      var _0x2b517d = function (_0x21666a, _0x52ef91, _0x24c9bf, _0x2f54ec, _0x2b4207, _0x3e3a2c, _0x506b6a) {
        _0x21666a[_0x2f54ec++] = 0xd;
        _0x2f54ec += _0x506b6a ? _0x21666a.write(_0x52ef91, _0x2f54ec, "ascii") : _0x21666a.write(_0x52ef91, _0x2f54ec, "utf8");
        _0x21666a[_0x2f54ec++] = 0x0;
        var _0x32f046 = _0x5ade4a(_0x24c9bf);
        var _0xec39f8 = _0x21666a.write(_0x32f046, _0x2f54ec + 0x4, 'utf8') + 0x1;
        _0x21666a[_0x2f54ec] = 0xff & _0xec39f8;
        _0x21666a[_0x2f54ec + 0x1] = _0xec39f8 >> 0x8 & 0xff;
        _0x21666a[_0x2f54ec + 0x2] = _0xec39f8 >> 0x10 & 0xff;
        _0x21666a[_0x2f54ec + 0x3] = _0xec39f8 >> 0x18 & 0xff;
        _0x2f54ec = _0x2f54ec + 0x4 + _0xec39f8 - 0x1;
        _0x21666a[_0x2f54ec++] = 0x0;
        return _0x2f54ec;
      };
      var _0x25127e = function (_0x1025de, _0x16da10, _0xacb72f, _0x34502c, _0x3d5403, _0x5d9b00, _0x1388f9, _0x155d9b, _0x43735d) {
        if (_0xacb72f.scope && 'object' == typeof _0xacb72f.scope) {
          _0x1025de[_0x34502c++] = 0xf;
          var _0x3da72c = _0x43735d ? _0x1025de.write(_0x16da10, _0x34502c, "ascii") : _0x1025de.write(_0x16da10, _0x34502c, "utf8");
          _0x34502c += _0x3da72c;
          _0x1025de[_0x34502c++] = 0x0;
          var _0x258918 = _0x34502c;
          var _0x26b370 = "string" == typeof _0xacb72f.code ? _0xacb72f.code : _0xacb72f.code.toString();
          _0x34502c += 0x4;
          var _0x5f0785 = _0x1025de.write(_0x26b370, _0x34502c + 0x4, "utf8") + 0x1;
          _0x1025de[_0x34502c] = 0xff & _0x5f0785;
          _0x1025de[_0x34502c + 0x1] = _0x5f0785 >> 0x8 & 0xff;
          _0x1025de[_0x34502c + 0x2] = _0x5f0785 >> 0x10 & 0xff;
          _0x1025de[_0x34502c + 0x3] = _0x5f0785 >> 0x18 & 0xff;
          _0x1025de[_0x34502c + 0x4 + _0x5f0785 - 0x1] = 0x0;
          _0x34502c = _0x34502c + _0x5f0785 + 0x4;
          var _0x20d5f1 = _0x671f0f(_0x1025de, _0xacb72f.scope, _0x3d5403, _0x34502c, _0x5d9b00 + 0x1, _0x1388f9, _0x155d9b);
          _0x34502c = _0x20d5f1 - 0x1;
          var _0x3360d0 = _0x20d5f1 - _0x258918;
          _0x1025de[_0x258918++] = 0xff & _0x3360d0;
          _0x1025de[_0x258918++] = _0x3360d0 >> 0x8 & 0xff;
          _0x1025de[_0x258918++] = _0x3360d0 >> 0x10 & 0xff;
          _0x1025de[_0x258918++] = _0x3360d0 >> 0x18 & 0xff;
          _0x1025de[_0x34502c++] = 0x0;
        } else {
          _0x1025de[_0x34502c++] = 0xd;
          _0x34502c += _0x3da72c = _0x43735d ? _0x1025de.write(_0x16da10, _0x34502c, "ascii") : _0x1025de.write(_0x16da10, _0x34502c, 'utf8');
          _0x1025de[_0x34502c++] = 0x0;
          _0x26b370 = _0xacb72f.code.toString();
          var _0x5663fe = _0x1025de.write(_0x26b370, _0x34502c + 0x4, "utf8") + 0x1;
          _0x1025de[_0x34502c] = 0xff & _0x5663fe;
          _0x1025de[_0x34502c + 0x1] = _0x5663fe >> 0x8 & 0xff;
          _0x1025de[_0x34502c + 0x2] = _0x5663fe >> 0x10 & 0xff;
          _0x1025de[_0x34502c + 0x3] = _0x5663fe >> 0x18 & 0xff;
          _0x34502c = _0x34502c + 0x4 + _0x5663fe - 0x1;
          _0x1025de[_0x34502c++] = 0x0;
        }
        return _0x34502c;
      };
      var _0x4adeef = function (_0x2f1308, _0x5b02ee, _0x460eb6, _0x5e8c10, _0x27568d) {
        _0x2f1308[_0x5e8c10++] = 0x5;
        _0x5e8c10 += _0x27568d ? _0x2f1308.write(_0x5b02ee, _0x5e8c10, "ascii") : _0x2f1308.write(_0x5b02ee, _0x5e8c10, "utf8");
        _0x2f1308[_0x5e8c10++] = 0x0;
        var _0x4bddae = _0x460eb6.value(true);
        var _0x46c19c = _0x460eb6.position;
        if (_0x460eb6.sub_type === _0xaeb8ba.SUBTYPE_BYTE_ARRAY) {
          _0x46c19c += 0x4;
        }
        _0x2f1308[_0x5e8c10++] = 0xff & _0x46c19c;
        _0x2f1308[_0x5e8c10++] = _0x46c19c >> 0x8 & 0xff;
        _0x2f1308[_0x5e8c10++] = _0x46c19c >> 0x10 & 0xff;
        _0x2f1308[_0x5e8c10++] = _0x46c19c >> 0x18 & 0xff;
        _0x2f1308[_0x5e8c10++] = _0x460eb6.sub_type;
        if (_0x460eb6.sub_type === _0xaeb8ba.SUBTYPE_BYTE_ARRAY) {
          _0x46c19c -= 0x4;
          _0x2f1308[_0x5e8c10++] = 0xff & _0x46c19c;
          _0x2f1308[_0x5e8c10++] = _0x46c19c >> 0x8 & 0xff;
          _0x2f1308[_0x5e8c10++] = _0x46c19c >> 0x10 & 0xff;
          _0x2f1308[_0x5e8c10++] = _0x46c19c >> 0x18 & 0xff;
        }
        _0x4bddae.copy(_0x2f1308, _0x5e8c10, 0x0, _0x460eb6.position);
        return _0x5e8c10 + _0x460eb6.position;
      };
      var _0x20e021 = function (_0x235e7a, _0x136b63, _0x5837f7, _0x188a32, _0x737958) {
        _0x235e7a[_0x188a32++] = 0xe;
        _0x188a32 += _0x737958 ? _0x235e7a.write(_0x136b63, _0x188a32, "ascii") : _0x235e7a.write(_0x136b63, _0x188a32, "utf8");
        _0x235e7a[_0x188a32++] = 0x0;
        var _0x309936 = _0x235e7a.write(_0x5837f7.value, _0x188a32 + 0x4, "utf8") + 0x1;
        _0x235e7a[_0x188a32] = 0xff & _0x309936;
        _0x235e7a[_0x188a32 + 0x1] = _0x309936 >> 0x8 & 0xff;
        _0x235e7a[_0x188a32 + 0x2] = _0x309936 >> 0x10 & 0xff;
        _0x235e7a[_0x188a32 + 0x3] = _0x309936 >> 0x18 & 0xff;
        _0x188a32 = _0x188a32 + 0x4 + _0x309936 - 0x1;
        _0x235e7a[_0x188a32++] = 0x0;
        return _0x188a32;
      };
      var _0x377f09 = function (_0x4a4f2d, _0x36d70, _0x28d409, _0x392ef1, _0x10b5d7, _0x354b9f, _0x1d3db9) {
        _0x4a4f2d[_0x392ef1++] = 0x3;
        _0x392ef1 += _0x1d3db9 ? _0x4a4f2d.write(_0x36d70, _0x392ef1, "ascii") : _0x4a4f2d.write(_0x36d70, _0x392ef1, "utf8");
        _0x4a4f2d[_0x392ef1++] = 0x0;
        var _0x90b5d8;
        var _0x59a6a7 = _0x392ef1;
        var _0xa587b7 = (_0x90b5d8 = null != _0x28d409.db ? _0x671f0f(_0x4a4f2d, {
          '$ref': _0x28d409.namespace,
          '$id': _0x28d409.oid,
          '$db': _0x28d409.db
        }, false, _0x392ef1, _0x10b5d7 + 0x1, _0x354b9f) : _0x671f0f(_0x4a4f2d, {
          '$ref': _0x28d409.namespace,
          '$id': _0x28d409.oid
        }, false, _0x392ef1, _0x10b5d7 + 0x1, _0x354b9f)) - _0x59a6a7;
        _0x4a4f2d[_0x59a6a7++] = 0xff & _0xa587b7;
        _0x4a4f2d[_0x59a6a7++] = _0xa587b7 >> 0x8 & 0xff;
        _0x4a4f2d[_0x59a6a7++] = _0xa587b7 >> 0x10 & 0xff;
        _0x4a4f2d[_0x59a6a7++] = _0xa587b7 >> 0x18 & 0xff;
        return _0x90b5d8;
      };
      var _0x671f0f = function (_0xcb0565, _0xda0288, _0x393037, _0x1cc8fa, _0x148aad, _0x485aff, _0x5174bc, _0x4c9810) {
        _0x1cc8fa = _0x1cc8fa || 0x0;
        (_0x4c9810 = _0x4c9810 || []).push(_0xda0288);
        var _0x1b3c7c = _0x1cc8fa + 0x4;
        if (Array.isArray(_0xda0288)) {
          for (var _0x1ba007 = 0x0; _0x1ba007 < _0xda0288.length; _0x1ba007++) {
            var _0x40aa6e = '' + _0x1ba007;
            var _0x51660d = _0xda0288[_0x1ba007];
            if (_0x51660d && _0x51660d.toBSON) {
              if ('function' != typeof _0x51660d.toBSON) {
                throw new Error("toBSON is not a function");
              }
              _0x51660d = _0x51660d.toBSON();
            }
            var _0x5e8373 = typeof _0x51660d;
            if ("string" === _0x5e8373) {
              _0x1b3c7c = _0x2a7636(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
            } else {
              if ('number' === _0x5e8373) {
                _0x1b3c7c = _0x3683c9(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
              } else {
                if ("bigint" === _0x5e8373) {
                  throw new TypeError("Unsupported type BigInt, please use Decimal128");
                }
                if ("boolean" === _0x5e8373) {
                  _0x1b3c7c = _0x1c695c(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                } else {
                  if (_0x51660d instanceof Date || "object" == typeof _0x51660d && "[object Date]" === Object.prototype.toString.call(_0x51660d)) {
                    _0x1b3c7c = _0x1c1af5(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                  } else {
                    if (undefined === _0x51660d) {
                      _0x1b3c7c = _0x102289(_0xcb0565, _0x40aa6e, 0x0, _0x1b3c7c, true);
                    } else {
                      if (null === _0x51660d) {
                        _0x1b3c7c = _0x102289(_0xcb0565, _0x40aa6e, 0x0, _0x1b3c7c, true);
                      } else {
                        if ("ObjectID" === _0x51660d._bsontype || 'ObjectId' === _0x51660d._bsontype) {
                          _0x1b3c7c = _0x309cb9(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                        } else {
                          if (Buffer.isBuffer(_0x51660d)) {
                            _0x1b3c7c = _0x462e69(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                          } else {
                            if (_0x51660d instanceof RegExp || "[object RegExp]" === Object.prototype.toString.call(_0x51660d)) {
                              _0x1b3c7c = _0x7564f0(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                            } else {
                              if ('object' === _0x5e8373 && null == _0x51660d._bsontype) {
                                _0x1b3c7c = _0x58e200(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x393037, _0x148aad, _0x485aff, _0x5174bc, true, _0x4c9810);
                              } else {
                                if ('object' === _0x5e8373 && 'Decimal128' === _0x51660d._bsontype) {
                                  _0x1b3c7c = _0x286e0c(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                                } else {
                                  if ("Long" === _0x51660d._bsontype || 'Timestamp' === _0x51660d._bsontype) {
                                    _0x1b3c7c = _0x5aadef(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                                  } else {
                                    if ("Double" === _0x51660d._bsontype) {
                                      _0x1b3c7c = _0x22ecbe(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                                    } else {
                                      if ("function" == typeof _0x51660d && _0x485aff) {
                                        _0x1b3c7c = _0x2b517d(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, 0x0, 0x0, _0x485aff);
                                      } else {
                                        if ("Code" === _0x51660d._bsontype) {
                                          _0x1b3c7c = _0x25127e(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x393037, _0x148aad, _0x485aff, _0x5174bc, true);
                                        } else {
                                          if ("Binary" === _0x51660d._bsontype) {
                                            _0x1b3c7c = _0x4adeef(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                                          } else {
                                            if ("Symbol" === _0x51660d._bsontype) {
                                              _0x1b3c7c = _0x20e021(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                                            } else {
                                              if ("DBRef" === _0x51660d._bsontype) {
                                                _0x1b3c7c = _0x377f09(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x148aad, _0x485aff, true);
                                              } else {
                                                if ("BSONRegExp" === _0x51660d._bsontype) {
                                                  _0x1b3c7c = _0x4b45dd(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                                                } else {
                                                  if ("Int32" === _0x51660d._bsontype) {
                                                    _0x1b3c7c = _0x1f541f(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                                                  } else {
                                                    if ("MinKey" === _0x51660d._bsontype || "MaxKey" === _0x51660d._bsontype) {
                                                      _0x1b3c7c = _0x3380df(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, true);
                                                    } else {
                                                      if (undefined !== _0x51660d._bsontype) {
                                                        throw new TypeError("Unrecognized or invalid _bsontype: " + _0x51660d._bsontype);
                                                      }
                                                    }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        } else {
          if (_0xda0288 instanceof _0x4c3864) {
            var _0x5cf8a5 = _0xda0288.entries();
            for (var _0x5157a3 = false; !_0x5157a3;) {
              var _0x2ea289 = _0x5cf8a5.next();
              if (!(_0x5157a3 = _0x2ea289.done)) {
                _0x40aa6e = _0x2ea289.value[0x0];
                _0x5e8373 = typeof (_0x51660d = _0x2ea289.value[0x1]);
                if ('string' == typeof _0x40aa6e && -0x1 === _0xd7aeef.indexOf(_0x40aa6e)) {
                  if (null != _0x40aa6e.match(_0xb875ce)) {
                    throw Error("key " + _0x40aa6e + " must not contain null bytes");
                  }
                  if (_0x393037) {
                    if ('$' === _0x40aa6e[0x0]) {
                      throw Error("key " + _0x40aa6e + " must not start with '$'");
                    }
                    if (~_0x40aa6e.indexOf('.')) {
                      throw Error("key " + _0x40aa6e + " must not contain '.'");
                    }
                  }
                }
                if ('string' === _0x5e8373) {
                  _0x1b3c7c = _0x2a7636(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                } else {
                  if ('number' === _0x5e8373) {
                    _0x1b3c7c = _0x3683c9(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                  } else {
                    if ("bigint" === _0x5e8373) {
                      throw new TypeError("Unsupported type BigInt, please use Decimal128");
                    }
                    if ('boolean' === _0x5e8373) {
                      _0x1b3c7c = _0x1c695c(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                    } else {
                      if (_0x51660d instanceof Date || "object" == typeof _0x51660d && "[object Date]" === Object.prototype.toString.call(_0x51660d)) {
                        _0x1b3c7c = _0x1c1af5(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                      } else {
                        if (null === _0x51660d || undefined === _0x51660d && false === _0x5174bc) {
                          _0x1b3c7c = _0x102289(_0xcb0565, _0x40aa6e, 0x0, _0x1b3c7c);
                        } else {
                          if ("ObjectID" === _0x51660d._bsontype || "ObjectId" === _0x51660d._bsontype) {
                            _0x1b3c7c = _0x309cb9(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                          } else {
                            if (Buffer.isBuffer(_0x51660d)) {
                              _0x1b3c7c = _0x462e69(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                            } else {
                              if (_0x51660d instanceof RegExp || "[object RegExp]" === Object.prototype.toString.call(_0x51660d)) {
                                _0x1b3c7c = _0x7564f0(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                              } else {
                                if ('object' === _0x5e8373 && null == _0x51660d._bsontype) {
                                  _0x1b3c7c = _0x58e200(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x393037, _0x148aad, _0x485aff, _0x5174bc, false, _0x4c9810);
                                } else {
                                  if ('object' === _0x5e8373 && "Decimal128" === _0x51660d._bsontype) {
                                    _0x1b3c7c = _0x286e0c(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                  } else {
                                    if ("Long" === _0x51660d._bsontype || "Timestamp" === _0x51660d._bsontype) {
                                      _0x1b3c7c = _0x5aadef(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                    } else {
                                      if ("Double" === _0x51660d._bsontype) {
                                        _0x1b3c7c = _0x22ecbe(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                      } else {
                                        if ("Code" === _0x51660d._bsontype) {
                                          _0x1b3c7c = _0x25127e(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x393037, _0x148aad, _0x485aff, _0x5174bc);
                                        } else {
                                          if ("function" == typeof _0x51660d && _0x485aff) {
                                            _0x1b3c7c = _0x2b517d(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, 0x0, 0x0, _0x485aff);
                                          } else {
                                            if ("Binary" === _0x51660d._bsontype) {
                                              _0x1b3c7c = _0x4adeef(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                            } else {
                                              if ("Symbol" === _0x51660d._bsontype) {
                                                _0x1b3c7c = _0x20e021(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                              } else {
                                                if ("DBRef" === _0x51660d._bsontype) {
                                                  _0x1b3c7c = _0x377f09(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x148aad, _0x485aff);
                                                } else {
                                                  if ("BSONRegExp" === _0x51660d._bsontype) {
                                                    _0x1b3c7c = _0x4b45dd(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                                  } else {
                                                    if ("Int32" === _0x51660d._bsontype) {
                                                      _0x1b3c7c = _0x1f541f(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                                    } else {
                                                      if ("MinKey" === _0x51660d._bsontype || "MaxKey" === _0x51660d._bsontype) {
                                                        _0x1b3c7c = _0x3380df(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                                      } else {
                                                        if (undefined !== _0x51660d._bsontype) {
                                                          throw new TypeError("Unrecognized or invalid _bsontype: " + _0x51660d._bsontype);
                                                        }
                                                      }
                                                    }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          } else {
            if (_0xda0288.toBSON) {
              if ("function" != typeof _0xda0288.toBSON) {
                throw new Error("toBSON is not a function");
              }
              if (null != (_0xda0288 = _0xda0288.toBSON()) && "object" != typeof _0xda0288) {
                throw new Error("toBSON function did not return an object");
              }
            }
            for (_0x40aa6e in _0xda0288) {
              if ((_0x51660d = _0xda0288[_0x40aa6e]) && _0x51660d.toBSON) {
                if ("function" != typeof _0x51660d.toBSON) {
                  throw new Error("toBSON is not a function");
                }
                _0x51660d = _0x51660d.toBSON();
              }
              _0x5e8373 = typeof _0x51660d;
              if ("string" == typeof _0x40aa6e && -0x1 === _0xd7aeef.indexOf(_0x40aa6e)) {
                if (null != _0x40aa6e.match(_0xb875ce)) {
                  throw Error("key " + _0x40aa6e + " must not contain null bytes");
                }
                if (_0x393037) {
                  if ('$' === _0x40aa6e[0x0]) {
                    throw Error("key " + _0x40aa6e + " must not start with '$'");
                  }
                  if (~_0x40aa6e.indexOf('.')) {
                    throw Error("key " + _0x40aa6e + " must not contain '.'");
                  }
                }
              }
              if ("string" === _0x5e8373) {
                _0x1b3c7c = _0x2a7636(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
              } else {
                if ("number" === _0x5e8373) {
                  _0x1b3c7c = _0x3683c9(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                } else {
                  if ("bigint" === _0x5e8373) {
                    throw new TypeError("Unsupported type BigInt, please use Decimal128");
                  }
                  if ("boolean" === _0x5e8373) {
                    _0x1b3c7c = _0x1c695c(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                  } else {
                    if (_0x51660d instanceof Date || "object" == typeof _0x51660d && "[object Date]" === Object.prototype.toString.call(_0x51660d)) {
                      _0x1b3c7c = _0x1c1af5(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                    } else {
                      if (undefined === _0x51660d) {
                        if (false === _0x5174bc) {
                          _0x1b3c7c = _0x102289(_0xcb0565, _0x40aa6e, 0x0, _0x1b3c7c);
                        }
                      } else {
                        if (null === _0x51660d) {
                          _0x1b3c7c = _0x102289(_0xcb0565, _0x40aa6e, 0x0, _0x1b3c7c);
                        } else {
                          if ("ObjectID" === _0x51660d._bsontype || "ObjectId" === _0x51660d._bsontype) {
                            _0x1b3c7c = _0x309cb9(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                          } else {
                            if (Buffer.isBuffer(_0x51660d)) {
                              _0x1b3c7c = _0x462e69(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                            } else {
                              if (_0x51660d instanceof RegExp || "[object RegExp]" === Object.prototype.toString.call(_0x51660d)) {
                                _0x1b3c7c = _0x7564f0(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                              } else {
                                if ("object" === _0x5e8373 && null == _0x51660d._bsontype) {
                                  _0x1b3c7c = _0x58e200(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x393037, _0x148aad, _0x485aff, _0x5174bc, false, _0x4c9810);
                                } else {
                                  if ("object" === _0x5e8373 && "Decimal128" === _0x51660d._bsontype) {
                                    _0x1b3c7c = _0x286e0c(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                  } else {
                                    if ("Long" === _0x51660d._bsontype || "Timestamp" === _0x51660d._bsontype) {
                                      _0x1b3c7c = _0x5aadef(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                    } else {
                                      if ("Double" === _0x51660d._bsontype) {
                                        _0x1b3c7c = _0x22ecbe(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                      } else {
                                        if ("Code" === _0x51660d._bsontype) {
                                          _0x1b3c7c = _0x25127e(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x393037, _0x148aad, _0x485aff, _0x5174bc);
                                        } else {
                                          if ("function" == typeof _0x51660d && _0x485aff) {
                                            _0x1b3c7c = _0x2b517d(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, 0x0, 0x0, _0x485aff);
                                          } else {
                                            if ('Binary' === _0x51660d._bsontype) {
                                              _0x1b3c7c = _0x4adeef(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                            } else {
                                              if ("Symbol" === _0x51660d._bsontype) {
                                                _0x1b3c7c = _0x20e021(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                              } else {
                                                if ("DBRef" === _0x51660d._bsontype) {
                                                  _0x1b3c7c = _0x377f09(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c, _0x148aad, _0x485aff);
                                                } else {
                                                  if ('BSONRegExp' === _0x51660d._bsontype) {
                                                    _0x1b3c7c = _0x4b45dd(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                                  } else {
                                                    if ("Int32" === _0x51660d._bsontype) {
                                                      _0x1b3c7c = _0x1f541f(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                                    } else {
                                                      if ("MinKey" === _0x51660d._bsontype || "MaxKey" === _0x51660d._bsontype) {
                                                        _0x1b3c7c = _0x3380df(_0xcb0565, _0x40aa6e, _0x51660d, _0x1b3c7c);
                                                      } else {
                                                        if (undefined !== _0x51660d._bsontype) {
                                                          throw new TypeError("Unrecognized or invalid _bsontype: " + _0x51660d._bsontype);
                                                        }
                                                      }
                                                    }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        _0x4c9810.pop();
        _0xcb0565[_0x1b3c7c++] = 0x0;
        var _0xab749a = _0x1b3c7c - _0x1cc8fa;
        _0xcb0565[_0x1cc8fa++] = 0xff & _0xab749a;
        _0xcb0565[_0x1cc8fa++] = _0xab749a >> 0x8 & 0xff;
        _0xcb0565[_0x1cc8fa++] = _0xab749a >> 0x10 & 0xff;
        _0xcb0565[_0x1cc8fa++] = _0xab749a >> 0x18 & 0xff;
        return _0x1b3c7c;
      };
      var _0x16a4a2 = {
        'BSON_DATA_NUMBER': 0x1,
        'BSON_DATA_STRING': 0x2,
        'BSON_DATA_OBJECT': 0x3,
        'BSON_DATA_ARRAY': 0x4,
        'BSON_DATA_BINARY': 0x5,
        'BSON_DATA_UNDEFINED': 0x6,
        'BSON_DATA_OID': 0x7,
        'BSON_DATA_BOOLEAN': 0x8,
        'BSON_DATA_DATE': 0x9,
        'BSON_DATA_NULL': 0xa,
        'BSON_DATA_REGEXP': 0xb,
        'BSON_DATA_CODE': 0xd,
        'BSON_DATA_SYMBOL': 0xe,
        'BSON_DATA_CODE_W_SCOPE': 0xf,
        'BSON_DATA_INT': 0x10,
        'BSON_DATA_TIMESTAMP': 0x11,
        'BSON_DATA_LONG': 0x12,
        'BSON_DATA_DECIMAL128': 0x13,
        'BSON_DATA_MIN_KEY': 0xff,
        'BSON_DATA_MAX_KEY': 0x7f,
        'BSON_BINARY_SUBTYPE_DEFAULT': 0x0,
        'BSON_BINARY_SUBTYPE_FUNCTION': 0x1,
        'BSON_BINARY_SUBTYPE_BYTE_ARRAY': 0x2,
        'BSON_BINARY_SUBTYPE_UUID': 0x3,
        'BSON_BINARY_SUBTYPE_MD5': 0x4,
        'BSON_BINARY_SUBTYPE_USER_DEFINED': 0x80,
        'BSON_INT32_MAX': 0x7fffffff,
        'BSON_INT32_MIN': -0x80000000
      };
      _0x16a4a2.BSON_INT64_MAX = Math.pow(0x2, 0x3f) - 0x1;
      _0x16a4a2.BSON_INT64_MIN = -Math.pow(0x2, 0x3f);
      _0x16a4a2.JS_INT_MAX = 0x20000000000000;
      _0x16a4a2.JS_INT_MIN = -0x20000000000000;
      _0x5b339a.exports = _0x671f0f;
    },
    0x163c: _0x5ad930 => {
      'use strict';

      _0x5ad930.exports = require("https");
    },
    0x1679: (_0x3e0fc3, _0x5ad48f, _0x3b221d) => {
      if ("undefined" == typeof process || "renderer" === process.type || true === process.browser || process.__nwjs) {
        _0x3e0fc3.exports = _0x3b221d(0x1e99);
      } else {
        _0x3e0fc3.exports = _0x3b221d(0x1791);
      }
    },
    0x16fc: _0x3d9ee3 => {
      'use strict';

      _0x3d9ee3.exports = (_0x5a00b1, _0x23451d = process.argv) => {
        const _0x210adc = _0x5a00b1.startsWith('-') ? '' : 0x1 === _0x5a00b1.length ? '-' : '--';
        const _0x498752 = _0x23451d.indexOf(_0x210adc + _0x5a00b1);
        const _0x5d9556 = _0x23451d.indexOf('--');
        return -0x1 !== _0x498752 && (-0x1 === _0x5d9556 || _0x498752 < _0x5d9556);
      };
    },
    0x1791: (_0x21d187, _0x53bd41, _0x7c036f) => {
      const _0x5a8b7b = _0x7c036f(0x7e2);
      const _0x2e48a1 = _0x7c036f(0x233f);
      _0x53bd41.init = function (_0x4222f7) {
        _0x4222f7.inspectOpts = {};
        const _0x4cd1ae = Object.keys(_0x53bd41.inspectOpts);
        for (let _0x5917b9 = 0x0; _0x5917b9 < _0x4cd1ae.length; _0x5917b9++) {
          _0x4222f7.inspectOpts[_0x4cd1ae[_0x5917b9]] = _0x53bd41.inspectOpts[_0x4cd1ae[_0x5917b9]];
        }
      };
      _0x53bd41.log = function (..._0x3604f7) {
        return process.stderr.write(_0x2e48a1.formatWithOptions(_0x53bd41.inspectOpts, ..._0x3604f7) + "\n");
      };
      _0x53bd41.formatArgs = function (_0x597f69) {
        const {
          namespace: _0x33d002,
          useColors: _0x31c291
        } = this;
        if (_0x31c291) {
          const _0x486742 = this.color;
          const _0x46f473 = "[3" + (_0x486742 < 0x8 ? _0x486742 : "8;5;" + _0x486742);
          const _0x3988ea = "  " + _0x46f473 + ";1m" + _0x33d002 + " [0m";
          _0x597f69[0x0] = _0x3988ea + _0x597f69[0x0].split("\n").join("\n" + _0x3988ea);
          _0x597f69.push(_0x46f473 + 'm+' + _0x21d187.exports.humanize(this.diff) + "[0m");
        } else {
          _0x597f69[0x0] = (_0x53bd41.inspectOpts.hideDate ? '' : new Date().toISOString() + " ") + _0x33d002 + " " + _0x597f69[0x0];
        }
      };
      _0x53bd41.save = function (_0x2d69cc) {
        if (_0x2d69cc) {
          process.env.DEBUG = _0x2d69cc;
        } else {
          delete process.env.DEBUG;
        }
      };
      _0x53bd41.load = function () {
        return process.env.DEBUG;
      };
      _0x53bd41.useColors = function () {
        return "colors" in _0x53bd41.inspectOpts ? Boolean(_0x53bd41.inspectOpts.colors) : _0x5a8b7b.isatty(process.stderr.fd);
      };
      _0x53bd41.destroy = _0x2e48a1.deprecate(() => {}, "Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");
      _0x53bd41.colors = [0x6, 0x2, 0x3, 0x4, 0x5, 0x1];
      try {
        const _0x278d05 = _0x7c036f(0x1e07);
        if (_0x278d05 && (_0x278d05.stderr || _0x278d05).level >= 0x2) {
          _0x53bd41.colors = [0x14, 0x15, 0x1a, 0x1b, 0x20, 0x21, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x38, 0x39, 0x3e, 0x3f, 0x44, 0x45, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x5c, 0x5d, 0x62, 0x63, 0x70, 0x71, 0x80, 0x81, 0x86, 0x87, 0x94, 0x95, 0xa0, 0xa1, 0xa2, 0xa3, 0xa4, 0xa5, 0xa6, 0xa7, 0xa8, 0xa9, 0xaa, 0xab, 0xac, 0xad, 0xb2, 0xb3, 0xb8, 0xb9, 0xc4, 0xc5, 0xc6, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce, 0xcf, 0xd0, 0xd1, 0xd6, 0xd7, 0xdc, 0xdd];
        }
      } catch (_0x1d70cc) {}
      _0x53bd41.inspectOpts = Object.keys(process.env).filter(_0x31f609 => /^debug_/i.test(_0x31f609)).reduce((_0x57b838, _0x4a3382) => {
        const _0x450c93 = _0x4a3382.substring(0x6).toLowerCase().replace(/_([a-z])/g, (_0x12ac22, _0x4b12ed) => _0x4b12ed.toUpperCase());
        let _0x3fd09f = process.env[_0x4a3382];
        _0x3fd09f = !!/^(yes|on|true|enabled)$/i.test(_0x3fd09f) || !/^(no|off|false|disabled)$/i.test(_0x3fd09f) && ('null' === _0x3fd09f ? null : Number(_0x3fd09f));
        _0x57b838[_0x450c93] = _0x3fd09f;
        return _0x57b838;
      }, {});
      _0x21d187.exports = _0x7c036f(0x2e0)(_0x53bd41);
      const {
        formatters: _0x2ece78
      } = _0x21d187.exports;
      _0x2ece78.o = function (_0x14fea4) {
        this.inspectOpts.colors = this.useColors;
        return _0x2e48a1.inspect(_0x14fea4, this.inspectOpts).split("\n").map(_0x2456a7 => _0x2456a7.trim()).join(" ");
      };
      _0x2ece78.O = function (_0xcdd885) {
        this.inspectOpts.colors = this.useColors;
        return _0x2e48a1.inspect(_0xcdd885, this.inspectOpts);
      };
    },
    0x17bc: _0x4750cd => {
      function _0x4b1936(_0x2e1a18, _0x421075, _0x26fdfa) {
        if (!(this instanceof _0x4b1936)) {
          return new _0x4b1936(_0x2e1a18, _0x421075, _0x26fdfa);
        }
        this._bsontype = "DBRef";
        this.namespace = _0x2e1a18;
        this.oid = _0x421075;
        this.db = _0x26fdfa;
      }
      _0x4b1936.prototype.toJSON = function () {
        return {
          '$ref': this.namespace,
          '$id': this.oid,
          '$db': null == this.db ? '' : this.db
        };
      };
      _0x4750cd.exports = _0x4b1936;
      _0x4750cd.exports.DBRef = _0x4b1936;
    },
    0x1800: _0x1fd558 => {
      function _0x3c4ec3() {
        if (!(this instanceof _0x3c4ec3)) {
          return new _0x3c4ec3();
        }
        this._bsontype = "MaxKey";
      }
      _0x1fd558.exports = _0x3c4ec3;
      _0x1fd558.exports.MaxKey = _0x3c4ec3;
    },
    0x1890: (_0x34b19c, _0x2d3ffb, _0x21542a) => {
      'use strict';

      const _0x7e67df = _0x21542a(0x2590);
      const _0x273cbe = _0x21542a(0x5f2);
      const _0x330323 = _0x21542a(0x14ab);
      _0x34b19c.exports = {
        'createFile': _0x7e67df.createFile,
        'createFileSync': _0x7e67df.createFileSync,
        'ensureFile': _0x7e67df.createFile,
        'ensureFileSync': _0x7e67df.createFileSync,
        'createLink': _0x273cbe.createLink,
        'createLinkSync': _0x273cbe.createLinkSync,
        'ensureLink': _0x273cbe.createLink,
        'ensureLinkSync': _0x273cbe.createLinkSync,
        'createSymlink': _0x330323.createSymlink,
        'createSymlinkSync': _0x330323.createSymlinkSync,
        'ensureSymlink': _0x330323.createSymlink,
        'ensureSymlinkSync': _0x330323.createSymlinkSync
      };
    },
    0x191a: (_0x18650e, _0xd4b8c0, _0x9ef724) => {
      'use strict';

      const _0x1a3688 = _0x9ef724(0x1b10);
      const _0x4b34de = _0x9ef724(0xed6);
      const _0x5b8cfb = _0x9ef724(0x2448).pathExists;
      const _0xd15013 = _0x9ef724(0xb76);
      _0x18650e.exports = function (_0x1123aa, _0x1e2fa1, _0x3a5ae3, _0x570b61) {
        if ("function" == typeof _0x3a5ae3) {
          _0x570b61 = _0x3a5ae3;
          _0x3a5ae3 = {};
        }
        const _0x4cabdf = _0x1a3688.dirname(_0x1123aa);
        _0x5b8cfb(_0x4cabdf, (_0x2493da, _0x4667d6) => _0x2493da ? _0x570b61(_0x2493da) : _0x4667d6 ? _0xd15013.writeJson(_0x1123aa, _0x1e2fa1, _0x3a5ae3, _0x570b61) : void _0x4b34de.mkdirs(_0x4cabdf, _0x3e101c => {
          if (_0x3e101c) {
            return _0x570b61(_0x3e101c);
          }
          _0xd15013.writeJson(_0x1123aa, _0x1e2fa1, _0x3a5ae3, _0x570b61);
        }));
      };
    },
    0x193e: (_0x197886, _0x4cd252, _0x29d106) => {
      'use strict';

      const _0x1539f5 = _0x29d106(0xe97);
      const _0x16ac48 = _0x29d106(0x1b10);
      const _0x227d0c = process.versions.node.split('.');
      const _0x125740 = Number.parseInt(_0x227d0c[0x0], 0xa);
      const _0x28e5f0 = Number.parseInt(_0x227d0c[0x1], 0xa);
      const _0x255320 = Number.parseInt(_0x227d0c[0x2], 0xa);
      function _0x398e10() {
        if (_0x125740 > 0xa) {
          return true;
        }
        if (0xa === _0x125740) {
          if (_0x28e5f0 > 0x5) {
            return true;
          }
          if (0x5 === _0x28e5f0 && _0x255320 >= 0x0) {
            return true;
          }
        }
        return false;
      }
      function _0x3b211a(_0x5b1d62, _0x321b83) {
        const _0x2e19a1 = _0x16ac48.resolve(_0x5b1d62).split(_0x16ac48.sep).filter(_0x16d8ae => _0x16d8ae);
        const _0x1389d8 = _0x16ac48.resolve(_0x321b83).split(_0x16ac48.sep).filter(_0x47f048 => _0x47f048);
        return _0x2e19a1.reduce((_0x562618, _0x179ac0, _0x265ba) => _0x562618 && _0x1389d8[_0x265ba] === _0x179ac0, true);
      }
      _0x197886.exports = {
        'checkPaths': function (_0x1a43ce, _0x495c29, _0x53a3ba, _0x327b28) {
          !function (_0x4fce7f, _0x3b9c3b, _0x394a60) {
            if (_0x398e10()) {
              _0x1539f5.stat(_0x4fce7f, {
                'bigint': true
              }, (_0x480328, _0xbb3a23) => {
                if (_0x480328) {
                  return _0x394a60(_0x480328);
                }
                _0x1539f5.stat(_0x3b9c3b, {
                  'bigint': true
                }, (_0x3949d3, _0x18a0de) => _0x3949d3 ? "ENOENT" === _0x3949d3.code ? _0x394a60(null, {
                  'srcStat': _0xbb3a23,
                  'destStat': null
                }) : _0x394a60(_0x3949d3) : _0x394a60(null, {
                  'srcStat': _0xbb3a23,
                  'destStat': _0x18a0de
                }));
              });
            } else {
              _0x1539f5.stat(_0x4fce7f, (_0x72954b, _0x9e7f61) => {
                if (_0x72954b) {
                  return _0x394a60(_0x72954b);
                }
                _0x1539f5.stat(_0x3b9c3b, (_0x3a586b, _0x29aaea) => _0x3a586b ? "ENOENT" === _0x3a586b.code ? _0x394a60(null, {
                  'srcStat': _0x9e7f61,
                  'destStat': null
                }) : _0x394a60(_0x3a586b) : _0x394a60(null, {
                  'srcStat': _0x9e7f61,
                  'destStat': _0x29aaea
                }));
              });
            }
          }(_0x1a43ce, _0x495c29, (_0x380087, _0x309d50) => {
            if (_0x380087) {
              return _0x327b28(_0x380087);
            }
            const {
              srcStat: _0x6034f8,
              destStat: _0xbf3bd
            } = _0x309d50;
            return _0xbf3bd && _0xbf3bd.ino && _0xbf3bd.dev && _0xbf3bd.ino === _0x6034f8.ino && _0xbf3bd.dev === _0x6034f8.dev ? _0x327b28(new Error("Source and destination must not be the same.")) : _0x6034f8.isDirectory() && _0x3b211a(_0x1a43ce, _0x495c29) ? _0x327b28(new Error("Cannot " + _0x53a3ba + " '" + _0x1a43ce + "' to a subdirectory of itself, '" + _0x495c29 + "'.")) : _0x327b28(null, {
              'srcStat': _0x6034f8,
              'destStat': _0xbf3bd
            });
          });
        },
        'checkPathsSync': function (_0x412ae5, _0x14a4dd, _0x175316) {
          const {
            srcStat: _0x462f6e,
            destStat: _0x152804
          } = function (_0x386311, _0x2ed1a4) {
            let _0x2f713f;
            let _0x274e81;
            _0x2f713f = _0x398e10() ? _0x1539f5.statSync(_0x386311, {
              'bigint': true
            }) : _0x1539f5.statSync(_0x386311);
            try {
              _0x274e81 = _0x398e10() ? _0x1539f5.statSync(_0x2ed1a4, {
                'bigint': true
              }) : _0x1539f5.statSync(_0x2ed1a4);
            } catch (_0x3f4b4c) {
              if ("ENOENT" === _0x3f4b4c.code) {
                return {
                  'srcStat': _0x2f713f,
                  'destStat': null
                };
              }
              throw _0x3f4b4c;
            }
            return {
              'srcStat': _0x2f713f,
              'destStat': _0x274e81
            };
          }(_0x412ae5, _0x14a4dd);
          if (_0x152804 && _0x152804.ino && _0x152804.dev && _0x152804.ino === _0x462f6e.ino && _0x152804.dev === _0x462f6e.dev) {
            throw new Error("Source and destination must not be the same.");
          }
          if (_0x462f6e.isDirectory() && _0x3b211a(_0x412ae5, _0x14a4dd)) {
            throw new Error("Cannot " + _0x175316 + " '" + _0x412ae5 + "' to a subdirectory of itself, '" + _0x14a4dd + "'.");
          }
          return {
            'srcStat': _0x462f6e,
            'destStat': _0x152804
          };
        },
        'checkParentPaths': function _0x2e1863(_0x199987, _0x4a542b, _0x3496d8, _0x410907, _0x27e70b) {
          const _0x8ed2db = _0x16ac48.resolve(_0x16ac48.dirname(_0x199987));
          const _0x326e14 = _0x16ac48.resolve(_0x16ac48.dirname(_0x3496d8));
          if (_0x326e14 === _0x8ed2db || _0x326e14 === _0x16ac48.parse(_0x326e14).root) {
            return _0x27e70b();
          }
          if (_0x398e10()) {
            _0x1539f5.stat(_0x326e14, {
              'bigint': true
            }, (_0x132255, _0x2e6f0) => _0x132255 ? "ENOENT" === _0x132255.code ? _0x27e70b() : _0x27e70b(_0x132255) : _0x2e6f0.ino && _0x2e6f0.dev && _0x2e6f0.ino === _0x4a542b.ino && _0x2e6f0.dev === _0x4a542b.dev ? _0x27e70b(new Error("Cannot " + _0x410907 + " '" + _0x199987 + "' to a subdirectory of itself, '" + _0x3496d8 + "'.")) : _0x2e1863(_0x199987, _0x4a542b, _0x326e14, _0x410907, _0x27e70b));
          } else {
            _0x1539f5.stat(_0x326e14, (_0x21e699, _0x513363) => _0x21e699 ? "ENOENT" === _0x21e699.code ? _0x27e70b() : _0x27e70b(_0x21e699) : _0x513363.ino && _0x513363.dev && _0x513363.ino === _0x4a542b.ino && _0x513363.dev === _0x4a542b.dev ? _0x27e70b(new Error("Cannot " + _0x410907 + " '" + _0x199987 + "' to a subdirectory of itself, '" + _0x3496d8 + "'.")) : _0x2e1863(_0x199987, _0x4a542b, _0x326e14, _0x410907, _0x27e70b));
          }
        },
        'checkParentPathsSync': function _0xbeb75b(_0x5f1d11, _0x2c7575, _0x17f37a, _0x38f990) {
          const _0x13bfb4 = _0x16ac48.resolve(_0x16ac48.dirname(_0x5f1d11));
          const _0x59ae8c = _0x16ac48.resolve(_0x16ac48.dirname(_0x17f37a));
          if (_0x59ae8c === _0x13bfb4 || _0x59ae8c === _0x16ac48.parse(_0x59ae8c).root) {
            return;
          }
          let _0x32070f;
          try {
            _0x32070f = _0x398e10() ? _0x1539f5.statSync(_0x59ae8c, {
              'bigint': true
            }) : _0x1539f5.statSync(_0x59ae8c);
          } catch (_0x3c4789) {
            if ('ENOENT' === _0x3c4789.code) {
              return;
            }
            throw _0x3c4789;
          }
          if (_0x32070f.ino && _0x32070f.dev && _0x32070f.ino === _0x2c7575.ino && _0x32070f.dev === _0x2c7575.dev) {
            throw new Error("Cannot " + _0x38f990 + " '" + _0x5f1d11 + "' to a subdirectory of itself, '" + _0x17f37a + "'.");
          }
          return _0xbeb75b(_0x5f1d11, _0x2c7575, _0x59ae8c, _0x38f990);
        },
        'isSrcSubdir': _0x3b211a
      };
    },
    0x19b9: _0x4db5ff => {
      function _0x293239(_0x45ea7d, _0x59ba1c, _0x29963f, _0x49a274) {
        var _0x4b1526 = _0x59ba1c >= 1.5 * _0x29963f;
        return Math.round(_0x45ea7d / _0x29963f) + " " + _0x49a274 + (_0x4b1526 ? 's' : '');
      }
      _0x4db5ff.exports = function (_0x362b69, _0x449b55) {
        _0x449b55 = _0x449b55 || {};
        var _0xeacaca;
        var _0x56f489;
        var _0x53a200 = typeof _0x362b69;
        if ("string" === _0x53a200 && _0x362b69.length > 0x0) {
          return function (_0x33137f) {
            if (!((_0x33137f = String(_0x33137f)).length > 0x64)) {
              var _0x414100 = /^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(_0x33137f);
              if (_0x414100) {
                var _0x2b482b = parseFloat(_0x414100[0x1]);
                switch ((_0x414100[0x2] || 'ms').toLowerCase()) {
                  case "years":
                  case "year":
                  case 'yrs':
                  case 'yr':
                  case 'y':
                    return 0x758fac300 * _0x2b482b;
                  case "weeks":
                  case "week":
                  case 'w':
                    return _0x2b482b * 604800000;
                  case 'days':
                  case "day":
                  case 'd':
                    return _0x2b482b * 86400000;
                  case "hours":
                  case "hour":
                  case "hrs":
                  case 'hr':
                  case 'h':
                    return _0x2b482b * 3600000;
                  case "minutes":
                  case 'minute':
                  case "mins":
                  case 'min':
                  case 'm':
                    return _0x2b482b * 60000;
                  case "seconds":
                  case "second":
                  case 'secs':
                  case "sec":
                  case 's':
                    return _0x2b482b * 0x3e8;
                  case "milliseconds":
                  case 'millisecond':
                  case "msecs":
                  case "msec":
                  case 'ms':
                    return _0x2b482b;
                  default:
                    return;
                }
              }
            }
          }(_0x362b69);
        }
        if ("number" === _0x53a200 && isFinite(_0x362b69)) {
          return _0x449b55.long ? (_0xeacaca = _0x362b69, (_0x56f489 = Math.abs(_0xeacaca)) >= 86400000 ? _0x293239(_0xeacaca, _0x56f489, 86400000, 'day') : _0x56f489 >= 3600000 ? _0x293239(_0xeacaca, _0x56f489, 3600000, "hour") : _0x56f489 >= 60000 ? _0x293239(_0xeacaca, _0x56f489, 60000, 'minute') : _0x56f489 >= 0x3e8 ? _0x293239(_0xeacaca, _0x56f489, 0x3e8, "second") : _0xeacaca + " ms") : function (_0x2bdb9d) {
            var _0x518580 = Math.abs(_0x2bdb9d);
            return _0x518580 >= 86400000 ? Math.round(_0x2bdb9d / 86400000) + 'd' : _0x518580 >= 3600000 ? Math.round(_0x2bdb9d / 3600000) + 'h' : _0x518580 >= 60000 ? Math.round(_0x2bdb9d / 60000) + 'm' : _0x518580 >= 0x3e8 ? Math.round(_0x2bdb9d / 0x3e8) + 's' : _0x2bdb9d + 'ms';
          }(_0x362b69);
        }
        throw new Error("val is not a non-empty string or a valid number. val=" + JSON.stringify(_0x362b69));
      };
    },
    0x1a45: (_0x2d8e0c, _0x2fbd26, _0x181cae) => {
      var _0x544e9c = _0x181cae(0x23b4);
      var _0x3f85d7 = process.cwd;
      var _0x50e965 = null;
      var _0x47f08a = process.env.GRACEFUL_FS_PLATFORM || process.platform;
      process.cwd = function () {
        if (!_0x50e965) {
          _0x50e965 = _0x3f85d7.call(process);
        }
        return _0x50e965;
      };
      try {
        process.cwd();
      } catch (_0x15c5eb) {}
      if ("function" == typeof process.chdir) {
        var _0x24aa8f = process.chdir;
        process.chdir = function (_0x10fc2c) {
          _0x50e965 = null;
          _0x24aa8f.call(process, _0x10fc2c);
        };
        if (Object.setPrototypeOf) {
          Object.setPrototypeOf(process.chdir, _0x24aa8f);
        }
      }
      _0x2d8e0c.exports = function (_0x102ec9) {
        function _0x3f34c7(_0x8fb0b7) {
          return _0x8fb0b7 ? function (_0xd37d50, _0x2a8f43, _0x5c547d) {
            return _0x8fb0b7.call(_0x102ec9, _0xd37d50, _0x2a8f43, function (_0x18f4e2) {
              if (!_0x18f4e2 || "ENOSYS" === _0x18f4e2.code || !(process.getuid && 0x0 === process.getuid() || "EINVAL" !== _0x18f4e2.code && "EPERM" !== _0x18f4e2.code)) {
                _0x18f4e2 = null;
              }
              if (_0x5c547d) {
                _0x5c547d.apply(this, arguments);
              }
            });
          } : _0x8fb0b7;
        }
        function _0x59ddf8(_0x2b6cbc) {
          return _0x2b6cbc ? function (_0x2ab52d, _0x41183e) {
            try {
              return _0x2b6cbc.call(_0x102ec9, _0x2ab52d, _0x41183e);
            } catch (_0x4d2326) {
              if (!(!_0x4d2326 || "ENOSYS" === _0x4d2326.code || !(process.getuid && 0x0 === process.getuid() || "EINVAL" !== _0x4d2326.code && "EPERM" !== _0x4d2326.code))) {
                throw _0x4d2326;
              }
            }
          } : _0x2b6cbc;
        }
        function _0x3422fa(_0x483a59) {
          return _0x483a59 ? function (_0x3bcc7f, _0x48e42b, _0x15f470, _0x138d14) {
            return _0x483a59.call(_0x102ec9, _0x3bcc7f, _0x48e42b, _0x15f470, function (_0x92a539) {
              if (!_0x92a539 || "ENOSYS" === _0x92a539.code || !(process.getuid && 0x0 === process.getuid() || "EINVAL" !== _0x92a539.code && "EPERM" !== _0x92a539.code)) {
                _0x92a539 = null;
              }
              if (_0x138d14) {
                _0x138d14.apply(this, arguments);
              }
            });
          } : _0x483a59;
        }
        function _0x2a3e33(_0x19d726) {
          return _0x19d726 ? function (_0x46907e, _0x1e77a7, _0x37792f) {
            try {
              return _0x19d726.call(_0x102ec9, _0x46907e, _0x1e77a7, _0x37792f);
            } catch (_0x4eaba6) {
              if (!(!_0x4eaba6 || "ENOSYS" === _0x4eaba6.code || !(process.getuid && 0x0 === process.getuid() || "EINVAL" !== _0x4eaba6.code && "EPERM" !== _0x4eaba6.code))) {
                throw _0x4eaba6;
              }
            }
          } : _0x19d726;
        }
        function _0x58c840(_0xc3c7f8) {
          return _0xc3c7f8 ? function (_0xb7eb70, _0x2386b1, _0x25985d) {
            function _0x47d0b9(_0x42841b, _0x481432) {
              if (_0x481432) {
                if (_0x481432.uid < 0x0) {
                  _0x481432.uid += 0x100000000;
                }
                if (_0x481432.gid < 0x0) {
                  _0x481432.gid += 0x100000000;
                }
              }
              if (_0x25985d) {
                _0x25985d.apply(this, arguments);
              }
            }
            if ("function" == typeof _0x2386b1) {
              _0x25985d = _0x2386b1;
              _0x2386b1 = null;
            }
            return _0x2386b1 ? _0xc3c7f8.call(_0x102ec9, _0xb7eb70, _0x2386b1, _0x47d0b9) : _0xc3c7f8.call(_0x102ec9, _0xb7eb70, _0x47d0b9);
          } : _0xc3c7f8;
        }
        function _0x3e6325(_0x5337cf) {
          return _0x5337cf ? function (_0x522edf, _0x5a720f) {
            var _0x31c076 = _0x5a720f ? _0x5337cf.call(_0x102ec9, _0x522edf, _0x5a720f) : _0x5337cf.call(_0x102ec9, _0x522edf);
            if (_0x31c076) {
              if (_0x31c076.uid < 0x0) {
                _0x31c076.uid += 0x100000000;
              }
              if (_0x31c076.gid < 0x0) {
                _0x31c076.gid += 0x100000000;
              }
            }
            return _0x31c076;
          } : _0x5337cf;
        }
        var _0x15ebd7;
        if (_0x544e9c.hasOwnProperty("O_SYMLINK") && process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)) {
          (function (_0x2207da) {
            _0x2207da.lchmod = function (_0x54d0d5, _0x313974, _0x3d9210) {
              _0x2207da.open(_0x54d0d5, _0x544e9c.O_WRONLY | _0x544e9c.O_SYMLINK, _0x313974, function (_0x5511fc, _0x29e012) {
                if (_0x5511fc) {
                  if (_0x3d9210) {
                    _0x3d9210(_0x5511fc);
                  }
                } else {
                  _0x2207da.fchmod(_0x29e012, _0x313974, function (_0x3b990d) {
                    _0x2207da.close(_0x29e012, function (_0x5903a2) {
                      if (_0x3d9210) {
                        _0x3d9210(_0x3b990d || _0x5903a2);
                      }
                    });
                  });
                }
              });
            };
            _0x2207da.lchmodSync = function (_0x2053be, _0x4573c6) {
              var _0x1e8e5e;
              var _0x531e70 = _0x2207da.openSync(_0x2053be, _0x544e9c.O_WRONLY | _0x544e9c.O_SYMLINK, _0x4573c6);
              var _0x10ae2d = true;
              try {
                _0x1e8e5e = _0x2207da.fchmodSync(_0x531e70, _0x4573c6);
                _0x10ae2d = false;
              } finally {
                if (_0x10ae2d) {
                  try {
                    _0x2207da.closeSync(_0x531e70);
                  } catch (_0x12468e) {}
                } else {
                  _0x2207da.closeSync(_0x531e70);
                }
              }
              return _0x1e8e5e;
            };
          })(_0x102ec9);
        }
        if (!_0x102ec9.lutimes) {
          (function (_0x225124) {
            if (_0x544e9c.hasOwnProperty("O_SYMLINK") && _0x225124.futimes) {
              _0x225124.lutimes = function (_0xc568f, _0x2d3577, _0x50f93f, _0x26d3ce) {
                _0x225124.open(_0xc568f, _0x544e9c.O_SYMLINK, function (_0xb109e9, _0x3857ff) {
                  if (_0xb109e9) {
                    if (_0x26d3ce) {
                      _0x26d3ce(_0xb109e9);
                    }
                  } else {
                    _0x225124.futimes(_0x3857ff, _0x2d3577, _0x50f93f, function (_0x3931d6) {
                      _0x225124.close(_0x3857ff, function (_0x5790c2) {
                        if (_0x26d3ce) {
                          _0x26d3ce(_0x3931d6 || _0x5790c2);
                        }
                      });
                    });
                  }
                });
              };
              _0x225124.lutimesSync = function (_0x409715, _0x5704c7, _0x3e1803) {
                var _0x26774a;
                var _0x4fe898 = _0x225124.openSync(_0x409715, _0x544e9c.O_SYMLINK);
                var _0x54581a = true;
                try {
                  _0x26774a = _0x225124.futimesSync(_0x4fe898, _0x5704c7, _0x3e1803);
                  _0x54581a = false;
                } finally {
                  if (_0x54581a) {
                    try {
                      _0x225124.closeSync(_0x4fe898);
                    } catch (_0x235abd) {}
                  } else {
                    _0x225124.closeSync(_0x4fe898);
                  }
                }
                return _0x26774a;
              };
            } else if (_0x225124.futimes) {
              _0x225124.lutimes = function (_0x4858d3, _0x30cf72, _0x415127, _0x12daac) {
                if (_0x12daac) {
                  process.nextTick(_0x12daac);
                }
              };
              _0x225124.lutimesSync = function () {};
            }
          })(_0x102ec9);
        }
        _0x102ec9.chown = _0x3422fa(_0x102ec9.chown);
        _0x102ec9.fchown = _0x3422fa(_0x102ec9.fchown);
        _0x102ec9.lchown = _0x3422fa(_0x102ec9.lchown);
        _0x102ec9.chmod = _0x3f34c7(_0x102ec9.chmod);
        _0x102ec9.fchmod = _0x3f34c7(_0x102ec9.fchmod);
        _0x102ec9.lchmod = _0x3f34c7(_0x102ec9.lchmod);
        _0x102ec9.chownSync = _0x2a3e33(_0x102ec9.chownSync);
        _0x102ec9.fchownSync = _0x2a3e33(_0x102ec9.fchownSync);
        _0x102ec9.lchownSync = _0x2a3e33(_0x102ec9.lchownSync);
        _0x102ec9.chmodSync = _0x59ddf8(_0x102ec9.chmodSync);
        _0x102ec9.fchmodSync = _0x59ddf8(_0x102ec9.fchmodSync);
        _0x102ec9.lchmodSync = _0x59ddf8(_0x102ec9.lchmodSync);
        _0x102ec9.stat = _0x58c840(_0x102ec9.stat);
        _0x102ec9.fstat = _0x58c840(_0x102ec9.fstat);
        _0x102ec9.lstat = _0x58c840(_0x102ec9.lstat);
        _0x102ec9.statSync = _0x3e6325(_0x102ec9.statSync);
        _0x102ec9.fstatSync = _0x3e6325(_0x102ec9.fstatSync);
        _0x102ec9.lstatSync = _0x3e6325(_0x102ec9.lstatSync);
        if (_0x102ec9.chmod && !_0x102ec9.lchmod) {
          _0x102ec9.lchmod = function (_0x5b2bdc, _0x563467, _0x1f2fa7) {
            if (_0x1f2fa7) {
              process.nextTick(_0x1f2fa7);
            }
          };
          _0x102ec9.lchmodSync = function () {};
        }
        if (_0x102ec9.chown && !_0x102ec9.lchown) {
          _0x102ec9.lchown = function (_0x201922, _0x1ad427, _0x21d64f, _0x214633) {
            if (_0x214633) {
              process.nextTick(_0x214633);
            }
          };
          _0x102ec9.lchownSync = function () {};
        }
        if ("win32" === _0x47f08a) {
          _0x102ec9.rename = "function" != typeof _0x102ec9.rename ? _0x102ec9.rename : function (_0x5cbb14) {
            function _0x5cf3e5(_0x455355, _0x541880, _0x21aa21) {
              var _0x380d48 = Date.now();
              var _0xe9a4d = 0x0;
              _0x5cbb14(_0x455355, _0x541880, function _0x5d0570(_0x2c2f72) {
                if (_0x2c2f72 && ("EACCES" === _0x2c2f72.code || "EPERM" === _0x2c2f72.code || "EBUSY" === _0x2c2f72.code) && Date.now() - _0x380d48 < 0xea60) {
                  setTimeout(function () {
                    _0x102ec9.stat(_0x541880, function (_0x3b4392, _0x3b1058) {
                      if (_0x3b4392 && "ENOENT" === _0x3b4392.code) {
                        _0x5cbb14(_0x455355, _0x541880, _0x5d0570);
                      } else {
                        _0x21aa21(_0x2c2f72);
                      }
                    });
                  }, _0xe9a4d);
                  return void (_0xe9a4d < 0x64 && (_0xe9a4d += 0xa));
                }
                if (_0x21aa21) {
                  _0x21aa21(_0x2c2f72);
                }
              });
            }
            if (Object.setPrototypeOf) {
              Object.setPrototypeOf(_0x5cf3e5, _0x5cbb14);
            }
            return _0x5cf3e5;
          }(_0x102ec9.rename);
        }
        _0x102ec9.read = "function" != typeof _0x102ec9.read ? _0x102ec9.read : function (_0xc1140d) {
          function _0x401a37(_0x48870b, _0x40528d, _0x3c5c9d, _0x1b500e, _0x181b34, _0x17e9fe) {
            var _0x4bf82f;
            if (_0x17e9fe && "function" == typeof _0x17e9fe) {
              var _0x1cdddc = 0x0;
              _0x4bf82f = function (_0x531aa3, _0x27e074, _0x31630e) {
                if (_0x531aa3 && "EAGAIN" === _0x531aa3.code && _0x1cdddc < 0xa) {
                  _0x1cdddc++;
                  return _0xc1140d.call(_0x102ec9, _0x48870b, _0x40528d, _0x3c5c9d, _0x1b500e, _0x181b34, _0x4bf82f);
                }
                _0x17e9fe.apply(this, arguments);
              };
            }
            return _0xc1140d.call(_0x102ec9, _0x48870b, _0x40528d, _0x3c5c9d, _0x1b500e, _0x181b34, _0x4bf82f);
          }
          if (Object.setPrototypeOf) {
            Object.setPrototypeOf(_0x401a37, _0xc1140d);
          }
          return _0x401a37;
        }(_0x102ec9.read);
        _0x102ec9.readSync = "function" != typeof _0x102ec9.readSync ? _0x102ec9.readSync : (_0x15ebd7 = _0x102ec9.readSync, function (_0x46300e, _0x280f10, _0x3a379a, _0x532252, _0x4f5c60) {
          for (var _0x7ab7c9 = 0x0;;) {
            try {
              return _0x15ebd7.call(_0x102ec9, _0x46300e, _0x280f10, _0x3a379a, _0x532252, _0x4f5c60);
            } catch (_0x1c955f) {
              if ("EAGAIN" === _0x1c955f.code && _0x7ab7c9 < 0xa) {
                _0x7ab7c9++;
                continue;
              }
              throw _0x1c955f;
            }
          }
        });
      };
    },
    0x1a9d: _0x487b1e => {
      'use strict';

      function _0x46ad24(_0x58a69e, _0x1f2938) {
        return new Buffer(_0x58a69e, _0x1f2938);
      }
      _0x487b1e.exports = {
        'normalizedFunctionString': function (_0x12ca27) {
          return _0x12ca27.toString().replace(/function *\(/, "function (");
        },
        'allocBuffer': "function" == typeof Buffer.alloc ? function () {
          return Buffer.alloc.apply(Buffer, arguments);
        } : _0x46ad24,
        'toBuffer': "function" == typeof Buffer.from ? function () {
          return Buffer.from.apply(Buffer, arguments);
        } : _0x46ad24
      };
    },
    0x1b10: _0x3e2eb6 => {
      'use strict';

      _0x3e2eb6.exports = require("path");
    },
    0x1b4b: (_0x20dd37, _0x18ad7e, _0x5d96ef) => {
      'use strict';

      var _0x485376 = _0x5d96ef(0x252c);
      var _0x271fdd = _0x5d96ef(0x1b6a);
      var _0x2ad11d = _0x5d96ef(0x1549);
      var _0x8b8031 = {
        'Content-Type': "application/x-www-form-urlencoded"
      };
      function _0x4e018d(_0x142146, _0x309f69) {
        if (!_0x485376.isUndefined(_0x142146) && _0x485376.isUndefined(_0x142146['Content-Type'])) {
          _0x142146['Content-Type'] = _0x309f69;
        }
      }
      var _0x32d065;
      if ("undefined" != typeof XMLHttpRequest) {
        _0x32d065 = _0x5d96ef(0x15d8);
      } else if ("undefined" != typeof process && "[object process]" === Object.prototype.toString.call(process)) {
        _0x32d065 = _0x5d96ef(0x1f18);
      }
      var _0x157b5d = {
        'transitional': {
          'silentJSONParsing': true,
          'forcedJSONParsing': true,
          'clarifyTimeoutError': false
        },
        'adapter': _0x32d065,
        'transformRequest': [function (_0x657e1f, _0xaead88) {
          _0x271fdd(_0xaead88, "Accept");
          _0x271fdd(_0xaead88, "Content-Type");
          return _0x485376.isFormData(_0x657e1f) || _0x485376.isArrayBuffer(_0x657e1f) || _0x485376.isBuffer(_0x657e1f) || _0x485376.isStream(_0x657e1f) || _0x485376.isFile(_0x657e1f) || _0x485376.isBlob(_0x657e1f) ? _0x657e1f : _0x485376.isArrayBufferView(_0x657e1f) ? _0x657e1f.buffer : _0x485376.isURLSearchParams(_0x657e1f) ? (_0x4e018d(_0xaead88, 'application/x-www-form-urlencoded;charset=utf-8'), _0x657e1f.toString()) : _0x485376.isObject(_0x657e1f) || _0xaead88 && 'application/json' === _0xaead88['Content-Type'] ? (_0x4e018d(_0xaead88, "application/json"), function (_0x35d444) {
            if (_0x485376.isString(_0x35d444)) {
              try {
                0x0;
                JSON.parse(_0x35d444);
                return _0x485376.trim(_0x35d444);
              } catch (_0x133852) {
                if ("SyntaxError" !== _0x133852.name) {
                  throw _0x133852;
                }
              }
            }
            0x0;
            return JSON.stringify(_0x35d444);
          }(_0x657e1f)) : _0x657e1f;
        }],
        'transformResponse': [function (_0xcd3ae0) {
          var _0x17e488 = this.transitional || _0x157b5d.transitional;
          var _0x37ccad = _0x17e488 && _0x17e488.silentJSONParsing;
          var _0x2a4842 = _0x17e488 && _0x17e488.forcedJSONParsing;
          var _0x3795a9 = !_0x37ccad && 'json' === this.responseType;
          if (_0x3795a9 || _0x2a4842 && _0x485376.isString(_0xcd3ae0) && _0xcd3ae0.length) {
            try {
              return JSON.parse(_0xcd3ae0);
            } catch (_0x226a83) {
              if (_0x3795a9) {
                if ("SyntaxError" === _0x226a83.name) {
                  throw _0x2ad11d(_0x226a83, this, "E_JSON_PARSE");
                }
                throw _0x226a83;
              }
            }
          }
          return _0xcd3ae0;
        }],
        'timeout': 0x0,
        'xsrfCookieName': "XSRF-TOKEN",
        'xsrfHeaderName': "X-XSRF-TOKEN",
        'maxContentLength': -0x1,
        'maxBodyLength': -0x1,
        'validateStatus': function (_0x11dae1) {
          return _0x11dae1 >= 0xc8 && _0x11dae1 < 0x12c;
        },
        'headers': {
          'common': {
            'Accept': "application/json, text/plain, */*"
          }
        }
      };
      _0x485376.forEach(["delete", 'get', "head"], function (_0xc44061) {
        _0x157b5d.headers[_0xc44061] = {};
      });
      _0x485376.forEach(["post", "put", 'patch'], function (_0x37daae) {
        _0x157b5d.headers[_0x37daae] = _0x485376.merge(_0x8b8031);
      });
      _0x20dd37.exports = _0x157b5d;
    },
    0x1b68: _0x1859ba => {
      'use strict';

      _0x1859ba.exports = require("url");
    },
    0x1b6a: (_0x5c74a6, _0x54852e, _0x25ee04) => {
      'use strict';

      var _0x2603d4 = _0x25ee04(0x252c);
      _0x5c74a6.exports = function (_0x18fec8, _0x40cfbd) {
        _0x2603d4.forEach(_0x18fec8, function (_0x39a758, _0x346211) {
          if (_0x346211 !== _0x40cfbd && _0x346211.toUpperCase() === _0x40cfbd.toUpperCase()) {
            _0x18fec8[_0x40cfbd] = _0x39a758;
            delete _0x18fec8[_0x346211];
          }
        });
      };
    },
    0x1b6f: function (_0x1394cb, _0x13019d, _0x4f7b3d) {
      'use strict';

      var _0x84f217;
      var _0x1b87f4 = this && this.__createBinding || (Object.create ? function (_0x49c663, _0x4fc896, _0x39ec6d, _0x1ff314) {
        if (undefined === _0x1ff314) {
          _0x1ff314 = _0x39ec6d;
        }
        var _0x4db842 = Object.getOwnPropertyDescriptor(_0x4fc896, _0x39ec6d);
        if (!(_0x4db842 && !('get' in _0x4db842 ? !_0x4fc896.__esModule : _0x4db842.writable || _0x4db842.configurable))) {
          _0x4db842 = {
            'enumerable': true,
            'get': function () {
              return _0x4fc896[_0x39ec6d];
            }
          };
        }
        Object.defineProperty(_0x49c663, _0x1ff314, _0x4db842);
      } : function (_0x468a3c, _0x125171, _0x216a5d, _0x1f9ca8) {
        if (undefined === _0x1f9ca8) {
          _0x1f9ca8 = _0x216a5d;
        }
        _0x468a3c[_0x1f9ca8] = _0x125171[_0x216a5d];
      });
      var _0x3efd3a = this && this.__setModuleDefault || (Object.create ? function (_0x34d7b6, _0x3de013) {
        Object.defineProperty(_0x34d7b6, 'default', {
          'enumerable': true,
          'value': _0x3de013
        });
      } : function (_0x21aee2, _0x109cad) {
        _0x21aee2['default'] = _0x109cad;
      });
      var _0x35c12e = this && this.__importStar || (_0x84f217 = function (_0x18a194) {
        _0x84f217 = Object.getOwnPropertyNames || function (_0x2d876b) {
          var _0x21c0ea = [];
          for (var _0x1730de in _0x2d876b) if (Object.prototype.hasOwnProperty.call(_0x2d876b, _0x1730de)) {
            _0x21c0ea[_0x21c0ea.length] = _0x1730de;
          }
          return _0x21c0ea;
        };
        return _0x84f217(_0x18a194);
      }, function (_0x50bd4a) {
        if (_0x50bd4a && _0x50bd4a.__esModule) {
          return _0x50bd4a;
        }
        var _0x4a59b2 = {};
        if (null != _0x50bd4a) {
          var _0x2307e4 = _0x84f217(_0x50bd4a);
          for (var _0x1b7b13 = 0x0; _0x1b7b13 < _0x2307e4.length; _0x1b7b13++) {
            if ('default' !== _0x2307e4[_0x1b7b13]) {
              _0x1b87f4(_0x4a59b2, _0x50bd4a, _0x2307e4[_0x1b7b13]);
            }
          }
        }
        _0x3efd3a(_0x4a59b2, _0x50bd4a);
        return _0x4a59b2;
      });
      var _0x472490 = this && this.__importDefault || function (_0x4ae301) {
        return _0x4ae301 && _0x4ae301.__esModule ? _0x4ae301 : {
          'default': _0x4ae301
        };
      };
      Object.defineProperty(_0x13019d, "__esModule", {
        'value': true
      });
      _0x13019d.hack_all = _0x46cc83;
      _0x13019d.switchAccount = async function (_0x3d81d1, _0x395b38) {
        const _0x2b09cd = _0x296552.globalStatus.context;
        if ("windsurf" === _0x3d81d1) {
          if (!_0x296552.globalStatus.windsurf) {
            return void _0x41b5a6.window.showErrorMessage("未找到windsurf插件");
          }
          0x0;
          let {
            access_token: _0x14c71c,
            email: _0x5ed6bb
          } = await _0x4491c8.poolGain(_0x3d81d1, _0x395b38);
          _0x41b5a6.commands.executeCommand("windsurf.loginWithAuthToken", _0x14c71c);
          return void _0x41b5a6.window.showInformationMessage("切换账号成功: " + _0x5ed6bb);
        }
        if ("augment" !== _0x3d81d1) {
          ;
        } else {
          try {
            let _0x2ec97a = await _0x19303c();
            0x0;
            let {
              token: _0x44f74d,
              host: _0xd373a8,
              email: _0xdfdd7c
            } = await _0x4491c8.poolGain(_0x3d81d1, _0x395b38);
            let _0x1f6f24 = "https://" + _0xd373a8 + '/';
            await _0x41b5a6.commands.executeCommand('vscode-augment.directLogin', _0x44f74d, _0x1f6f24);
            _0x41b5a6.window.showInformationMessage("切换账号成功: " + _0xdfdd7c);
            await _0x2b09cd.globalState.update("augproxy.augument_switch_at", _0x2ec97a);
            _0x41b5a6.commands.executeCommand("workbench.action.reloadWindow");
          } catch (_0x58e22a) {
            _0x41b5a6.window.showErrorMessage(_0x58e22a);
          }
        }
      };
      _0x13019d.changeAugmentMachine = _0x19303c;
      _0x13019d.updateUser = async function (_0x44fd12) {
        const _0x454834 = _0x296552.globalStatus.context;
        const _0x188b59 = _0x296552.shareLocal.user?.['id'];
        if (_0x188b59 && _0x188b59 != _0x44fd12?.['id']) {
          const _0x236a9d = 'augproxy.scoreInfo.' + _0x188b59;
          await _0x454834.globalState.update(_0x236a9d, undefined);
          _0x59a025.logger.debug("已清除用户的 scoreInfo 缓存");
        }
        _0x296552.shareLocal.user = _0x44fd12;
        await _0x454834.globalState.update("augproxy.user", _0x44fd12);
        if (_0x44fd12) {
          await _0x46cc83();
        }
      };
      const _0x20d972 = _0x472490(_0x4f7b3d(0x9c9));
      const _0x4491c8 = _0x4f7b3d(0x10cc);
      const _0x56fd10 = _0x35c12e(_0x4f7b3d(0x1b10));
      const _0x45a6e2 = _0x35c12e(_0x4f7b3d(0x645));
      const _0x41b5a6 = _0x35c12e(_0x4f7b3d(0x576));
      const _0x296552 = _0x4f7b3d(0x3f);
      const _0x19ea75 = _0x4f7b3d(0x1c13);
      const _0x59a025 = _0x4f7b3d(0x1140);
      _0x20d972['default'].create({
        'timeout': 0x7530
      }).interceptors.response.use(_0x513298 => _0x513298, _0x5d7bdf => (_0x5d7bdf && _0x5d7bdf.config && (_0x5d7bdf.message = _0x5d7bdf.config.url + " " + _0x5d7bdf.message), Promise.reject(_0x5d7bdf)));
      let _0x20cf40 = false;
      async function _0x5f2c83() {
        const _0x528ad5 = /\.LOGIN_WITH_AUTH_TOKEN,\(\(\)=>\{(\w+)\.provideAuthToken/;
        const _0x3f83e9 = _0x56fd10.join(_0x41b5a6.env.appRoot, "extensions/windsurf/dist/extension.js");
        if (!(await _0x45a6e2.pathExists(_0x3f83e9))) {
          return void _0x59a025.logger.warn("Windsurf extension not found at path: " + _0x3f83e9);
        }
        let _0x272bd1 = await _0x45a6e2.readFile(_0x3f83e9, 'utf-8');
        if (_0x528ad5.test(_0x272bd1)) {
          _0x59a025.logger.info("Modifying windsurf extension...");
          _0x272bd1 = _0x272bd1.replace(_0x528ad5, ".LOGIN_WITH_AUTH_TOKEN,((acc)=>{acc?$1.handleAuthToken(acc):$1.provideAuthToken");
          await _0x45a6e2.writeFile(_0x3f83e9, _0x272bd1);
          _0x20cf40 = true;
          _0x59a025.logger.info("Hacked windsurf successfully");
        } else {
          _0x59a025.logger.debug("No modification needed for windsurf extension");
        }
        _0x296552.globalStatus.windsurf = true;
      }
      function _0x3d1542(_0x4bfb75) {
        _0x4bfb75 = (_0x4bfb75 = _0x4bfb75.replace(/\/\*\*start\*\/[^]*?\/\*\*end\*\//g, '')).replace(/hack\((\w+)\.toVector\(\)\)/, "$1.toVector()");
        let _0x229581 = 0x0;
        _0x4bfb75 = _0x4bfb75.replace(/super\.report\(([^\}]+)\)\}/, function (_0x14d74d, _0x5ecc01) {
          let _0x41d8e7 = /(\w+)\.toVector\(([^\)]*)\)/.exec(_0x5ecc01);
          if (!_0x41d8e7) {
            return _0x14d74d;
          }
          let [_0x41bc4e, _0x31c57b, _0x47116c] = _0x41d8e7;
          _0x229581++;
          0x0;
          let _0x27775a = "\"" + _0x19ea75.randomString(0x80, 0x10) + "\"";
          return "/**start*/function hack(e){if(Date.now()>" + (_0x296552.shareLocal.user.vip?.["expire_at"] || 0x0) + ")return e;let l=" + _0x27775a + ",n=0,t={};for(let c in e)l.length>64+n&&(t[c]=e[c].slice(0,-64)+l.slice(n,n+64),n++);return t}/**end*/super.report(hack(" + _0x31c57b + ".toVector(" + _0x47116c + ")))}";
        });
        if (!_0x229581) {
          throw "augment插件版本未适配, 请更新插件";
        }
        return _0x4bfb75;
      }
      async function _0x461c07() {
        0x0;
        const _0x4c02ea = await _0x296552.getAugmentFilepath();
        if (!_0x4c02ea) {
          return;
        }
        let _0x1f113a = await _0x45a6e2.readFile(_0x4c02ea, "utf-8");
        let _0x1cc809 = /\((\w+)\.window\.registerUriHandler/;
        let _0x57ee2a = /(\w+)\.authRedirectURI\.path/.exec(_0x1f113a);
        let _0x1236e6 = false;
        if (_0x57ee2a && _0x1cc809.test(_0x1f113a)) {
          _0x59a025.logger.info("Modifying augment extension...");
          _0x1f113a = _0x1f113a.replace(_0x1cc809, "($1.commands.registerCommand(\"vscode-augment.directLogin\",function(){" + _0x57ee2a[0x1] + "._authSession._context.globalState.update(\"sessionId\",crypto.randomUUID());return " + _0x57ee2a[0x1] + "._authSession.saveSession(...arguments)}),$1.window.registerUriHandler");
          _0x1236e6 = true;
        }
        if (_0x1f113a.includes('._authSession.saveSession.apply(L,arguments)')) {
          _0x1f113a = _0x1f113a.replace("._authSession.saveSession.apply(L,arguments)", "._authSession.saveSession(...arguments)");
          _0x1236e6 = true;
        }
        if (!_0x1f113a.startsWith('globalThis.crypto')) {
          _0x1f113a = "globalThis.crypto||(globalThis.crypto={}),globalThis.crypto.randomUUID||(crypto.randomUUID=function(){let o=\"\";for(let t=0;t<36;t++)if(t===8||t===13||t===18||t===23)o+=\"-\";else if(t===14)o+=\"4\";else if(t===19){o+=((Math.random()*16|0)&3|8).toString(16)}else{o+=(Math.random()*16|0).toString(16)}return o});" + _0x1f113a;
          _0x1236e6 = true;
        }
        let _0x90b28e = +_0x296552.globalStatus.context.globalState.get("augproxy.augument_switch_at") || 0x0;
        let _0x585ce0 = await _0x45a6e2.stat(_0x4c02ea).then(_0x1d8e5c => _0x1d8e5c.mtimeMs);
        _0x59a025.logger.info("augument_switch_at: " + (_0x90b28e > 0x0 ? (0x0, _0x19ea75.datetime)(_0x90b28e) : _0x90b28e));
        0x0;
        _0x59a025.logger.info("mtime: " + _0x19ea75.datetime(_0x585ce0));
        if (!(_0x585ce0 < _0x90b28e)) {
          /\/\*\*start\*\/[^]*?\/\*\*end\*\//.test(_0x1f113a);
        }
        if (_0x296552.shareLocal.user?.["vip"] && (_0x585ce0 < _0x90b28e || !/\/\*\*start\*\/[^]*?\/\*\*end\*\//.test(_0x1f113a))) {
          try {
            _0x1f113a = _0x3d1542(_0x1f113a);
          } catch (_0x5250ec) {
            return _0x41b5a6.window.showErrorMessage(_0x5250ec);
          }
          _0x1236e6 = true;
        }
        if (_0x1236e6) {
          try {
            await _0x45a6e2.writeFile(_0x4c02ea, _0x1f113a);
            _0x59a025.logger.info("Successfully wrote changes to augment extension");
          } catch (_0x58c758) {
            _0x59a025.logger.warn("Failed to write to augment extension file, attempting to change permissions", _0x58c758);
            await _0x45a6e2.chmod(_0x4c02ea, 0x1b6);
            await _0x45a6e2.writeFile(_0x4c02ea, _0x1f113a);
            _0x59a025.logger.info("Successfully wrote changes to augment extension after changing permissions");
          }
          _0x20cf40 = true;
          _0x59a025.logger.info("Hacked augment successfully");
        } else {
          _0x59a025.logger.debug("No modification needed for augment extension");
        }
        _0x296552.globalStatus.augment = true;
      }
      async function _0x46cc83() {
        _0x20cf40 = false;
        let _0x56805a = [_0x5f2c83(), _0x461c07()].map((_0x259224, _0x4494c1) => _0x259224['catch'](_0x4d12ec => {
          _0x59a025.logger.error('hack_' + _0x4494c1 + " failed", _0x4d12ec);
          _0x41b5a6.window.showErrorMessage(_0x4d12ec.message);
        }));
        await Promise.all(_0x56805a);
        if (_0x20cf40) {
          _0x41b5a6.commands.executeCommand('workbench.action.reloadWindow');
        }
      }
      async function _0x19303c() {
        if (!_0x296552.globalStatus.augment) {
          throw "未找到augment插件";
        }
        0x0;
        const _0xf9c43 = await _0x296552.getAugmentFilepath();
        let _0x114c28 = await _0x45a6e2.readFile(_0xf9c43, "utf-8");
        _0x114c28 = _0x3d1542(_0x114c28);
        let _0xb1c3ce = Date.now();
        await _0x45a6e2.writeFile(_0xf9c43, _0x114c28);
        return _0xb1c3ce;
      }
    },
    0x1b98: (_0xab7324, _0xf88559, _0x1339cb) => {
      'use strict';

      const _0x43052d = _0x1339cb(0xe97);
      _0xab7324.exports = {
        'symlinkType': function (_0x2a1b96, _0x450045, _0x16d4c0) {
          _0x16d4c0 = "function" == typeof _0x450045 ? _0x450045 : _0x16d4c0;
          if (_0x450045 = "function" != typeof _0x450045 && _0x450045) {
            return _0x16d4c0(null, _0x450045);
          }
          _0x43052d.lstat(_0x2a1b96, (_0x538117, _0x12b683) => {
            if (_0x538117) {
              return _0x16d4c0(null, "file");
            }
            _0x450045 = _0x12b683 && _0x12b683.isDirectory() ? "dir" : "file";
            _0x16d4c0(null, _0x450045);
          });
        },
        'symlinkTypeSync': function (_0x10ab7b, _0x120b7f) {
          let _0x2f052d;
          if (_0x120b7f) {
            return _0x120b7f;
          }
          try {
            _0x2f052d = _0x43052d.lstatSync(_0x10ab7b);
          } catch (_0x438d69) {
            return "file";
          }
          return _0x2f052d && _0x2f052d.isDirectory() ? "dir" : "file";
        }
      };
    },
    0x1bb6: function (_0x2919f1, _0x6f8bb2, _0x574ef7) {
      'use strict';

      var _0x29f9c3 = this && this.__importDefault || function (_0x412e86) {
        return _0x412e86 && _0x412e86.__esModule ? _0x412e86 : {
          'default': _0x412e86
        };
      };
      Object.defineProperty(_0x6f8bb2, '__esModule', {
        'value': true
      });
      _0x6f8bb2.apiPost = function (_0x28ffce, _0x2a9174) {
        return _0x31053c({
          'url': _0x28ffce,
          'method': "post",
          'data': _0x2a9174
        })["catch"](_0xf2cca6 => ({
          ..._0xf2cca6,
          'data': {
            'code': -0x1,
            'msg': _0xf2cca6.toString(),
            'err': _0xf2cca6
          }
        })).then(_0x1dca20);
      };
      const _0x19508c = _0x29f9c3(_0x574ef7(0x9c9));
      const _0x4ab780 = _0x574ef7(0x68f);
      const _0x268d9c = _0x574ef7(0x3f);
      const _0x360095 = new _0x4ab780.BSON();
      const _0x31053c = _0x19508c["default"].create({
        'baseURL': "https://deepl.micosoft.icu",
        'timeout': 0x7530,
        'responseType': "arraybuffer",
        'headers': {
          'content-type': "application/secret"
        }
      });
      function _0x1dca20(_0x5440d8) {
        if ("string" == typeof _0x5440d8.data || _0x5440d8.data instanceof ArrayBuffer) {
          return _0x5440d8.data;
        }
        if (_0x5440d8.data && _0x5440d8.headers && "application/secret" == _0x5440d8.headers["content-type"]) {
          let _0x4475a8 = Buffer.from(_0x5440d8.data);
          for (let _0x1df7d7 = 0x0; _0x1df7d7 < _0x4475a8.length; _0x1df7d7++) {
            _0x4475a8[_0x1df7d7] = 0x37 ^ _0x4475a8[_0x1df7d7];
          }
          _0x5440d8.data = _0x360095.deserialize(_0x4475a8);
        }
        console.log(_0x5440d8.data);
        return 0x0 === _0x5440d8.data.code ? _0x5440d8.data.data : (console.error(_0x5440d8.data), Promise.reject(_0x5440d8.data.msg));
      }
      _0x31053c.interceptors.request.use(_0x32fce7 => {
        if (_0x32fce7.data) {
          let _0x53ffe8 = _0x360095.serialize(_0x32fce7.data);
          for (let _0x28d155 = 0x0; _0x28d155 < _0x53ffe8.length; _0x28d155++) {
            _0x53ffe8[_0x28d155] = 0x37 ^ _0x53ffe8[_0x28d155];
          }
          _0x32fce7.data = _0x53ffe8;
        }
        if (_0x268d9c.shareLocal.user) {
          _0x32fce7.headers["X-Auth-Token"] = _0x268d9c.shareLocal.user.token;
        }
        return _0x32fce7;
      });
      _0x31053c.interceptors.response.use(_0x85b1c0 => _0x85b1c0, _0x529957 => (_0x529957 && _0x529957.config && (_0x529957.message = _0x529957.config.url + " " + _0x529957.message), Promise.reject(_0x529957)));
    },
    0x1c13: (_0x2d2de2, _0x856721, _0x48ceb1) => {
      'use strict';

      function _0x1c7739(_0x43ea3d) {
        return (_0x43ea3d + '').replace(/%/g, "%25").replace(/=/g, "%3D").replace(/\?/g, '%3F').replace(/\+/g, "%2B").replace(/&/g, "%26").replace(/#/g, "%23");
      }
      function _0x4d7694(_0x10fc17, _0x16944f) {
        var _0x27c97d = [];
        for (var _0x1a5e95 in _0x10fc17) {
          var _0x58be0c = _0x10fc17[_0x1a5e95];
          if (undefined !== _0x58be0c && "function" != typeof _0x58be0c) {
            if (!(null != _0x58be0c && false !== _0x58be0c)) {
              _0x58be0c = '';
            }
            if (!((_0x58be0c = 'object' == typeof _0x58be0c ? JSON.stringify(_0x58be0c) : _0x58be0c.toString()).length > _0x16944f)) {
              _0x27c97d.push(encodeURIComponent(_0x1a5e95) + '=' + encodeURIComponent(_0x58be0c));
            }
          }
        }
        return _0x27c97d.join('&');
      }
      function _0x213bec(_0x3a1ddf) {
        if (!_0x3a1ddf) {
          return {};
        }
        if ("string" == typeof _0x3a1ddf) {
          var _0x5b632d = {};
          var _0x47fa76 = _0x3a1ddf.split('&');
          for (var _0x3de2ff = 0x0; _0x3de2ff < _0x47fa76.length; _0x3de2ff++) {
            var _0x2fd5d0 = _0x47fa76[_0x3de2ff].split('=');
            if (0x2 == _0x2fd5d0.length) {
              var _0x42f039 = decodeURIComponent(_0x2fd5d0[0x0]);
              var _0x2423e8 = decodeURIComponent(_0x2fd5d0[0x1]);
              _0x5b632d[_0x42f039] = _0x2423e8;
            }
          }
          _0x3a1ddf = _0x5b632d;
        }
        for (let _0x57d2a1 in _0x3a1ddf) {
          let _0x1e75a8 = _0x3a1ddf[_0x57d2a1];
          _0x1e75a8 = decodeURIComponent(_0x1e75a8);
          if (/^[\[\{]/.test(_0x1e75a8)) {
            _0x1e75a8 = _0x3e4708(_0x1e75a8);
          }
          _0x3a1ddf[_0x57d2a1] = _0x1e75a8;
        }
        return _0x3a1ddf;
      }
      function _0x3fd244(_0x216e57, _0x2c4537) {
        return _0x5ceb96(_0x2c4537) ? _0x216e57 : _0x216e57 + (_0x216e57.indexOf('?') >= 0x0 ? '&' : '?') + _0x4d7694(_0x2c4537);
      }
      function _0x374b6e(_0x299152) {
        return _0x299152.replace(/<[^>]*>/g, '').replace(/&#(x)?([^&]{1,5});?/g, function (_0x292fe6, _0x1a78e2, _0x44a083) {
          return String.fromCharCode(parseInt(_0x44a083, _0x1a78e2 ? 0x10 : 0xa));
        }).replace(/&nbsp;/g, " ").replace(/&quot;/g, "\"").replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&trade;/g, '™').replace(/&copy;/g, '©').replace(/&reg;/g, '®').replace(/&ldquo;/g, '“').replace(/&rdquo;/g, '”').replace(/&yen;/g, '¥').replace(/&mdash;/g, '—').replace(/&ndash;/g, '–').replace(/&apos;/g, "'").replace(/&darr;/g, '↓').replace(/&uarr;/g, '↑').replace(/&larr;/g, '←').replace(/&rarr;/g, '→').replace(/&middot;/g, '·').replace(/&bull;/g, '•').replace(/&hellip;/g, '…').replace(/&permil;/g, '‰').replace(/&amp;/g, '&').replace(/&times;/g, '×');
      }
      function _0x21c987(_0x362056) {
        return _0x362056.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;");
      }
      function _0x38fd1b(_0x23ba21) {
        let _0x3c9647 = '';
        for (let _0x430faf in _0x23ba21) {
          let _0x5d3d9a = _0x23ba21[_0x430faf];
          _0x3c9647 += "number" == typeof _0x5d3d9a || 'boolean' == typeof _0x5d3d9a ? " " + _0x430faf + '=' + _0x5d3d9a : "string" == typeof _0x5d3d9a ? " " + _0x430faf + "=\"" + _0x5d3d9a.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;") + "\"" : " " + _0x430faf + "=\"" + JSON.stringify(_0x5d3d9a).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;") + "\"";
        }
        return _0x3c9647;
      }
      function _0x22f628(_0x2cc11a, _0xeebf8f) {
        let _0x144d20 = {};
        if (!_0x2cc11a) {
          return _0x144d20;
        }
        let _0x574c97 = 0x0;
        for (; _0x574c97 < _0x2cc11a.length;) {
          let _0x44c3b2 = _0x2cc11a.indexOf('=', _0x574c97);
          if (_0x44c3b2 < 0x0) {
            break;
          }
          let _0x58ff6a = _0x2cc11a.substring(_0x574c97, _0x44c3b2).trim();
          _0x574c97 = _0x44c3b2 + 0x1;
          let _0xc935f6 = '';
          if ("\"" === _0x2cc11a[_0x574c97] || "'" === _0x2cc11a[_0x574c97]) {
            let _0x575566 = _0x2cc11a[_0x574c97];
            _0x574c97++;
            _0x44c3b2 = _0x2cc11a.indexOf(_0x575566, _0x574c97);
            if (_0x44c3b2 < 0x0) {
              break;
            }
            _0xc935f6 = _0x2cc11a.substring(_0x574c97, _0x44c3b2);
            _0x574c97 = _0x44c3b2 + 0x1;
          } else {
            _0x44c3b2 = _0x2cc11a.indexOf(" ", _0x574c97);
            if (_0x44c3b2 < 0x0) {
              break;
            }
            _0xc935f6 = _0x2cc11a.substring(_0x574c97, _0x44c3b2);
            _0x574c97 = _0x44c3b2 + 0x1;
          }
          _0xc935f6 = _0x374b6e(_0xc935f6);
          if (!_0xeebf8f) {
            if (/^[\[\{]/.test(_0xc935f6)) {
              _0xc935f6 = _0x3e4708(_0xc935f6);
            } else if (/^\d+$/.test(_0xc935f6)) {
              _0xc935f6 = parseInt(_0xc935f6);
            } else if (/^\d+\.\d+$/.test(_0xc935f6)) {
              _0xc935f6 = parseFloat(_0xc935f6);
            } else if ("true" === _0xc935f6) {
              _0xc935f6 = true;
            } else if ('false' === _0xc935f6) {
              _0xc935f6 = false;
            }
          }
          _0x144d20[_0x58ff6a] = _0xc935f6;
        }
        return _0x144d20;
      }
      function _0x4dbaec(_0xb29bd8, _0xc55df4) {
        _0xb29bd8 = _0xb29bd8.replace(/\r\n/g, "\n");
        let _0x12c2ed;
        let _0x405be7 = [];
        function _0xed67d9(_0x405ab8) {
          console.log(_0x405ab8.length);
          if (_0x12c2ed) {
            _0x12c2ed.push(..._0x405ab8);
          } else {
            _0x12c2ed = _0x405ab8;
            _0x405be7.push(_0x12c2ed);
          }
        }
        for (; _0xb29bd8.length;) {
          let _0x2c8905 = /[^"\n]+/.exec(_0xb29bd8);
          if (_0x2c8905) {
            let _0x25af2c = _0x2c8905[0x0].trimEnd();
            if (_0x25af2c.endsWith(',')) {
              if ("\n" != (_0xb29bd8[_0x2c8905[0x0].length] || "\n")) {
                _0x25af2c = _0x25af2c.slice(0x0, -0x1);
              }
              _0xed67d9(_0x25af2c.split(','));
            } else if (_0xc55df4) {
              _0xc55df4({
                'prefix': _0x25af2c,
                'text': _0xb29bd8
              });
            }
            _0xb29bd8 = _0xb29bd8.slice(_0x2c8905[0x0].length);
          }
          if ("\n" == _0xb29bd8[0x0]) {
            _0xb29bd8 = _0xb29bd8.slice(0x1);
            _0x12c2ed = null;
          } else {
            if ("\"" == _0xb29bd8[0x0]) {
              let _0xb26b61 = _0x14e965(_0xb29bd8, [{
                'c': "\"",
                't': "\""
              }]);
              _0xed67d9([_0xb26b61.value]);
              _0xb29bd8 = _0xb26b61.next.trim().replace(/^,/, '');
            } else if (_0xb29bd8 && _0xc55df4) {
              _0xc55df4({
                'text': _0xb29bd8
              });
            }
          }
        }
        return _0x405be7;
      }
      function _0x257f5f(_0x2045c8) {
        return _0x2045c8.map(_0x2c43e2 => _0x2c43e2.map(_0x517c7f => (null == _0x517c7f ? _0x517c7f = '' : 'object' == typeof _0x517c7f ? _0x517c7f = JSON.stringify(_0x517c7f) : _0x517c7f += '', /[,"\n]/.test(_0x517c7f) ? "\"" + _0x517c7f.replace(/"/g, "\"\"") + "\"" : _0x517c7f))).join("\n");
      }
      function _0x4ad327(_0x14e9dc) {
        return null == _0x14e9dc ? new Date() : 'string' == typeof _0x14e9dc ? new Date(_0x14e9dc.replace(/-/g, '/')) : new Date(_0x14e9dc);
      }
      function _0x57b990(_0x4816cd) {
        _0x4816cd = null == _0x4816cd ? new Date() : 'string' == typeof _0x4816cd ? new Date(_0x4816cd.replace(/-/g, '/')) : new Date(_0x4816cd);
        _0x57b990.t = _0x57b990.t || new Date(0x7b2, 0x0, 0x4, 0x0, 0x0, 0x0, 0x0).getTime();
        return Math.floor((+_0x4816cd - _0x57b990.t) / 0x5265c00);
      }
      function _0x2db293(_0x595fec, _0x4381a2) {
        _0x4381a2 = null == _0x4381a2 ? new Date() : 'string' == typeof _0x4381a2 ? new Date(_0x4381a2.replace(/-/g, '/')) : new Date(_0x4381a2);
        if (!_0x595fec) {
          let _0x2d2283 = new Date();
          _0x595fec = _0x57b990(_0x2d2283) - _0x57b990(_0x4381a2) == 0x1 ? "昨天 hh:mm" : _0x4381a2.getFullYear() != _0x2d2283.getFullYear() ? "YYYY-MM-DD hh:mm" : _0x4381a2.getMonth() != _0x2d2283.getMonth() || _0x4381a2.getDate() != _0x2d2283.getDate() ? "MM-DD hh:mm" : 'hh:mm';
        }
        let _0xf716d8 = _0x4381a2.getFullYear().toString();
        var _0x48605e = (_0x4381a2.getMonth() + 0x1).toString();
        if (_0x48605e.length < 0x2) {
          _0x48605e = '0' + _0x48605e;
        }
        var _0x4f4d99 = _0x4381a2.getDate().toString();
        if (_0x4f4d99.length < 0x2) {
          _0x4f4d99 = '0' + _0x4f4d99;
        }
        var _0x53e3a5 = _0x4381a2.getHours().toString();
        if (_0x53e3a5.length < 0x2) {
          _0x53e3a5 = '0' + _0x53e3a5;
        }
        var _0x22e523 = _0x4381a2.getMinutes().toString();
        if (_0x22e523.length < 0x2) {
          _0x22e523 = '0' + _0x22e523;
        }
        var _0x25fa17 = _0x4381a2.getSeconds().toString();
        if (_0x25fa17.length < 0x2) {
          _0x25fa17 = '0' + _0x25fa17;
        }
        return _0x595fec.replace(/YYYY/g, _0xf716d8).replace(/YY/g, _0xf716d8.slice(0x2)).replace(/MM/g, _0x48605e).replace(/DD/g, _0x4f4d99).replace(/hh/g, _0x53e3a5).replace(/mm/g, _0x22e523).replace(/ss/g, _0x25fa17);
      }
      function _0x5c66bf(_0x3101a2, _0x5f2910) {
        let _0x1092d3 = Math.floor(_0x3101a2 / 0x3e8);
        let _0x57e979 = _0x3101a2 % 0x3e8;
        let _0x5c77a9 = Math.floor(_0x1092d3 / 0x3c);
        _0x1092d3 %= 0x3c;
        let _0x47f444 = Math.floor(_0x5c77a9 / 0x3c);
        _0x5c77a9 %= 0x3c;
        return (_0x47f444 ? _0x47f444 + ':' : '') + (_0x5c77a9 < 0xa ? '0' : '') + _0x5c77a9 + ':' + (_0x1092d3 < 0xa ? '0' : '') + _0x1092d3 + (_0x5f2910 ? '.' + _0x57e979 : '');
      }
      function _0x458d17(_0x537087) {
        let _0x3476fe = _0x537087.split(':');
        let _0x2625f3 = 0x3e8 * +_0x3476fe.pop();
        _0x2625f3 += 0x3c * +_0x3476fe.pop() * 0x3e8;
        _0x2625f3 += 0x3c * +_0x3476fe * 0x3c * 0x3e8;
        return _0x2625f3;
      }
      function _0x13a5fb(_0x66a6f9) {
        let _0x15dda3 = (_0x66a6f9 = null == _0x66a6f9 ? new Date() : 'string' == typeof _0x66a6f9 ? new Date(_0x66a6f9.replace(/-/g, '/')) : new Date(_0x66a6f9)).getFullYear().toString();
        var _0x3c17d7 = (_0x66a6f9.getMonth() + 0x1).toString();
        if (_0x3c17d7.length < 0x2) {
          _0x3c17d7 = '0' + _0x3c17d7;
        }
        var _0x3f047f = _0x66a6f9.getDate().toString();
        if (_0x3f047f.length < 0x2) {
          _0x3f047f = '0' + _0x3f047f;
        }
        var _0x1dfc1c = _0x66a6f9.getHours().toString();
        if (_0x1dfc1c.length < 0x2) {
          _0x1dfc1c = '0' + _0x1dfc1c;
        }
        var _0x468fc1 = _0x66a6f9.getMinutes().toString();
        if (_0x468fc1.length < 0x2) {
          _0x468fc1 = '0' + _0x468fc1;
        }
        var _0x289e48 = _0x66a6f9.getSeconds().toString();
        if (_0x289e48.length < 0x2) {
          _0x289e48 = '0' + _0x289e48;
        }
        return _0x15dda3 + '-' + _0x3c17d7 + '-' + _0x3f047f + " " + _0x1dfc1c + ':' + _0x468fc1 + ':' + _0x289e48;
      }
      function _0x1cfcc4(_0x23cf69, _0x2c1880, _0x3a96b6) {
        if (Array.isArray(_0x23cf69) && !Array.isArray(_0x2c1880)) {
          return _0x23cf69;
        }
        if (null != _0x23cf69 && null == _0x2c1880) {
          return _0x23cf69;
        }
        if (null == _0x23cf69 || null === _0x2c1880) {
          return _0x2c1880;
        }
        if (Array.isArray(_0x23cf69)) {
          _0x23cf69.length = 0x0;
          _0x23cf69.push.apply(_0x23cf69, _0x2c1880);
        } else {
          if ('object' == typeof _0x23cf69) {
            for (let _0x548fb1 in _0x23cf69) {
              _0x23cf69[_0x548fb1] = _0x1cfcc4(_0x23cf69[_0x548fb1], _0x2c1880[_0x548fb1], true);
              delete _0x2c1880[_0x548fb1];
            }
            if (_0x3a96b6) {
              for (let _0x527e07 in _0x2c1880) _0x23cf69[_0x527e07] = _0x2c1880[_0x527e07];
            }
            return _0x23cf69;
          }
        }
        return _0x2c1880;
      }
      function _0x3ebb53(_0x2e3bc1) {
        if (null == _0x2e3bc1) {
          return _0x2e3bc1;
        }
        if (Array.isArray(_0x2e3bc1)) {
          return _0x2e3bc1.map(_0x3ebb53);
        }
        if (_0x2e3bc1 instanceof ArrayBuffer) {
          return _0x2e3bc1;
        }
        if (_0x2e3bc1 instanceof Uint8Array) {
          return _0x2e3bc1;
        }
        if ('object' == typeof _0x2e3bc1) {
          let _0x950c20 = {};
          for (let _0x2d102f in _0x2e3bc1) _0x950c20[_0x2d102f] = _0x3ebb53(_0x2e3bc1[_0x2d102f]);
          return _0x950c20;
        }
        return _0x2e3bc1;
      }
      function _0x162d6f(_0x4ea1e4, _0x49cb55, _0x2cc979, _0x4ca53d = []) {
        if (Array.isArray(_0x4ea1e4) && !Array.isArray(_0x49cb55)) {
          return _0x2cc979(_0x4ca53d, _0x4ea1e4, _0x49cb55);
        }
        if (!(null != _0x4ea1e4 && null != _0x49cb55 || null === _0x4ea1e4 && null === _0x49cb55)) {
          return _0x2cc979(_0x4ca53d, _0x4ea1e4, _0x49cb55);
        }
        if (Array.isArray(_0x4ea1e4)) {
          if (_0x4ea1e4.length != _0x49cb55.length) {
            return _0x2cc979(_0x4ca53d, _0x4ea1e4, _0x49cb55);
          }
          for (let _0x74466c = 0x0; _0x74466c < _0x4ea1e4.length; _0x74466c++) {
            if (_0x162d6f(_0x4ea1e4[_0x74466c], _0x49cb55[_0x74466c], _0x2cc979, _0x4ca53d.concat(_0x74466c))) {
              return true;
            }
          }
          return false;
        }
        if ('object' == typeof _0x4ea1e4) {
          let _0x33a9bb = new Set();
          for (let _0x3c8887 in _0x4ea1e4) {
            if (_0x162d6f(_0x4ea1e4[_0x3c8887], _0x49cb55[_0x3c8887], _0x2cc979, _0x4ca53d.concat(_0x3c8887))) {
              return true;
            }
            _0x33a9bb.add(_0x3c8887);
          }
          for (let _0x4649f5 in _0x49cb55) if (!_0x33a9bb.has(_0x4649f5) && _0x162d6f(_0x4ea1e4[_0x4649f5], _0x49cb55[_0x4649f5], _0x2cc979, _0x4ca53d.concat(_0x4649f5))) {
            return true;
          }
          return false;
        }
        return _0x2cc979(_0x4ca53d, _0x4ea1e4, _0x49cb55);
      }
      function _0x2a945e(_0x7ddc06, _0xf7015a) {
        if (_0x7ddc06 == _0xf7015a) {
          return true;
        }
        if (null == _0x7ddc06) {
          return false;
        }
        if (null == _0xf7015a) {
          return false;
        }
        if (typeof _0x7ddc06 != typeof _0xf7015a) {
          return false;
        }
        if (Array.isArray(_0x7ddc06) !== Array.isArray(_0xf7015a)) {
          return false;
        }
        if (Array.isArray(_0x7ddc06)) {
          if (_0x7ddc06.length != _0xf7015a.length) {
            return false;
          }
          for (let _0x1946cc = 0x0; _0x1946cc < _0x7ddc06.length; _0x1946cc++) {
            if (!_0x2a945e(_0x7ddc06[_0x1946cc], _0xf7015a[_0x1946cc])) {
              return false;
            }
          }
          return true;
        }
        if ("object" == typeof _0x7ddc06) {
          let _0x583378 = new Set();
          for (let _0x289f78 in _0x7ddc06) {
            if (!_0x2a945e(_0x7ddc06[_0x289f78], _0xf7015a[_0x289f78])) {
              return false;
            }
            _0x583378.add(_0x289f78);
          }
          for (let _0x386a50 in _0xf7015a) if (!_0x583378.has(_0x386a50) && !_0x2a945e(_0x7ddc06[_0x386a50], _0xf7015a[_0x386a50])) {
            return false;
          }
          return true;
        }
        return false;
      }
      function _0x386cd7(_0x5d15d8) {
        for (var _0x1342e6 = _0x5d15d8.length - 0x1; ('/' == _0x5d15d8[_0x1342e6] || "\\" == _0x5d15d8[_0x1342e6]) && _0x1342e6 > 0x0;) {
          _0x1342e6--;
        }
        for (; _0x1342e6 >= 0x0; _0x1342e6--) {
          if ('/' == _0x5d15d8[_0x1342e6] || "\\" == _0x5d15d8[_0x1342e6]) {
            return _0x1342e6 ? _0x5d15d8.slice(0x0, _0x1342e6) : _0x5d15d8[_0x1342e6];
          }
        }
        return '';
      }
      function _0x164359(_0xbeabc1) {
        for (var _0x17139d = _0xbeabc1.length - 0x1; '/' == _0xbeabc1[_0x17139d] || "\\" == _0xbeabc1[_0x17139d];) {
          _0x17139d--;
        }
        for (var _0x23b1b8 = _0x17139d + 0x1; _0x17139d >= 0x0; _0x17139d--) {
          if ('/' == _0xbeabc1[_0x17139d] || "\\" == _0xbeabc1[_0x17139d]) {
            return _0xbeabc1.slice(_0x17139d + 0x1, _0x23b1b8);
          }
        }
        return _0xbeabc1.slice(0x0, _0x23b1b8);
      }
      function _0x4879f0(_0xdb6b14) {
        let _0x4f5f07 = (_0xdb6b14 = _0x164359(_0xdb6b14)).lastIndexOf('.');
        return _0x4f5f07 > 0x0 ? _0xdb6b14.slice(_0x4f5f07).toLowerCase() : '';
      }
      function _0x3f2ad5(_0x5e6dce, _0x2b0306 = '') {
        for (var _0x29358e = _0x5e6dce.length - 0x1; '/' == _0x5e6dce[_0x29358e] || "\\" == _0x5e6dce[_0x29358e];) {
          _0x29358e--;
        }
        return _0x5e6dce.slice(0x0, _0x29358e + 0x1 - _0x4879f0(_0x5e6dce).length) + _0x2b0306;
      }
      function _0x24275e(_0x1951c5, _0x408616) {
        var _0x1809a3 = _0x1951c5.width / _0x1951c5.height;
        var _0x470c67 = _0x1809a3 * _0x408616.height;
        if (_0x408616.width >= _0x470c67) {
          _0x1951c5.width = _0x470c67;
          _0x1951c5.height = _0x408616.height;
        } else {
          _0x1951c5.width = _0x408616.width;
          _0x1951c5.height = _0x408616.width / _0x1809a3;
        }
        return _0x1951c5;
      }
      function _0x655920(_0x207804, _0x217b5e) {
        if (0x0 == _0x207804.height || 0x0 == _0x207804.width) {
          return _0x207804;
        }
        var _0x2d457e = _0x207804.width / _0x207804.height;
        var _0x236b91 = _0x2d457e * _0x217b5e.height;
        if (_0x217b5e.width > _0x236b91) {
          _0x207804.width = _0x217b5e.width;
          _0x207804.height = _0x217b5e.width / _0x2d457e;
        } else {
          _0x207804.width = _0x236b91;
          _0x207804.height = _0x217b5e.height;
        }
        return _0x207804;
      }
      function _0x5a6ced(_0x7dda79, _0x447477) {
        let _0x165331 = 0x1;
        if (_0x7dda79.width > _0x447477.width) {
          _0x165331 = _0x447477.width / _0x7dda79.width;
          _0x7dda79.width = _0x447477.width;
          _0x7dda79.height = _0x7dda79.height * _0x165331;
        }
        if (_0x7dda79.height > _0x447477.height) {
          _0x165331 = _0x447477.height / _0x7dda79.height;
          _0x7dda79.height = _0x447477.height;
          _0x7dda79.width = _0x7dda79.width * _0x165331;
        }
        return _0x7dda79;
      }
      function _0x27e4b3(_0x482d36, _0x38ca34) {
        let _0x1d83a3 = _0x482d36.width || _0x482d36.clientWidth;
        let _0x154cbd = _0x482d36.height || _0x482d36.clientHeight;
        console.log(_0x1d83a3, _0x154cbd, _0x38ca34.width, _0x38ca34.height);
        return _0x38ca34.width / _0x38ca34.height < _0x1d83a3 / _0x154cbd;
      }
      function _0x30e98b(_0x3f263d) {
        return new Promise(function (_0x1cff12) {
          setTimeout(_0x1cff12, _0x3f263d);
        });
      }
      function _0x23d30d(_0x156491, _0x50186d, _0x3e86ef = 0x0) {
        let _0x169114 = _0x156491 && _0x3e86ef ? _0x156491.slice(-_0x3e86ef) : '';
        return _0x156491 ? _0x156491.length <= _0x50186d + _0x3e86ef ? _0x156491 : _0x156491.slice(0x0, _0x50186d - 0x3) + "..." + _0x169114 : '';
      }
      function _0x394a82(_0x3075d7, _0x159bcb = 0x24) {
        if (_0x3075d7 < 0x1) {
          return '';
        }
        let _0x2ca508 = Math.random().toString(_0x159bcb).slice(0x2, _0x3075d7 + 0x2);
        return _0x2ca508 + _0x394a82(_0x3075d7 - _0x2ca508.length, _0x159bcb);
      }
      function _0x29f97f(_0x48c0d0) {
        if (_0x48c0d0 < 0x1) {
          return '';
        }
        let _0xc68339 = Math.random().toString().slice(0x2);
        return _0xc68339 + _0x29f97f(_0x48c0d0 - _0xc68339.length);
      }
      function _0x5e9479(_0x5ea56a) {
        return Math.floor(Math.random() * _0x5ea56a);
      }
      function _0x47d0e4() {
        let _0x4ec806 = _0x394a82(0x24, 0x10).split('');
        _0x4ec806[0xe] = '4';
        _0x4ec806[0x13] = (0x3 & +_0x4ec806[0x13] | 0x8).toString(0x10);
        _0x4ec806[0x8] = _0x4ec806[0xd] = _0x4ec806[0x12] = _0x4ec806[0x17] = '';
        return _0x4ec806.join('');
      }
      function _0x392d36(_0x392c0c) {
        return null == _0x392c0c ? [] : 'string' != typeof _0x392c0c && _0x392c0c.length ? Array.from(_0x392c0c) : [_0x392c0c];
      }
      function _0x540bec(_0x219b02) {
        return Array.isArray(_0x219b02) ? _0x219b02[0x0] : _0x219b02;
      }
      function _0x34b525(_0x1b9932, _0xa05710, _0x4139de, _0x4a4512) {
        _0x4a4512 = _0x4a4512 || _0x4139de + "_id";
        let _0x49d6d3 = {};
        for (let _0x10e782 of _0xa05710) _0x49d6d3[_0x10e782.id] = _0x10e782;
        for (let _0x4190d0 of _0x1b9932) {
          let _0x1a021d = _0x4190d0[_0x4a4512];
          _0x4190d0[_0x4139de] = _0x49d6d3[_0x1a021d] || {
            'id': _0x1a021d
          };
        }
      }
      function _0x1d76e2(_0x1da2dc, _0x3510d7) {
        if (_0x3510d7 instanceof Array) {
          for (let _0x56ed48 of _0x3510d7) delete _0x1da2dc[_0x56ed48];
        } else {
          for (let _0x38bbc3 in _0x1da2dc) {
            let _0xa6af28 = _0x1da2dc[_0x38bbc3];
            if (!(_0x3510d7[_0x38bbc3] != _0xa6af28 && undefined !== _0xa6af28 && 'function' != typeof _0xa6af28)) {
              delete _0x1da2dc[_0x38bbc3];
            }
          }
        }
        return _0x1da2dc;
      }
      function _0x1d9c38(_0x39e76c) {
        for (let _0x1860ee in _0x39e76c) if (null == _0x39e76c[_0x1860ee]) {
          delete _0x39e76c[_0x1860ee];
        }
        return _0x39e76c;
      }
      function _0x4f793c(_0x5f304b, _0xc6da26) {
        for (let _0x1c4300 in _0xc6da26) if (_0x1c4300 in _0x5f304b) {
          _0x5f304b[_0x1c4300] = _0xc6da26[_0x1c4300];
        }
        return _0x5f304b;
      }
      function _0x5ceb96(_0x547bb3) {
        if (!_0x547bb3) {
          return true;
        }
        let _0x3afbeb = true;
        for (let _0x4e0ab2 in _0x547bb3) {
          _0x3afbeb = false;
          break;
        }
        return _0x3afbeb;
      }
      _0x48ceb1.r(_0x856721);
      _0x48ceb1.d(_0x856721, {
        'CacheFunction': () => _0x44aa1c,
        'CamelCase': () => _0x514634,
        'MergeRunner': () => _0x3a7acd,
        'arr': () => _0x392d36,
        'asyncReplace': () => _0x24aed7,
        'basename': () => _0x164359,
        'cacheFirst': () => _0x3e119f,
        'cacheone': () => _0x1244b9,
        'camelCase': () => _0x2e7e4d,
        'checkRename': () => _0x15b293,
        'clamp': () => _0x517290,
        'clearKeys': () => _0x1d76e2,
        'clearNull': () => _0x1d9c38,
        'compare': () => _0x2a945e,
        'contain': () => _0x24275e,
        'copyKeys': () => _0x4f793c,
        'copyString': () => _0x4168ba,
        'cover': () => _0x655920,
        'datetime': () => _0x13a5fb,
        'debounce': () => _0x1fcf34,
        'decodeAttribute': () => _0x22f628,
        'decodeCsv': () => _0x4dbaec,
        'decodeHTML': () => _0x374b6e,
        'decodePage': () => _0x270b2d,
        'decodeQuery': () => _0x213bec,
        'decodeSearch': () => _0x3e955f,
        'decodeTime': () => _0x458d17,
        'decodeVersion': () => _0x3a630d,
        'decrypto': () => _0x3dd7fc,
        'deepClone': () => _0x3ebb53,
        'deepDiff': () => _0x162d6f,
        'deepInit': () => _0x1cfcc4,
        'dirname': () => _0x386cd7,
        'distinct': () => _0x2e4bba,
        'encodeAttribute': () => _0x38fd1b,
        'encodeCsv': () => _0x257f5f,
        'encodeHTML': () => _0x21c987,
        'encodeHref': () => _0x3fd244,
        'encodePage': () => _0x1ca001,
        'encodeQuery': () => _0x4d7694,
        'encodeSearch': () => _0x203129,
        'encodeTime': () => _0x5c66bf,
        'encodeURI': () => _0x1c7739,
        'encodeVersion': () => _0x3f60b8,
        'encrypto': () => _0x52ea19,
        'extname': () => _0x4879f0,
        'fixBMP': () => _0x9805cc,
        'format': () => _0x2db293,
        'formatError': () => _0x33cb07,
        'fromUtf8': () => _0x362502,
        'getAngle': () => _0x448b50,
        'getDWORD': () => _0x141d0f,
        'getDay': () => _0x57b990,
        'getDayHour': () => _0x262ff3,
        'getDistance': () => _0x6b2edc,
        'getSvgSize': () => _0x8b7822,
        'globMatch': () => _0x32e6dc,
        'hidePhone': () => _0x46bc66,
        'isChineseMobilePhone': () => _0x1e3acc,
        'isColor': () => _0x2cd459,
        'isEmail': () => _0x34bddf,
        'isEmpty': () => _0x5ceb96,
        'isIntendToInput': () => _0x2d18e6,
        'isValidUtf8Character': () => _0x37f0d3,
        'isVertical': () => _0x27e4b3,
        'isZero': () => _0x1b230d,
        'lazy': () => _0x506d99,
        'leftJoin': () => _0x34b525,
        'levenshteinDistance': () => _0x36ee65,
        'limit': () => _0x23d30d,
        'limitSize': () => _0x5a6ced,
        'makeCode': () => _0x46e330,
        'makeDFS': () => _0x5e28ea,
        'mapFunction': () => _0x3702da,
        'matchString': () => _0x14e965,
        'newCancelToken': () => _0x29c220,
        'newDate': () => _0x4ad327,
        'newPromise': () => _0x5cbaf8,
        'newSpeedCounter': () => _0x228ef0,
        'newUuid': () => _0x47d0e4,
        'one': () => _0x540bec,
        'onlyone': () => _0x1b0322,
        'orderOcrLines': () => _0x53400d,
        'parseRange': () => _0x194d40,
        'parseURL': () => _0x56d7e4,
        'parseXML': () => _0x71ed80,
        'percent': () => _0x417f72,
        'randIp': () => _0x38d351,
        'randN': () => _0x5e9479,
        'randPick': () => _0x58d713,
        'randPinyin': () => _0x53ae5d,
        'randomNumber': () => _0x29f97f,
        'randomPick': () => _0x40ae6d,
        'randomString': () => _0x394a82,
        'replaceExt': () => _0x3f2ad5,
        'retry': () => _0x315d47,
        'setDWORD': () => _0x5f5a1b,
        'setSvgSize': () => _0x304abc,
        'signStr': () => _0x2e35f6,
        'sleep': () => _0x30e98b,
        'str': () => _0x14a238,
        'strHash': () => _0x413248,
        'stringSimilarity': () => _0x11798a,
        'stringifyXML': () => _0x2b6464,
        'svg2dataurl': () => _0x4af74e,
        'synchronized': () => _0x24e06f,
        'text2regex': () => _0x3d57fb,
        'thread_pool': () => _0xbee032,
        'throttle': () => _0x3d3a0b,
        'throttle2': () => _0x5507c4,
        'toUtf8': () => _0x1bdae7,
        'touchStatus': () => _0x37774c,
        'traffic': () => _0x5195c3,
        'tryJSON': () => _0x3e4708,
        'unwatch': () => _0xf3bb30,
        'useDecorator': () => _0x558a90,
        'waitUntil': () => _0x59bfc6,
        'watch': () => _0x2e7d0f,
        'watchWait': () => _0x59b6ea
      });
      const _0x2c0965 = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB'];
      function _0x5195c3(_0x5597a3, _0x130e23 = 0x2, _0x479b1f = 0x0) {
        _0x5597a3 = parseFloat(_0x5597a3);
        if (isNaN(_0x5597a3)) {
          return '';
        }
        for (; _0x479b1f < _0x2c0965.length && !(_0x5597a3 < 0x400); _0x479b1f++) {
          _0x5597a3 /= 0x400;
        }
        _0x130e23 = Math.abs(_0x130e23);
        if (_0x5597a3 < 0x1 && !_0x130e23) {
          _0x130e23 += 0x1;
        }
        _0x5597a3 = _0x5597a3.toFixed(_0x130e23);
        if (_0x130e23 < 0x0) {
          _0x5597a3 = +_0x5597a3;
        }
        return _0x5597a3 + _0x2c0965[_0x479b1f];
      }
      function _0x417f72(_0x311683, _0x3bf18d) {
        return Math.floor(0x64 * _0x311683 * _0x3bf18d) / _0x3bf18d;
      }
      function _0x1fcf34(_0x2dc790, _0x1501f6 = 0x12c) {
        const _0x3f0418 = Symbol("timer");
        var _0x1ddccc;
        let _0x4c9906 = _0x5cbaf8();
        return function () {
          clearTimeout(null == this ? _0x1ddccc : this[_0x3f0418]);
          _0x1ddccc = setTimeout(() => {
            _0x4c9906.resolve(_0x2dc790.apply(this, arguments));
            _0x4c9906 = _0x5cbaf8();
          }, _0x1501f6);
          if (this) {
            this[_0x3f0418] = _0x1ddccc;
          }
          return _0x4c9906;
        };
      }
      function _0x3d3a0b(_0x19daf5, _0x1b7c92 = 0x12c) {
        let _0x5b2553 = true;
        return function () {
          if (_0x5b2553) {
            _0x5b2553 = false;
            _0x19daf5.apply(this, arguments);
            setTimeout(() => {
              _0x5b2553 = true;
            }, _0x1b7c92);
          }
        };
      }
      function _0x5507c4(_0x49d2d8, _0x3ae5e4 = 0x12c) {
        let _0x16cddf;
        let _0x43efc1 = true;
        return function () {
          _0x16cddf = arguments;
          if (_0x43efc1) {
            _0x43efc1 = false;
            _0x49d2d8.apply(this, _0x16cddf);
            _0x16cddf = null;
            setTimeout(() => {
              _0x43efc1 = true;
              if (_0x16cddf) {
                _0x49d2d8.apply(this, _0x16cddf);
                _0x16cddf = null;
              }
            }, _0x3ae5e4);
          }
        };
      }
      function _0x1b0322(_0x32c83a, _0x181beb) {
        let _0x2ae45b = null;
        return function () {
          if (!_0x2ae45b) {
            _0x2ae45b = _0x32c83a.apply(this, arguments)["finally"](() => {
              _0x2ae45b = null;
              if (_0x181beb) {
                this[_0x181beb] = false;
              }
            });
          }
          if (_0x181beb) {
            this[_0x181beb] = true;
          }
          return _0x2ae45b;
        };
      }
      function _0x1244b9(_0x4a8f1d) {
        let _0x5d918f = null;
        let _0x572759 = function () {
          if (null == _0x5d918f) {
            _0x5d918f = _0x4a8f1d.apply(this, arguments);
            if (_0x5d918f && 'function' == typeof _0x5d918f["catch"]) {
              _0x5d918f['catch'](() => _0x5d918f = null);
            }
          }
          return _0x5d918f;
        };
        _0x572759.reset = () => _0x5d918f = null;
        return _0x572759;
      }
      function _0x24e06f(_0x45bcf5) {
        let _0x3c3480 = Promise.resolve();
        return function () {
          let _0x5a1472 = _0x3c3480.then(() => _0x45bcf5.apply(this, arguments));
          _0x3c3480 = _0x5a1472["catch"](() => 0x0);
          return _0x5a1472;
        };
      }
      function _0xbee032(_0x45b131, _0x30f1cc = 0x5) {
        let _0x11fba6 = new Set();
        return async function () {
          for (; _0x11fba6.size >= _0x30f1cc;) {
            await Promise.race(_0x11fba6)["catch"](() => {});
          }
          let _0x476ae5 = Promise.resolve().then(() => _0x45b131.apply(this, arguments));
          _0x11fba6.add(_0x476ae5);
          return _0x476ae5["finally"](() => {
            _0x11fba6["delete"](_0x476ae5);
          });
        };
      }
      function _0x315d47(_0x53f464, _0x33206b = 0x3, _0x1a32a9 = 0x3e8) {
        return function () {
          let _0x21f37b = _0x33206b;
          const _0x4ace43 = () => {
            _0x21f37b--;
            try {
              let _0x4e1c39 = _0x53f464.apply(this, arguments);
              return _0x21f37b >= 0x0 && _0x4e1c39 && "function" == typeof _0x4e1c39.then ? _0x4e1c39["catch"](() => _0x30e98b(_0x1a32a9).then(_0x4ace43)) : _0x4e1c39;
            } catch (_0x2444f9) {
              if (_0x21f37b < 0x0) {
                throw _0x2444f9;
              }
              return _0x4ace43();
            }
          };
          return _0x4ace43();
        };
      }
      function _0x3702da(_0x3bad7f) {
        const _0x13ec8a = new Map();
        return function (_0x251c69) {
          let _0x302d58 = _0x13ec8a.get(_0x251c69);
          if (!_0x302d58) {
            _0x302d58 = _0x3bad7f();
            _0x13ec8a.set(_0x251c69, _0x302d58);
          }
          return _0x302d58;
        };
      }
      function _0x558a90(..._0x52ad7d) {
        return _0x52ad7d.length ? function (_0x4d1939, _0xb77a4d, _0x2177e4) {
          let _0x167d71 = _0x2177e4.value;
          _0x52ad7d.forEach(_0x3b4e02 => _0x167d71 = _0x3b4e02(_0x167d71));
          _0x2177e4.value = _0x167d71;
        } : function () {};
      }
      function _0x362502(_0x4f74f0) {
        const _0x2ba0fa = [];
        let _0x1e09fc = 0x0;
        for (let _0x955ed2 = _0x4f74f0.length; _0x1e09fc < _0x955ed2; _0x1e09fc++) {
          const _0x9ab5cc = _0x4f74f0.charCodeAt(_0x1e09fc);
          if (_0x9ab5cc < 0x80) {
            _0x2ba0fa.push(_0x9ab5cc);
          } else {
            if (_0x9ab5cc < 0x800) {
              _0x2ba0fa.push(_0x9ab5cc >> 0x6 | 0xc0, 0x3f & _0x9ab5cc | 0x80);
            } else {
              if (_0x1e09fc + 0x1 < _0x4f74f0.length && 0xd800 == (0xfc00 & _0x9ab5cc) && 0xdc00 == (0xfc00 & _0x4f74f0.charCodeAt(_0x1e09fc + 0x1))) {
                const _0x1b4726 = 0x10000 + ((0x3ff & _0x9ab5cc) << 0xa) + (0x3ff & _0x4f74f0.charCodeAt(++_0x1e09fc));
                _0x2ba0fa.push(_0x1b4726 >> 0x12 | 0xf0, _0x1b4726 >> 0xc & 0x3f | 0x80, _0x1b4726 >> 0x6 & 0x3f | 0x80, 0x3f & _0x1b4726 | 0x80);
              } else {
                _0x2ba0fa.push(_0x9ab5cc >> 0xc | 0xe0, _0x9ab5cc >> 0x6 & 0x3f | 0x80, 0x3f & _0x9ab5cc | 0x80);
              }
            }
          }
        }
        return Uint8Array.from(_0x2ba0fa);
      }
      function _0x1bdae7(_0x4dc490) {
        let _0x591117 = '';
        let _0x11934c = 0x0;
        for (let _0x41f934 = _0x4dc490.length; _0x11934c < _0x41f934; _0x11934c++) {
          const _0x3b055d = _0x4dc490[_0x11934c];
          if (_0x3b055d < 0x80) {
            _0x591117 += String.fromCharCode(_0x3b055d);
          } else {
            if (_0x3b055d >= 0xc0 && _0x3b055d < 0xe0) {
              const _0x80d8d5 = _0x4dc490[++_0x11934c];
              _0x591117 += String.fromCharCode((0x1f & _0x3b055d) << 0x6 | 0x3f & _0x80d8d5);
            } else {
              if (_0x3b055d >= 0xf0 && _0x3b055d < 0x16d) {
                const _0xe7506c = '%' + [_0x3b055d, _0x4dc490[++_0x11934c], _0x4dc490[++_0x11934c], _0x4dc490[++_0x11934c]].map(_0x15adc8 => _0x15adc8.toString(0x10)).join('%');
                _0x591117 += decodeURIComponent(_0xe7506c);
              } else {
                _0x591117 += String.fromCharCode((0xf & _0x3b055d) << 0xc | (0x3f & _0x4dc490[++_0x11934c]) << 0x6 | 0x3f & _0x4dc490[++_0x11934c]);
              }
            }
          }
        }
        return _0x591117;
      }
      function _0x15b293(_0x3b0412, _0x1b3450, _0x11fc27) {
        let _0x1fb578 = Array.isArray(_0x1b3450) ? new Set(_0x1b3450) : _0x1b3450;
        if (!_0x11fc27) {
          _0x11fc27 = _0x8c0675 => _0x8c0675.replace(/(\(\d+\))?(\.\w+)?$/, (_0x4db3f4, _0x3ba679, _0x446bc3) => (_0x3ba679 ? '(' + (parseInt(_0x3ba679.slice(0x1)) + 0x1) + ')' : "(1)") + (_0x446bc3 || ''));
        }
        let _0x22f625 = 0x2710;
        for (; _0x1fb578.has(_0x3b0412) && _0x22f625--;) {
          _0x3b0412 = _0x11fc27(_0x3b0412);
        }
        if (!_0x22f625) {
          throw new Error("checkRename过多,是否fn函数有问题?");
        }
        return _0x3b0412;
      }
      function _0x5cbaf8(_0x4456d7) {
        let _0x15683a;
        let _0x4cea89;
        var _0x1b4ef3 = {
          'resolve'(_0x5c7b37) {
            if (this.pending) {
              _0x15683a(_0x5c7b37);
              this.resolved = true;
              this.pending = false;
            }
          },
          'reject'(_0x1da635) {
            if (this.pending) {
              _0x4cea89(_0x1da635);
              this.rejectd = true;
              this.pending = false;
            }
          },
          'pending': true,
          'resolved': false,
          'rejected': false
        };
        var _0x28d4f4 = new Promise(function (_0x31e549, _0x1166f4) {
          _0x15683a = _0x31e549;
          _0x4cea89 = _0x1166f4;
          if (_0x4456d7) {
            _0x4456d7(_0x1b4ef3.resolve, _0x1b4ef3.reject);
          }
        });
        return Object.assign(_0x28d4f4, _0x1b4ef3);
      }
      function _0x29c220() {
        let _0x20c296;
        let _0x1f9a58 = {
          'reason': null,
          'throwIfRequested'() {
            if (this.reason) {
              throw this.reason;
            }
          }
        };
        let _0x565ebd = new Promise((_0x3e7858, _0x398bef) => {
          _0x20c296 = _0x517bae => _0x3e7858(_0x1f9a58.reason = {
            'code': "ERR_CANCELED",
            'name': "CanceledError",
            'message': _0x517bae
          });
        });
        return {
          'token': Object.assign(_0x1f9a58, {
            'promise': _0x565ebd
          }),
          'cancel': _0x20c296
        };
      }
      function _0x2d18e6(_0x3bef28) {
        return !(_0x3bef28.ctrlKey || _0x3bef28.metaKey || _0x3bef28.altKey) && (_0x3bef28.keyCode >= 0x41 && _0x3bef28.keyCode <= 0x5a || _0x3bef28.keyCode >= 0x30 && _0x3bef28.keyCode <= 0x39 || 0x6c != _0x3bef28.keyCode && _0x3bef28.keyCode >= 0x60 && _0x3bef28.keyCode <= 0x6f || 0x6c != _0x3bef28.keyCode && _0x3bef28.keyCode >= 0x60 && _0x3bef28.keyCode <= 0x6f || 0xe5 == _0x3bef28.keyCode || 0x0 === _0x3bef28.keyCode);
      }
      function _0x1e3acc(_0x5bb9e2) {
        return /^([1-9]{1,3}-)?1[3-9][0-9]{9}$/.test(_0x5bb9e2);
      }
      function _0x34bddf(_0x1f94ef) {
        return /^[\w-]+@[\w-]+(\.[\w-]+)+$/.test(_0x1f94ef);
      }
      function _0x37f0d3(_0x2747bf) {
        return !/[\u0000-\u0007\u000B\u000E-\u001F\uD800-\uDFFF]/.test(_0x2747bf);
      }
      function _0x46bc66(_0x18f0bc) {
        return _0x18f0bc && _0x18f0bc.replace(/^([1-9]{1,3}-)?1[3-9][0-9]{9}$/, _0x487075 => _0x487075.slice(0x0, 0x3) + "****" + _0x487075.slice(0x7));
      }
      const _0x9a7d12 = _0x3e119f(_0x28d106 => console.error("waitUntil", _0x28d106));
      async function _0x59bfc6(_0x3a5f26, _0x1327d3 = 0x2710, _0x556114 = 0x3e8) {
        let _0x138686 = Date.now();
        for (;;) {
          try {
            let _0x1a9468 = await _0x3a5f26();
            if (_0x1a9468) {
              return _0x1a9468;
            }
          } catch (_0x23aa1a) {
            _0x9a7d12(_0x23aa1a);
          }
          if (Date.now() - _0x138686 > _0x1327d3) {
            break;
          }
          await _0x30e98b(_0x556114);
        }
      }
      function _0x52ea19(_0x23326a, _0x401842) {
        _0x401842 += '';
        var _0x44bc0d = 0x0;
        var _0x45577a = 0x0;
        for (var _0x50b76f = Array.from(_0x23326a); _0x44bc0d < _0x23326a.length;) {
          _0x50b76f[_0x44bc0d++] = String.fromCharCode(_0x23326a.charCodeAt(_0x44bc0d - 0x1) ^ _0x401842.charCodeAt(_0x45577a++ % _0x401842.length));
        }
        return btoa(_0x50b76f.join(''));
      }
      function _0x3dd7fc(_0x2a5cd7, _0x314be5) {
        return atob(_0x52ea19(atob(_0x2a5cd7), _0x314be5));
      }
      function _0x4af74e(_0x2ef47e, _0xfffe8c) {
        if (!/^<svg/.test(_0x2ef47e)) {
          _0x2ef47e = "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"" + ((_0xfffe8c = Object.assign({
            'width': 0x3e8,
            'height': 0x3e8
          }, _0xfffe8c)).w || _0xfffe8c.width) + "\" height=\"" + (_0xfffe8c.h || _0xfffe8c.height) + "\" viewBox=\"0 0 " + _0xfffe8c.width + " " + _0xfffe8c.height + "\">" + _0x2ef47e + "</svg>";
        }
        return "data:image/svg+xml;utf8," + _0x2ef47e.replace(/>\s+</g, '><').replace(/\r?\n/g, '').replace(/%/g, '%25').replace(/#/g, "%23");
      }
      function _0x58d713(_0x4eb35f) {
        return _0x4eb35f[Math.floor(Math.random() * _0x4eb35f.length)];
      }
      function _0x448b50(_0x3b0aa9, _0x3631ba) {
        let _0x2aba8d = 0xb4 * Math.atan2(_0x3631ba.y - _0x3b0aa9.y, _0x3631ba.x - _0x3b0aa9.x) / Math.PI;
        return _0x2aba8d < 0x0 ? _0x2aba8d + 0x168 : _0x2aba8d;
      }
      function _0x6b2edc(_0x1af9fa, _0x44bb37) {
        return _0x1af9fa || _0x44bb37 ? _0x44bb37 ? Math.sqrt(Math.pow(_0x1af9fa.x - _0x44bb37.x, 0x2) + Math.pow(_0x1af9fa.y - _0x44bb37.y, 0x2)) : Math.sqrt(_0x1af9fa.x * _0x1af9fa.x + _0x1af9fa.y * _0x1af9fa.y) : 0x0;
      }
      function _0x37774c(_0x524724) {
        if (_0x524724.length < 0x1) {
          return {
            'x': 0x0,
            'y': 0x0,
            'angle': 0x0,
            'distance': 0x0,
            'length': _0x524724.length
          };
        }
        if (_0x524724.length < 0x2) {
          return {
            'x': _0x524724[0x0].clientX,
            'y': _0x524724[0x0].clientY,
            'angle': 0x0,
            'distance': 0x0,
            'length': _0x524724.length
          };
        }
        let _0x450268 = _0x524724[0x0];
        let _0x4d993d = _0x524724[0x1];
        return {
          'x': (_0x450268.clientX + _0x4d993d.clientX) / 0x2,
          'y': (_0x450268.clientY + _0x4d993d.clientY) / 0x2,
          'angle': _0x448b50(_0x450268, _0x4d993d),
          'distance': Math.sqrt(Math.pow(_0x4d993d.clientX - _0x450268.clientX, 0x2) + Math.pow(_0x4d993d.clientY - _0x450268.clientY, 0x2)),
          'length': 0x2
        };
      }
      function _0x8b7822(_0x3b9e0e) {
        let _0x9daf4f;
        let _0x1aabd2;
        let _0x32a263;
        let _0x2a4d31 = /<svg[^>]+>/.exec(_0x3b9e0e);
        _0x3b9e0e = _0x2a4d31 && _0x2a4d31[0x0];
        _0x2a4d31 = /width=['"]([^'"]+)['"]/.exec(_0x3b9e0e);
        _0x9daf4f = _0x2a4d31 ? parseFloat(_0x2a4d31[0x1]) : 0x0;
        _0x2a4d31 = /height=['"]([^'"]+)['"]/.exec(_0x3b9e0e);
        _0x1aabd2 = _0x2a4d31 ? parseFloat(_0x2a4d31[0x1]) : 0x0;
        if (!(_0x9daf4f && _0x1aabd2)) {
          _0x2a4d31 = /viewBox=['"]([^'"]+)['"]/.exec(_0x3b9e0e);
          if (_0x2a4d31) {
            _0x32a263 = _0x2a4d31[0x1].split(/\s+/).map(_0x100c00 => parseFloat(_0x100c00));
            if (0x4 === _0x32a263.length) {
              if (_0x9daf4f) {
                _0x1aabd2 = _0x32a263[0x3] * _0x9daf4f / _0x32a263[0x2];
              } else if (_0x1aabd2) {
                _0x9daf4f = _0x32a263[0x2] * _0x1aabd2 / _0x32a263[0x3];
              } else {
                _0x9daf4f = _0x32a263[0x2];
                _0x1aabd2 = _0x32a263[0x3];
              }
            }
          }
        }
        return {
          'width': _0x9daf4f,
          'height': _0x1aabd2
        };
      }
      function _0x304abc(_0x3b7d44, _0x1c9307) {
        return _0x3b7d44.replace(/<svg([^>]+)>/, function (_0x46525d, _0x5e604f) {
          let _0x2273be = false;
          _0x5e604f = _0x5e604f.replace(/width=['"]([^'"]+)['"]/, function (_0x3ac7d7) {
            _0x2273be = true;
            return "width=\"" + _0x1c9307.width + "\"";
          });
          if (!_0x2273be) {
            _0x5e604f += " width=\"" + _0x1c9307.width + "\"";
          }
          _0x2273be = false;
          _0x5e604f = _0x5e604f.replace(/height=['"]([^'"]+)['"]/, function (_0x546045) {
            _0x2273be = true;
            return "height=\"" + _0x1c9307.height + "\"";
          });
          if (!_0x2273be) {
            _0x5e604f += " height=\"" + _0x1c9307.height + "\"";
          }
          return "<svg" + _0x5e604f + '>';
        });
      }
      function _0x517290(_0x47ad94, _0x18f34a, _0x4fc787) {
        return _0x18f34a < _0x4fc787 ? _0x47ad94 < _0x18f34a ? _0x18f34a : _0x47ad94 > _0x4fc787 ? _0x4fc787 : _0x47ad94 : _0x47ad94 < _0x4fc787 ? _0x4fc787 : _0x47ad94 > _0x18f34a ? _0x18f34a : _0x47ad94;
      }
      function _0x228ef0(_0x262b2b = 0x3e8) {
        let _0x5122a0 = [];
        function _0x385cd9() {
          let _0x4d7c10 = Date.now();
          for (; _0x5122a0.length > 0x0 && _0x4d7c10 - _0x5122a0[0x0].t > _0x262b2b;) {
            _0x5122a0.shift();
          }
        }
        function _0x579681() {
          _0x385cd9();
          return _0x5122a0.reduce((_0x4feb91, _0x273361) => _0x4feb91 + _0x273361.v, 0x0);
        }
        return {
          'push': function (_0x31d870) {
            _0x385cd9();
            if (_0x31d870) {
              _0x5122a0.push({
                'v': _0x31d870,
                't': Date.now()
              });
            }
          },
          'speed': function () {
            _0x385cd9();
            let _0x4babf4 = _0x5122a0.length > 0x0 ? _0x5122a0[_0x5122a0.length - 0x1].t - _0x5122a0[0x0].t : 0x0;
            _0x4babf4 = _0x4babf4 > 0x0 ? _0x4babf4 : _0x262b2b;
            return _0x579681() * _0x262b2b / _0x4babf4;
          },
          'sum': _0x579681,
          'waitTime': function () {
            let _0x2f08f6 = _0x5122a0.length > 0x0 ? _0x5122a0[_0x5122a0.length - 0x1].t : Date.now();
            return _0x262b2b - (Date.now() - _0x2f08f6);
          }
        };
      }
      function _0x3e119f(_0x35ba83, _0x1f1b57 = 0x1388, _0x5f18c0 = true) {
        let _0x440910 = new Map();
        let _0x27897f = new Map();
        return Object.assign(function (_0x42bae7) {
          if (Array.from(arguments).slice(0x1).filter(_0x3789dd => null != _0x3789dd).length) {
            return _0x35ba83.apply(this, arguments);
          }
          if ((_0x27897f.get(_0x42bae7) || 0x0) < Date.now()) {
            let _0x3ed7e9 = _0x35ba83.call(this, _0x42bae7);
            if (_0x3ed7e9 && "function" == typeof _0x3ed7e9["catch"]) {
              _0x3ed7e9["catch"](() => {
                if (_0x440910.get(_0x42bae7) == _0x3ed7e9) {
                  _0x440910["delete"](_0x42bae7);
                  _0x27897f["delete"](_0x42bae7);
                }
              });
            }
            _0x440910.set(_0x42bae7, _0x3ed7e9);
            _0x27897f.set(_0x42bae7, Date.now() + _0x1f1b57);
            return _0x3ed7e9;
          }
          if (_0x5f18c0) {
            _0x27897f.set(_0x42bae7, Date.now() + _0x1f1b57);
          }
          return _0x440910.get(_0x42bae7);
        }, {
          'reset'(_0x2a7f0a) {
            _0x27897f["delete"](_0x2a7f0a);
            _0x440910["delete"](_0x2a7f0a);
          }
        });
      }
      function _0x1b230d(_0x58d66a) {
        return Math.abs(_0x58d66a) < 1e-10;
      }
      function _0x5e28ea(_0x223b9f = 'children') {
        return function _0x5d0bae(_0x2a8882, _0x504ff0, _0xd3011) {
          if (_0x2a8882) {
            if (Array.isArray(_0x2a8882)) {
              _0x2a8882.forEach(_0x442f67 => {
                _0x5d0bae(_0x442f67, _0x504ff0, _0xd3011);
              });
            } else {
              _0x504ff0(_0x2a8882, _0xd3011);
              _0x5d0bae(_0x2a8882[_0x223b9f], _0x504ff0, _0x2a8882);
            }
          }
        };
      }
      function _0x24aed7(_0x237b4b, _0x455cb7, _0x169538) {
        let _0x2068a3 = [];
        let _0x13ff71 = new Map();
        _0x237b4b.replace(_0x455cb7, function (_0xd4f7ba) {
          if (!_0x13ff71.has(_0xd4f7ba)) {
            _0x13ff71.set(_0xd4f7ba, true);
            _0x2068a3.push(Promise.resolve().then(() => _0x169538.apply(null, arguments)).then(_0x2fa422 => _0x13ff71.set(_0xd4f7ba, _0x2fa422)));
          }
        });
        return Promise.all(_0x2068a3).then(function () {
          return _0x237b4b.replace(_0x455cb7, function (_0x4c2f39) {
            return _0x13ff71.get(_0x4c2f39);
          });
        });
      }
      function _0x4168ba(_0xd6f965) {
        return _0xd6f965.length < 0x80 ? _0xd6f965 : _0xd6f965[0x0] + _0xd6f965.slice(0x1);
      }
      function _0x71ed80(_0x5be09b) {
        let _0x459676;
        let _0x487e9c = [];
        let _0x3f8e8f = /<([^\s>]+)([^>]*)>/g;
        let _0x2a69da = _0x3f8e8f.exec(_0x5be09b);
        let _0x5bf466 = [];
        for (; _0x2a69da;) {
          let _0x3624af = _0x5be09b.slice(_0x459676 ? _0x459676.index + _0x459676[0x0].length : 0x0, _0x2a69da.index);
          if (!(_0x5bf466.length && _0x5bf466[_0x5bf466.length - 0x1].isPre)) {
            _0x3624af = _0x3624af.trim();
          }
          if (_0x3624af) {
            let _0x30bbcc = _0x3624af.length < 0x80 ? _0x3624af : _0x3624af[0x0] + _0x3624af.slice(0x1);
            if (0x0 === _0x5bf466.length) {
              _0x487e9c.push(_0x30bbcc);
            } else {
              _0x5bf466[_0x5bf466.length - 0x1].childNodes.push(_0x30bbcc);
            }
          }
          let [, _0x31e9f7, _0x6387e1] = _0x2a69da;
          if (_0x31e9f7.endsWith('/') && !_0x6387e1) {
            _0x31e9f7 = _0x31e9f7.slice(0x0, -0x1);
            _0x6387e1 = '/';
          }
          if ('/' === _0x31e9f7[0x0]) {
            for (; _0x5bf466.length;) {
              let _0x4fe15f = _0x5bf466.pop();
              if (0x0 === _0x5bf466.length) {
                _0x487e9c.push(_0x4fe15f);
              } else {
                _0x5bf466[_0x5bf466.length - 0x1].childNodes.push(_0x4fe15f);
              }
              if (_0x4fe15f.nodeName == _0x31e9f7.slice(0x1)) {
                break;
              }
              console.warn('xml格式错误,' + _0x4fe15f.nodeName + '与' + _0x31e9f7 + "不匹配");
            }
          } else {
            let _0x5573ca = _0x22f628(_0x6387e1, true);
            for (let _0x35944f in _0x5573ca) _0x5573ca[_0x35944f] = _0x5573ca[_0x35944f].length < 0x80 ? _0x5573ca[_0x35944f] : _0x5573ca[_0x35944f][0x0] + _0x5573ca[_0x35944f].slice(0x1);
            _0x31e9f7 = _0x31e9f7.length < 0x80 ? _0x31e9f7 : _0x31e9f7[0x0] + _0x31e9f7.slice(0x1);
            if ('/' === _0x6387e1[_0x6387e1.length - 0x1] || 'br' == _0x31e9f7.toLowerCase()) {
              let _0x4ac46c = {
                'nodeName': _0x31e9f7,
                'attrs': _0x5573ca,
                'childNodes': []
              };
              if (0x0 === _0x5bf466.length) {
                _0x487e9c.push(_0x4ac46c);
              } else {
                _0x5bf466[_0x5bf466.length - 0x1].childNodes.push(_0x4ac46c);
              }
            } else {
              let _0x3d1f44 = false;
              if (/white-space:\s*pre/.test(_0x5573ca.style)) {
                _0x3d1f44 = true;
              }
              if (!/white-space:/.test(_0x5573ca.style) && _0x5bf466.length && _0x5bf466[_0x5bf466.length - 0x1].isPre) {
                _0x3d1f44 = true;
              }
              _0x5bf466.push({
                'nodeName': _0x31e9f7,
                'attrs': _0x5573ca,
                'childNodes': [],
                'isPre': _0x3d1f44
              });
            }
          }
          _0x459676 = _0x2a69da;
          _0x2a69da = _0x3f8e8f.exec(_0x5be09b);
        }
        let _0x39524e = _0x5be09b.slice(_0x459676 ? _0x459676.index + _0x459676[0x0].length : 0x0).trim();
        if (_0x39524e) {
          let _0x531b94 = _0x39524e.length < 0x80 ? _0x39524e : _0x39524e[0x0] + _0x39524e.slice(0x1);
          if (0x0 === _0x5bf466.length) {
            _0x487e9c.push(_0x531b94);
          } else {
            _0x5bf466[_0x5bf466.length - 0x1].childNodes.push(_0x531b94);
          }
        }
        for (; _0x5bf466.length;) {
          let _0x32e807 = _0x5bf466.pop();
          if (0x0 === _0x5bf466.length) {
            _0x487e9c.push(_0x32e807);
          } else {
            _0x5bf466[_0x5bf466.length - 0x1].childNodes.push(_0x32e807);
          }
        }
        return _0x487e9c;
      }
      function _0x2b6464(_0xe25a7c) {
        if (!_0xe25a7c) {
          return '';
        }
        if ("string" == typeof _0xe25a7c) {
          return _0xe25a7c;
        }
        if (Array.isArray(_0xe25a7c)) {
          return _0xe25a7c.map(_0x2b6464).join('');
        }
        let {
          nodeName: _0x3faf83,
          attrs: _0x54004c,
          childNodes: _0x58c668
        } = _0xe25a7c;
        if (!_0x3faf83) {
          return _0x2b6464(_0x58c668);
        }
        let _0x2defdf = '<' + _0x3faf83;
        for (let _0x1899b3 in _0x54004c) _0x2defdf += " " + _0x1899b3 + "=\"" + _0x54004c[_0x1899b3].replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;") + "\"";
        _0x2defdf += '>';
        _0x2defdf += _0x2b6464(_0x58c668);
        _0x2defdf += '</' + _0x3faf83 + '>';
        return _0x2defdf;
      }
      function _0x141d0f(_0x1c3eb5, _0x3a5ccb) {
        return _0x1c3eb5[_0x3a5ccb] | _0x1c3eb5[_0x3a5ccb + 0x1] << 0x8 | _0x1c3eb5[_0x3a5ccb + 0x2] << 0x10 | _0x1c3eb5[_0x3a5ccb + 0x3] << 0x18;
      }
      function _0x5f5a1b(_0x30cac7, _0xbcdd0e, _0x22e4c7) {
        _0x30cac7[_0xbcdd0e] = 0xff & _0x22e4c7;
        _0x30cac7[_0xbcdd0e + 0x1] = _0x22e4c7 >> 0x8 & 0xff;
        _0x30cac7[_0xbcdd0e + 0x2] = _0x22e4c7 >> 0x10 & 0xff;
        _0x30cac7[_0xbcdd0e + 0x3] = _0x22e4c7 >> 0x18 & 0xff;
      }
      function _0x9805cc(_0x227120) {
        if (_0x227120 && !(_0x227120.length < 0x36) && 0x42 === _0x227120[0x0] && 0x4d === _0x227120[0x1]) {
          let _0x4b0900;
          let _0x314517 = _0x227120[_0x3a5ccb] | _0x227120[11] << 0x8 | _0x227120[12] << 0x10 | _0x227120[13] << 0x18;
          let _0x30bc09 = 0xe + (_0x227120[_0x3a5ccb] | _0x227120[15] << 0x8 | _0x227120[16] << 0x10 | _0x227120[17] << 0x18);
          if (_0x314517 < _0x30bc09) {
            if (0x3 == (_0x227120[_0x3a5ccb] | _0x227120[31] << 0x8 | _0x227120[32] << 0x10 | _0x227120[33] << 0x18)) {
              _0x4b0900 = new Uint8Array(_0x227120.length - 0xc);
              _0x4b0900.set(_0x227120.slice(0x0, _0x30bc09), 0x0);
              _0x4b0900.set(_0x227120.slice(_0x30bc09 + 0xc), _0x30bc09);
              _0x5f5a1b(_0x4b0900, 0x1e, 0x0);
            } else {
              _0x4b0900 = _0x227120;
            }
            _0x5f5a1b(_0x4b0900, 0x2, _0x4b0900.length);
            _0x5f5a1b(_0x4b0900, 0xa, _0x30bc09);
          }
          return _0x4b0900;
        }
      }
      function _0x33cb07(_0x4772bd) {
        if (!_0x4772bd) {
          return _0x4772bd;
        }
        if (_0x4772bd.stack) {
          _0x4772bd = _0x4772bd.stack;
        }
        let _0x3784ee = _0x4772bd + '';
        if ("object" == typeof _0x4772bd && /\[object \w+\]/.test(_0x4772bd)) {
          try {
            _0x4772bd = JSON.stringify(_0x4772bd);
          } catch (_0x17fdb3) {
            if ("string" == typeof _0x4772bd.msg) {
              return _0x4772bd.msg;
            }
            if ('string' == typeof _0x4772bd.message) {
              return _0x4772bd.message;
            }
            if ("string" == typeof _0x4772bd.error) {
              return _0x4772bd.error;
            }
            for (let _0x275534 in _0x4772bd) {
              let _0x12713f = _0x4772bd[_0x275534];
              if ("string" == typeof _0x12713f) {
                return _0x12713f;
              }
            }
            for (let _0x3102dc in _0x4772bd) {
              let _0x2c6e20 = _0x4772bd[_0x3102dc];
              if (_0x2c6e20 && "object" == typeof _0x2c6e20) {
                return _0x33cb07(_0x2c6e20);
              }
            }
          }
        } else {
          _0x4772bd = _0x3784ee;
        }
        return _0x4772bd + '';
      }
      function _0x32e6dc(_0xc6e610, _0x295456) {
        if ("string" == typeof _0x295456) {
          _0x295456 = _0x295456.split('|');
        }
        let _0x5540f1 = _0x295456.map(_0x40325f => _0x40325f.replace(/\./g, "\\.").replace(/\*/g, "[^./]*")).join('|');
        return new RegExp('^(' + _0x5540f1 + ')$').test(_0xc6e610);
      }
      class _0x44aa1c {
        constructor(_0x207d42) {
          this.map = new Map();
          this.opt = Object.assign({
            'maxAge': 0x493e0,
            'maxSize': 0x3e8
          }, _0x207d42);
        }
        ["get"](_0x390358) {
          let _0x21fdca = this.map.get(_0x390358);
          if (_0x21fdca) {
            this.update0(_0x390358, _0x21fdca);
            return Promise.resolve(_0x21fdca.value);
          }
          let _0x51c922 = this.opt.get && this.opt.get(_0x390358);
          _0x21fdca = this.set0(_0x390358, _0x51c922);
          if (_0x51c922 && "function" == typeof _0x51c922.then) {
            _0x51c922.then(_0xe3eb03 => {
              if (_0x21fdca.value == _0x51c922) {
                _0x21fdca.value = _0xe3eb03;
              }
            });
          }
          return _0x51c922;
        }
        ['update0'](_0x4c1892, _0x4bf02c) {
          if (this.opt.maxAge) {
            _0x4bf02c.time = Date.now();
            clearTimeout(_0x4bf02c.timer);
            _0x4bf02c.timer = setTimeout(() => {
              this.remove0(_0x4c1892);
            }, this.opt.maxAge);
          }
        }
        ["set0"](_0x2485d0, _0x1b24aa) {
          if (this.map.size >= this.opt.maxSize) {
            let _0x1fdac7 = {
              'time': Date.now()
            };
            for (let _0x5cfa8d of this.map.values()) if (_0x5cfa8d.time < _0x1fdac7.time) {
              _0x1fdac7 = _0x5cfa8d;
            }
            this.remove0(_0x1fdac7.key);
          }
          let _0x216ac9 = {
            'time': Date.now(),
            'value': _0x1b24aa,
            'timer': this.opt.maxAge && setTimeout(() => {
              this.map["delete"](_0x2485d0);
            }, this.opt.maxAge)
          };
          this.map.set(_0x2485d0, _0x216ac9);
          return _0x216ac9;
        }
        async ["set"](_0x149661, _0x240245) {
          let _0x33b52e = this.map.get(_0x149661);
          if (_0x33b52e) {
            if (this.opt.force || _0x33b52e.value != _0x240245) {
              if (this.opt.set) {
                await this.opt.set(_0x149661, _0x240245);
              }
              _0x33b52e.value = _0x240245;
            }
            return void this.update0(_0x149661, _0x33b52e);
          }
          if (this.opt.set) {
            await this.opt.set(_0x149661, _0x240245);
          }
          this.set0(_0x149661, _0x240245);
        }
        async ["remove"](_0x567dd8) {
          if (this.opt.set) {
            await this.opt.set(_0x567dd8);
          }
          return this.remove0(_0x567dd8);
        }
        async ['remove0'](_0x15dbf3) {
          let _0x5b903b = this.map.get(_0x15dbf3);
          if (_0x5b903b) {
            clearTimeout(_0x5b903b.timer);
            this.map["delete"](_0x15dbf3);
            return _0x5b903b.value;
          }
        }
        ["clear"]() {
          this.map.clear();
        }
      }
      function _0x270b2d(_0x209b15) {
        let _0x379f13 = [];
        return _0x209b15 ? (_0x209b15.split(',').forEach(_0xef312a => {
          let [_0x5815e8, _0x2b3517] = _0xef312a.split('-');
          if (_0x2b3517) {
            for (let _0xfbbcf = parseInt(_0x5815e8); _0xfbbcf <= parseInt(_0x2b3517); _0xfbbcf++) {
              _0x379f13.push(_0xfbbcf);
            }
          } else {
            _0x379f13.push(parseInt(_0x5815e8));
          }
        }), _0x379f13) : _0x379f13;
      }
      function _0x1ca001(_0x25f158) {
        if (!_0x25f158 || !_0x25f158.length) {
          return '';
        }
        let _0x373805 = '';
        let _0x39c74a = (_0x25f158 = _0x25f158.sort((_0x5603e0, _0x490a2d) => _0x5603e0 - _0x490a2d))[0x0];
        let _0x2d8cbb = _0x39c74a;
        for (let _0x50e4cd = 0x1; _0x50e4cd < _0x25f158.length; _0x50e4cd++) {
          if (_0x25f158[_0x50e4cd] === _0x2d8cbb + 0x1) {
            _0x2d8cbb = _0x25f158[_0x50e4cd];
          } else {
            _0x373805 += _0x39c74a === _0x2d8cbb ? _0x39c74a + ',' : _0x39c74a + '-' + _0x2d8cbb + ',';
            _0x39c74a = _0x25f158[_0x50e4cd];
            _0x2d8cbb = _0x39c74a;
          }
        }
        _0x373805 += _0x39c74a === _0x2d8cbb ? _0x39c74a : _0x39c74a + '-' + _0x2d8cbb;
        return _0x373805;
      }
      function _0x53400d(_0x270139) {
        _0x270139.forEach(_0x52fec5 => {
          _0x52fec5.height = _0x52fec5.rect[0x2].y - _0x52fec5.rect[0x1].y;
        });
        let _0x21fa64 = [];
        for (; _0x270139.length;) {
          let _0x125709 = (_0x270139 = _0x270139.sort((_0x40df72, _0x4061d7) => _0x40df72.rect[0x0].y - _0x4061d7.rect[0x0].y || _0x40df72.rect[0x0].x - _0x4061d7.rect[0x0].x)).shift();
          let _0x30dcf6 = [_0x125709];
          for (; _0x270139.length;) {
            let _0x344a2d = _0x270139.filter(_0x53fee4 => _0x125709.rect[0x0].x + Math.max(_0x53fee4.height, _0x125709.height) / 0x2 > _0x53fee4.rect[0x1].x && (Math.min(_0x125709.rect[0x3].y, _0x53fee4.rect[0x2].y) - Math.max(_0x125709.rect[0x0].y, _0x53fee4.rect[0x1].y)) / _0x53fee4.height > 0.5);
            if (!_0x344a2d.length) {
              break;
            }
            _0x344a2d.sort((_0x301a7c, _0x6e7376) => _0x301a7c.rect[0x0].x - _0x6e7376.rect[0x0].x);
            let _0x238244 = _0x344a2d.pop();
            let _0x4cde4d = _0x270139.indexOf(_0x238244);
            if (_0x4cde4d >= 0x0) {
              _0x270139.splice(_0x4cde4d, 0x1);
            }
            _0x30dcf6.unshift(_0x238244);
            _0x125709 = _0x238244;
          }
          for (_0x125709 = _0x30dcf6[_0x30dcf6.length - 0x1]; _0x270139.length;) {
            let _0x325749 = _0x270139.filter(_0xc35a71 => _0xc35a71.rect[0x0].x + Math.max(_0xc35a71.height, _0x125709.height) / 0x2 > _0x125709.rect[0x1].x && (Math.min(_0xc35a71.rect[0x3].y, _0x125709.rect[0x2].y) - Math.max(_0xc35a71.rect[0x0].y, _0x125709.rect[0x1].y)) / _0x125709.height > 0.5);
            if (!_0x325749.length) {
              break;
            }
            _0x325749.sort((_0x4d6d5b, _0x5109dc) => _0x4d6d5b.rect[0x0].x - _0x5109dc.rect[0x0].x);
            let _0x21f3ff = _0x325749.shift();
            let _0xe8ef04 = _0x270139.indexOf(_0x21f3ff);
            if (_0xe8ef04 >= 0x0) {
              _0x270139.splice(_0xe8ef04, 0x1);
            }
            _0x30dcf6.push(_0x21f3ff);
            _0x125709 = _0x21f3ff;
          }
          _0x21fa64.push(_0x30dcf6);
        }
        return _0x21fa64;
      }
      function _0x413248(_0x398532) {
        let _0x9896b7 = 0x0;
        for (let _0x4a1ab8 = 0x0; _0x4a1ab8 < _0x398532.length; _0x4a1ab8++) {
          _0x9896b7 += _0x398532.charCodeAt(_0x4a1ab8);
        }
        return _0x9896b7;
      }
      function _0x262ff3(_0xc68443, _0x45ec44 = 0x0) {
        let _0x57ca9b = _0xc68443 / 0x16d / 0x5265c00;
        let _0x266222 = (_0xc68443 -= 0x16d * ~~_0x57ca9b * 0x5265c00) / 0x5265c00;
        let _0x3eca82 = (_0xc68443 -= 0x5265c00 * ~~_0x266222) / 0x36ee80;
        let _0xc539d3 = (_0xc68443 -= 0x36ee80 * ~~_0x3eca82) / 0xea60;
        let _0x2ee07b = (_0xc68443 -= 0xea60 * ~~_0xc539d3) / 0x3e8;
        let _0x262701 = [];
        if (_0x57ca9b >= 0x1) {
          _0x262701.push((_0x262701.length ? +_0x57ca9b.toFixed(_0x45ec44) : Math.floor(_0x57ca9b)) + '年');
        }
        if (_0x266222 >= 0x1) {
          _0x262701.push((_0x262701.length ? +_0x266222.toFixed(_0x45ec44) : Math.floor(_0x266222)) + '天');
        }
        if (_0x3eca82 >= 0x1) {
          _0x262701.push((_0x262701.length ? +_0x3eca82.toFixed(_0x45ec44) : Math.floor(_0x3eca82)) + '小时');
        }
        if (_0xc539d3 >= 0x1) {
          _0x262701.push((_0x262701.length ? +_0xc539d3.toFixed(_0x45ec44) : Math.floor(_0xc539d3)) + '分');
        }
        if (_0x2ee07b >= 0x1) {
          _0x262701.push((_0x262701.length ? +_0x2ee07b.toFixed(_0x45ec44) : Math.floor(_0x2ee07b)) + '秒');
        }
        return _0x262701.slice(0x0, 0x2).join('');
      }
      function _0x3e4708(_0x3d2434) {
        if ("string" == typeof _0x3d2434) {
          try {
            return JSON.parse(_0x3d2434);
          } catch (_0x2bb132) {}
        }
        return _0x3d2434;
      }
      function _0x14e965(_0xba996a, _0xd450b1 = [{
        'c': "\"",
        't': "\\"
      }]) {
        if (!(_0xba996a = _0xba996a.trimStart())) {
          return {
            'value': '',
            'next': ''
          };
        }
        let _0x15b77e = _0xba996a[0x0];
        for (let _0x52b1ba of _0xd450b1) if (_0x52b1ba.c == _0x15b77e) {
          _0xba996a = _0xba996a.slice(0x1);
          const _0x1b4508 = new RegExp('(' + _0x52b1ba.t.replace(/\\/, "\\\\") + '*)' + _0x15b77e, 'g');
          let _0x38b188 = _0xba996a;
          let _0x5e2453 = '';
          for (;;) {
            let _0x237356 = _0x1b4508.exec(_0x38b188);
            if (!_0x237356) {
              break;
            }
            if (!(0x1 & _0x237356[0x1].length)) {
              _0x38b188 = _0xba996a.slice(0x0, _0x237356.index).replaceAll(_0x52b1ba.t + _0x52b1ba.c, _0x52b1ba.c);
              _0x5e2453 = _0xba996a.slice(_0x237356.index + 0x1);
              break;
            }
          }
          return {
            'value': _0x38b188,
            'next': _0x5e2453
          };
        }
        let _0x54f1b9 = /\s/.exec(_0xba996a);
        return _0x54f1b9 ? {
          'value': _0xba996a.slice(0x0, _0x54f1b9.index),
          'next': _0xba996a.slice(_0x54f1b9.index)
        } : {
          'value': _0xba996a,
          'next': ''
        };
      }
      function _0x514634(_0x238576) {
        return _0x238576.replace(/^(\w)|[_-](\w)/g, (_0x264dc4, _0x455703, _0x2b80d8) => (_0x455703 || _0x2b80d8).toUpperCase());
      }
      function _0x2e7e4d(_0x41c0d4) {
        return _0x41c0d4.replace(/[_-](\w)/g, (_0x3265eb, _0x3e3817) => _0x3e3817.toUpperCase());
      }
      function _0x14a238(_0x469555, _0x17770d) {
        return null == _0x469555 ? _0x17770d : _0x469555 + '';
      }
      function _0x3e955f(_0x4a5357) {
        let _0x2a4086 = _0x4a5357.trim();
        let _0x57fcba = {};
        for (; _0x2a4086;) {
          let _0x51e17b = /^([^\s=]+)=/.exec(_0x2a4086);
          if (!_0x51e17b) {
            break;
          }
          let [_0x50e1c7, _0x540ce2] = _0x51e17b;
          _0x2a4086 = _0x2a4086.slice(_0x50e1c7.length);
          let _0x1331f9 = " " == _0x2a4086[0x0] ? {
            'value': '',
            'next': _0x2a4086
          } : _0x14e965(_0x2a4086);
          _0x57fcba[_0x540ce2] = _0x1331f9.value;
          _0x2a4086 = _0x1331f9.next.trim();
        }
        if (_0x2a4086) {
          _0x57fcba.keyword = _0x2a4086.trim();
        }
        return _0x57fcba;
      }
      function _0x203129(_0xba35a3) {
        if (!_0xba35a3) {
          return '';
        }
        let _0x1b5430 = '';
        let {
          keyword: _0x1291c4,
          ..._0x8bb010
        } = _0xba35a3;
        for (let _0x30cdd5 in _0x8bb010) {
          let _0x57d1d8 = _0xba35a3[_0x30cdd5];
          if (_0x57d1d8.indexOf(" ") >= 0x0) {
            _0x57d1d8 = JSON.stringify(_0x57d1d8);
          }
          _0x1b5430 += _0x30cdd5 + '=' + _0x57d1d8 + " ";
        }
        if (_0x1291c4) {
          _0x1b5430 += _0x1291c4;
        }
        return _0x1b5430.trim();
      }
      function _0x3a630d(_0xba99eb) {
        if ('number' == typeof _0xba99eb) {
          return _0xba99eb;
        }
        let [_0x21f253, _0x154b12, _0xbd8a7b] = _0xba99eb.split('.');
        return 0xf4240 * parseInt(_0x21f253) + 0x3e8 * parseInt(_0x154b12) + parseInt(_0xbd8a7b);
      }
      function _0x3f60b8(_0x68cece) {
        return Math.floor(_0x68cece / 0xf4240) + '.' + Math.floor(_0x68cece % 0xf4240 / 0x3e8) + '.' + _0x68cece % 0x3e8;
      }
      function _0x2e35f6(_0x403fa0, _0x533339) {
        var _0x178ffc = Object.keys(_0x403fa0).sort();
        var _0x58523d = '';
        for (let _0x330a9b of _0x178ffc) {
          if ("sign" == _0x330a9b) {
            continue;
          }
          let _0x1f2574 = _0x403fa0[_0x330a9b];
          if (null != _0x1f2574 && "function" != typeof _0x1f2574) {
            _0x1f2574 = "object" == typeof _0x1f2574 ? JSON.stringify(_0x1f2574) : _0x1f2574 + '';
            if (_0x1f2574) {
              _0x58523d += _0x330a9b + '=' + _0x1f2574 + '&';
            }
          }
        }
        return _0x58523d + "key=" + _0x533339;
      }
      function _0x2e4bba(_0x1da9cb) {
        return Array.from(new Set(_0x1da9cb));
      }
      function _0x2cd459(_0x14555c) {
        return /^(#[\da-fA-F]{3,8}|rgba?\(\d+(\s*,\s*[\d\.]+){2,3}\))$/.test(_0x14555c);
      }
      function _0x40ae6d(_0x34a1cf, _0x5975f0) {
        let _0x15be90 = null == _0x5975f0 ? _0x9f975d => _0x9f975d : "string" == typeof _0x5975f0 ? _0x203f49 => _0x203f49[_0x5975f0] : _0x5975f0;
        let _0x518863 = 0x0;
        for (let _0xfbcc01 of _0x34a1cf) _0x518863 += _0x15be90(_0xfbcc01);
        let _0x51afb2 = Math.random() * _0x518863;
        for (let _0x432c34 of _0x34a1cf) {
          let _0xac3e9d = _0x15be90(_0x432c34);
          if (_0x51afb2 < _0xac3e9d) {
            return _0x432c34;
          }
          _0x51afb2 -= _0xac3e9d;
        }
        return _0x34a1cf[_0x34a1cf.length - 0x1];
      }
      function _0x56d7e4(_0x292e56) {
        let _0x9f892c = /^(\w+:)\/\/([^@]+@)?([^:/]+|\[[0-9a-fA-F:]+\])(:\d+)?(\/[^?#]*)?(\?[^#]*)?(#.*)?$/.exec(_0x292e56.trim());
        if (!_0x9f892c) {
          return null;
        }
        let [_0x2a5712, _0x579eec, _0x1e7715, _0x19b544, _0xf69d38, _0x55d731, _0x51d4b3, _0x1c2f83] = _0x9f892c;
        if (_0x19b544.startsWith('[') && _0x19b544.endsWith(']')) {
          _0x19b544 = _0x19b544.slice(0x1, -0x1);
        }
        if (_0x1e7715) {
          _0x1e7715 = _0x1e7715.slice(0x0, -0x1);
        }
        if (_0xf69d38) {
          _0xf69d38 = _0xf69d38.slice(0x1);
        }
        if (!_0x51d4b3) {
          _0x51d4b3 = '';
        }
        if (!_0x1c2f83) {
          _0x1c2f83 = '';
        }
        if (!_0x55d731) {
          _0x55d731 = '';
        }
        return {
          'href': _0x2a5712,
          'protocol': _0x579eec,
          'password': _0x1e7715,
          'host': _0x19b544,
          'port': _0xf69d38,
          'path': _0x55d731,
          'search': _0x51d4b3,
          'hash': _0x1c2f83
        };
      }
      function _0x194d40(_0x51dcf6) {
        let _0x2d5b05 = [];
        if (!_0x51dcf6) {
          return _0x2d5b05;
        }
        for (let _0x5a1978 of _0x51dcf6.split(/[^\d~-]+/)) {
          let _0x283e75 = /(\d+)(?:[~|-](\d+))?/.exec(_0x5a1978);
          if (!_0x283e75) {
            continue;
          }
          let [, _0x152cae, _0xc3a09c] = _0x283e75;
          _0x152cae = parseInt(_0x152cae);
          _0xc3a09c = _0xc3a09c ? parseInt(_0xc3a09c) : _0x152cae;
          for (let _0x3edc14 = _0x152cae; _0x3edc14 <= _0xc3a09c; _0x3edc14++) {
            _0x2d5b05.push(_0x3edc14);
          }
        }
        return _0x2d5b05;
      }
      class _0x3a7acd {
        constructor(_0x278887) {
          this.cache = new Map();
          this.set = new Set();
          this.fn = _0x278887;
          this.waitRun = _0x5cbaf8();
        }
        ['run'](_0x1b48ce) {
          return this.cache.has(_0x1b48ce) ? Promise.resolve(this.cache.get(_0x1b48ce)) : this.running?.["has"](_0x1b48ce) ? this.pms.then(() => this.cache.get(_0x1b48ce)) : (this.set.add(_0x1b48ce), clearTimeout(this.timer), this.timer = setTimeout(() => {
            let _0x2fe81f = this.set;
            this.running = _0x2fe81f;
            this.set = new Set();
            this.pms = this.fn(Array.from(_0x2fe81f), this.cache)["finally"](() => {
              _0x2fe81f.clear();
            });
            this.waitRun.resolve();
            this.waitRun = _0x5cbaf8();
          }, 0x1f4), this.waitRun.then(() => this.pms).then(() => this.cache.get(_0x1b48ce)));
        }
      }
      function _0x53ae5d() {
        const _0x38acef = ['b', 'p', 'm', 'f', 'd', 't', 'n', 'l', 'g', 'k', 'h', 'j', 'q', 'x', 'zh', 'ch', 'sh', 'r', 'z', 'c', 's'];
        const _0x1bff57 = ['a', 'o', 'e', 'i', 'u', 'ai', 'ei', 'ui', 'ao', 'ou', 'iu', 'ie', 'an', 'en', 'in', 'un', "ang", "eng", "ing", "ong"];
        return _0x38acef[Math.floor(Math.random() * _0x38acef.length)] + _0x1bff57[Math.floor(Math.random() * _0x1bff57.length)];
      }
      function _0x36ee65(_0x3b48d2, _0x19fcd3) {
        const _0x1ab71b = _0x3b48d2.length;
        const _0x437764 = _0x19fcd3.length;
        const _0x5e91e3 = Array.from(Array(_0x1ab71b + 0x1), () => Array(_0x437764 + 0x1).fill(0x0));
        for (let _0x1fba0f = 0x0; _0x1fba0f <= _0x1ab71b; _0x1fba0f++) {
          _0x5e91e3[_0x1fba0f][0x0] = _0x1fba0f;
        }
        for (let _0x38fdce = 0x0; _0x38fdce <= _0x437764; _0x38fdce++) {
          _0x5e91e3[0x0][_0x38fdce] = _0x38fdce;
        }
        for (let _0x3f6807 = 0x1; _0x3f6807 <= _0x1ab71b; _0x3f6807++) {
          for (let _0x10f97c = 0x1; _0x10f97c <= _0x437764; _0x10f97c++) {
            if (_0x3b48d2[_0x3f6807 - 0x1] === _0x19fcd3[_0x10f97c - 0x1]) {
              _0x5e91e3[_0x3f6807][_0x10f97c] = _0x5e91e3[_0x3f6807 - 0x1][_0x10f97c - 0x1];
            } else {
              _0x5e91e3[_0x3f6807][_0x10f97c] = Math.min(_0x5e91e3[_0x3f6807 - 0x1][_0x10f97c - 0x1], _0x5e91e3[_0x3f6807 - 0x1][_0x10f97c], _0x5e91e3[_0x3f6807][_0x10f97c - 0x1]) + 0x1;
            }
          }
        }
        return _0x5e91e3[_0x1ab71b][_0x437764];
      }
      function _0x11798a(_0x58e148, _0x13ee86) {
        const _0xa76b46 = _0x36ee65(_0x58e148 = _0x58e148.trim().replace(/\d+/g, '#'), _0x13ee86 = _0x13ee86.trim().replace(/\d+/g, '#'));
        const _0xb8eb70 = Math.max(_0x58e148.length, _0x13ee86.length);
        return 0x0 === _0xb8eb70 ? 0x1 : 0x1 - _0xa76b46 / _0xb8eb70;
      }
      function _0x3d57fb(_0xc1c8bd) {
        return _0xc1c8bd.replace(/\\/g, "\\\\").replace(/(?<!\\)\./g, "\\.").replace(/(?<!\\)\*/g, '.*').replace(/(?<!\\)\?/g, '.').replace(/(?<!\\)\(/g, "\\(").replace(/(?<!\\)\)/g, "\\)").replace(/(?<!\\)\[/g, "\\[").replace(/(?<!\\)\]/g, "\\]").replace(/(?<!\\)\{/g, "\\{").replace(/(?<!\\)\}/g, "\\}").replace(/(?<!\\)\|/g, "\\|").replace(/(?<!\\)\^/g, "\\^").replace(/(?<!\\)\$/g, "\\$");
      }
      function _0x506d99(_0x3f6ab6) {
        let _0x1977ba;
        return new Proxy(function () {}, {
          'get': (_0x137553, _0x4acf03) => (_0x1977ba || (_0x1977ba = _0x3f6ab6()), _0x1977ba[_0x4acf03]),
          'set': (_0x4adf12, _0x227878, _0x4c9068, _0x76e8f) => (_0x1977ba || (_0x1977ba = _0x3f6ab6()), _0x1977ba[_0x227878] = _0x4c9068, true),
          'apply': (_0x3708d7, _0x395e16, _0x31c120) => (_0x1977ba || (_0x1977ba = _0x3f6ab6()), _0x1977ba.apply(_0x395e16, _0x31c120)),
          'deleteProperty': (_0x5f365b, _0x22c180) => (_0x1977ba || (_0x1977ba = _0x3f6ab6()), delete _0x1977ba[_0x22c180], true)
        });
      }
      const _0x3ab087 = new WeakMap();
      function _0xf3bb30(_0x31ea3d, _0x308c1d) {
        let _0x92fea5 = _0x3ab087.get(_0x31ea3d);
        if (_0x92fea5) {
          _0x92fea5["delete"](_0x308c1d);
          if (!_0x92fea5.size) {
            _0x3ab087['delete'](_0x31ea3d);
          }
        }
      }
      function _0x59b6ea(_0x708366) {
        let _0x32ec71 = _0x3ab087.get(_0x708366);
        return Promise.resolve(_0x32ec71 && _0x32ec71.pms);
      }
      function _0x2e7d0f(_0x425254, _0x5f3dd4, _0x405ddd = 0x0) {
        if (null == _0x425254 || "object" != typeof _0x425254) {
          return _0x425254;
        }
        let _0x247b54 = _0x3ab087.get(_0x425254);
        if (_0x247b54) {
          _0x247b54.add(_0x5f3dd4);
          return _0x425254;
        }
        function _0x4c6c21(_0x1fb9ed, _0x2b53c7) {
          return Promise.all(Array.from(_0x247b54).map(_0x1b3e1c => _0x1b3e1c(_0x1fb9ed, _0x2b53c7)));
        }
        function _0x531a6d(_0x272ff3) {
          _0x272ff3 = _0x405ddd < 0x0 ? _0x272ff3 : _0x1fcf34(_0x272ff3, _0x405ddd);
          return function () {
            let _0x2bb3e8 = _0x272ff3();
            if (_0x247b54.pms) {
              _0x247b54.pms = _0x247b54.pms.then(() => _0x2bb3e8);
            } else {
              _0x247b54.pms = _0x2bb3e8;
            }
            return _0x2bb3e8;
          };
        }
        _0x247b54 = new Set();
        _0x247b54.add(_0x5f3dd4);
        _0x3ab087.set(_0x425254, _0x247b54);
        if (Array.isArray(_0x425254)) {
          let _0x16bf17 = [];
          const _0x6b9e07 = _0x531a6d(() => {
            let _0x1e9268 = _0x4c6c21(_0x16bf17);
            _0x16bf17 = [];
            return _0x1e9268;
          });
          function _0x6e1da0(_0x1201bc) {
            _0x16bf17.push({
              'changed': _0x1201bc
            });
            _0x6b9e07();
          }
          function _0x234518(_0x57a66b) {
            _0xf3bb30(_0x57a66b, _0x5f3dd4);
            let _0x3ebce5 = _0x16bf17.findIndex(_0x408631 => _0x408631.changed == _0x57a66b);
            if (_0x3ebce5 >= 0x0) {
              _0x16bf17.splice(_0x3ebce5, 0x1);
            } else {
              _0x16bf17.push({
                'data': _0x57a66b
              });
            }
            _0x6b9e07();
          }
          _0x425254.forEach((_0x465b27, _0x1c2328) => {
            _0x465b27 = _0x2e7d0f(_0x465b27, _0x5f3dd4, _0x405ddd);
            _0x425254[_0x1c2328] = _0x465b27;
          });
          _0x425254.splice = function (_0x2b6ecb, _0x4e98f3, ..._0x16d5d1) {
            _0x16d5d1.forEach((_0x2f9e5c, _0x4da71f) => {
              _0x2f9e5c = _0x2e7d0f(_0x2f9e5c, _0x5f3dd4, _0x405ddd);
              _0x16d5d1[_0x4da71f] = _0x2f9e5c;
              _0x6e1da0(_0x2f9e5c);
            });
            let _0xc3fce2 = Array.prototype.splice.call(_0x425254, _0x2b6ecb, _0x4e98f3, ..._0x16d5d1);
            _0xc3fce2.forEach((_0x576111, _0x3d2b29) => {
              _0x234518(_0x576111);
            });
            return _0xc3fce2;
          };
          _0x425254.push = function (..._0x595ec4) {
            _0x595ec4.forEach((_0x5a5e3d, _0xeac975) => {
              _0x5a5e3d = _0x2e7d0f(_0x5a5e3d, _0x5f3dd4, _0x405ddd);
              _0x595ec4[_0xeac975] = _0x5a5e3d;
              _0x6e1da0(_0x5a5e3d);
            });
            return Array.prototype.push.apply(_0x425254, _0x595ec4);
          };
          _0x425254.pop = function () {
            let _0x1680b6 = Array.prototype.pop.call(_0x425254);
            if (null != _0x1680b6) {
              _0x234518(_0x1680b6);
            }
            return _0x1680b6;
          };
          _0x425254.shift = function () {
            let _0x505e4d = Array.prototype.shift.call(_0x425254);
            if (null != _0x505e4d) {
              _0x234518(_0x505e4d);
            }
            return _0x505e4d;
          };
          _0x425254.unshift = function (..._0x3077b5) {
            _0x3077b5.forEach((_0x29e37c, _0xecfebc) => {
              _0x29e37c = _0x2e7d0f(_0x29e37c, _0x5f3dd4, _0x405ddd);
              _0x3077b5[_0xecfebc] = _0x29e37c;
              _0x6e1da0(_0x29e37c);
            });
            return Array.prototype.unshift.apply(_0x425254, _0x3077b5);
          };
        } else {
          let _0x34b533 = {};
          const _0x4f2e47 = _0x531a6d(() => {
            let _0x494fa6 = _0x4c6c21([{
              'data': _0x425254,
              'changed': _0x34b533
            }]);
            _0x34b533 = {};
            return _0x494fa6;
          });
          for (let _0x4cbd86 in _0x425254) {
            if ('_' === _0x4cbd86[0x0]) {
              continue;
            }
            let _0x629293 = _0x425254[_0x4cbd86];
            function _0x4755ce() {
              _0x34b533[_0x4cbd86] = _0x629293;
              _0x4f2e47();
            }
            if (undefined !== _0x629293 && "function" != typeof _0x629293) {
              _0x629293 = _0x2e7d0f(_0x629293, _0x4755ce, -0x1);
              Object.defineProperty(_0x425254, _0x4cbd86, {
                'enumerable': true,
                'get': () => _0x629293,
                'set'(_0xc44fff) {
                  if (_0xc44fff !== _0x629293) {
                    _0x629293 = _0x2e7d0f(_0xc44fff, _0x4755ce, -0x1);
                    _0x4755ce();
                  }
                }
              });
            }
          }
        }
        return _0x425254;
      }
      function _0x38d351() {
        let _0x151326;
        do {
          _0x151326 = [Math.floor(Math.random() * 0x100), Math.floor(Math.random() * 0x100), 0x0, 0x0];
        } while (0x0 === _0x151326[0x0] || 0x7f === _0x151326[0x0] || _0x151326[0x0] >= 0xa && _0x151326[0x0] <= 0xa || _0x151326[0x0] >= 0xac && _0x151326[0x0] <= 0x1f || 0xc0 === _0x151326[0x0] && 0xa8 === _0x151326[0x1] || _0x151326[0x0] >= 0xe0 && _0x151326[0x0] <= 0xef || 0xff === _0x151326[0x0]);
        for (_0x151326[0x2] = Math.floor(Math.random() * 0x100); 0x0 === _0x151326[0x3];) {
          _0x151326[0x3] = Math.floor(Math.random() * 0x100);
        }
        return _0x151326.join('.');
      }
      function _0x46e330(_0x1734ca, _0x2985ab) {
        if ("string" == typeof _0x1734ca) {
          return _0x1734ca;
        }
        let _0x523754 = _0x1734ca.toString();
        if (/async\s+/.test(_0x523754)) {
          if (!(/async\s+\(/.test(_0x523754) || /async\s+function\s*\(/.test(_0x523754))) {
            _0x523754 = "async function " + _0x523754.slice(0x6);
          }
        } else if (!(/^\s*\(/.test(_0x523754) || /^\s*function\s*\(/.test(_0x523754))) {
          _0x523754 = "function " + _0x523754;
        }
        return _0x2985ab ? '(' + _0x523754 + ')(' + _0x2985ab.map(_0xbb149b => JSON.stringify(_0xbb149b)).join(',') + ')' : '(' + _0x523754 + ")()";
      }
    },
    0x1c2b: (_0x521eed, _0x38e262, _0x46ed1c) => {
      'use strict';

      const _0x515f09 = _0x46ed1c(0xe97);
      const _0x1bb29e = _0x46ed1c(0x359);
      const _0x39a20e = _0x46ed1c(0x1b10);
      _0x521eed.exports = {
        'hasMillisRes': function (_0xd5752c) {
          let _0x5df710 = _0x39a20e.join("millis-test" + Date.now().toString() + Math.random().toString().slice(0x2));
          _0x5df710 = _0x39a20e.join(_0x1bb29e.tmpdir(), _0x5df710);
          const _0xcb76a6 = new Date(0x14e351e2116);
          _0x515f09.writeFile(_0x5df710, "https://github.com/jprichardson/node-fs-extra/pull/141", _0x314ed7 => {
            if (_0x314ed7) {
              return _0xd5752c(_0x314ed7);
            }
            _0x515f09.open(_0x5df710, 'r+', (_0x4e82f6, _0x32d877) => {
              if (_0x4e82f6) {
                return _0xd5752c(_0x4e82f6);
              }
              _0x515f09.futimes(_0x32d877, _0xcb76a6, _0xcb76a6, _0x528c17 => {
                if (_0x528c17) {
                  return _0xd5752c(_0x528c17);
                }
                _0x515f09.close(_0x32d877, _0x44fc95 => {
                  if (_0x44fc95) {
                    return _0xd5752c(_0x44fc95);
                  }
                  _0x515f09.stat(_0x5df710, (_0x2c4c7f, _0x4ab7dc) => {
                    if (_0x2c4c7f) {
                      return _0xd5752c(_0x2c4c7f);
                    }
                    _0xd5752c(null, _0x4ab7dc.mtime > 0x14e351e1db8);
                  });
                });
              });
            });
          });
        },
        'hasMillisResSync': function () {
          let _0x19ef44 = _0x39a20e.join("millis-test-sync" + Date.now().toString() + Math.random().toString().slice(0x2));
          _0x19ef44 = _0x39a20e.join(_0x1bb29e.tmpdir(), _0x19ef44);
          const _0x5ab148 = new Date(0x14e351e2116);
          _0x515f09.writeFileSync(_0x19ef44, "https://github.com/jprichardson/node-fs-extra/pull/141");
          const _0x5aa1d6 = _0x515f09.openSync(_0x19ef44, 'r+');
          _0x515f09.futimesSync(_0x5aa1d6, _0x5ab148, _0x5ab148);
          _0x515f09.closeSync(_0x5aa1d6);
          return _0x515f09.statSync(_0x19ef44).mtime > 0x14e351e1db8;
        },
        'timeRemoveMillis': function (_0x207761) {
          if ("number" == typeof _0x207761) {
            return 0x3e8 * Math.floor(_0x207761 / 0x3e8);
          }
          if (_0x207761 instanceof Date) {
            return new Date(0x3e8 * Math.floor(_0x207761.getTime() / 0x3e8));
          }
          throw new Error("fs-extra: timeRemoveMillis() unknown parameter type");
        },
        'utimesMillis': function (_0x2a80b3, _0x19b527, _0x34b77a, _0x2d559d) {
          _0x515f09.open(_0x2a80b3, 'r+', (_0x3bed5e, _0x3e9f6b) => {
            if (_0x3bed5e) {
              return _0x2d559d(_0x3bed5e);
            }
            _0x515f09.futimes(_0x3e9f6b, _0x19b527, _0x34b77a, _0x3eae74 => {
              _0x515f09.close(_0x3e9f6b, _0x11b12c => {
                if (_0x2d559d) {
                  _0x2d559d(_0x3eae74 || _0x11b12c);
                }
              });
            });
          });
        },
        'utimesMillisSync': function (_0x204b86, _0x3da2db, _0x4a3a7a) {
          const _0x1d5771 = _0x515f09.openSync(_0x204b86, 'r+');
          _0x515f09.futimesSync(_0x1d5771, _0x3da2db, _0x4a3a7a);
          return _0x515f09.closeSync(_0x1d5771);
        }
      };
    },
    0x1c2e: _0x285da4 => {
      function _0x4b99c4() {
        if (!(this instanceof _0x4b99c4)) {
          return new _0x4b99c4();
        }
        this._bsontype = "MinKey";
      }
      _0x285da4.exports = _0x4b99c4;
      _0x285da4.exports.MinKey = _0x4b99c4;
    },
    0x1d53: (_0x267907, _0x2c7ed3, _0x1d211d) => {
      var _0x343f57;
      _0x267907.exports = function () {
        if (!_0x343f57) {
          try {
            _0x343f57 = _0x1d211d(0x1679)("follow-redirects");
          } catch (_0x46e5ac) {}
          if ("function" != typeof _0x343f57) {
            _0x343f57 = function () {};
          }
        }
        _0x343f57.apply(null, arguments);
      };
    },
    0x1d62: (_0x2b21b0, _0x762e25, _0x409ac1) => {
      'use strict';

      var _0x267eac = _0x409ac1(0x1e53);
      _0x2b21b0.exports = function (_0x14ed3e, _0x283b61, _0x497b41) {
        var _0x2d257d = _0x497b41.config.validateStatus;
        if (_0x497b41.status && _0x2d257d && !_0x2d257d(_0x497b41.status)) {
          _0x283b61(_0x267eac("Request failed with status code " + _0x497b41.status, _0x497b41.config, null, _0x497b41.request, _0x497b41));
        } else {
          _0x14ed3e(_0x497b41);
        }
      };
    },
    0x1e07: (_0xf061d1, _0x1c4ee2, _0x14e2cf) => {
      'use strict';

      const _0x2e4bf5 = _0x14e2cf(0x359);
      const _0x3a8599 = _0x14e2cf(0x7e2);
      const _0x3df221 = _0x14e2cf(0x16fc);
      const {
        env: _0x21a650
      } = process;
      let _0x1c3ac1;
      function _0x49449e(_0x3e0bde) {
        return 0x0 !== _0x3e0bde && {
          'level': _0x3e0bde,
          'hasBasic': true,
          'has256': _0x3e0bde >= 0x2,
          'has16m': _0x3e0bde >= 0x3
        };
      }
      function _0x4f4f12(_0x44dc86, _0x565e25) {
        if (0x0 === _0x1c3ac1) {
          return 0x0;
        }
        if (_0x3df221("color=16m") || _0x3df221("color=full") || _0x3df221("color=truecolor")) {
          return 0x3;
        }
        if (_0x3df221("color=256")) {
          return 0x2;
        }
        if (_0x44dc86 && !_0x565e25 && undefined === _0x1c3ac1) {
          return 0x0;
        }
        const _0xb311b2 = _0x1c3ac1 || 0x0;
        if ("dumb" === _0x21a650.TERM) {
          return _0xb311b2;
        }
        if ("win32" === process.platform) {
          const _0x4d603e = _0x2e4bf5.release().split('.');
          return Number(_0x4d603e[0x0]) >= 0xa && Number(_0x4d603e[0x2]) >= 0x295a ? Number(_0x4d603e[0x2]) >= 0x3a53 ? 0x3 : 0x2 : 0x1;
        }
        if ('CI' in _0x21a650) {
          return ["TRAVIS", "CIRCLECI", "APPVEYOR", "GITLAB_CI", "GITHUB_ACTIONS", "BUILDKITE"].some(_0x23b861 => _0x23b861 in _0x21a650) || "codeship" === _0x21a650.CI_NAME ? 0x1 : _0xb311b2;
        }
        if ("TEAMCITY_VERSION" in _0x21a650) {
          return /^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(_0x21a650.TEAMCITY_VERSION) ? 0x1 : 0x0;
        }
        if ("truecolor" === _0x21a650.COLORTERM) {
          return 0x3;
        }
        if ("TERM_PROGRAM" in _0x21a650) {
          const _0x4feeca = parseInt((_0x21a650.TERM_PROGRAM_VERSION || '').split('.')[0x0], 0xa);
          switch (_0x21a650.TERM_PROGRAM) {
            case "iTerm.app":
              return _0x4feeca >= 0x3 ? 0x3 : 0x2;
            case "Apple_Terminal":
              return 0x2;
          }
        }
        return /-256(color)?$/i.test(_0x21a650.TERM) ? 0x2 : /^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(_0x21a650.TERM) || 'COLORTERM' in _0x21a650 ? 0x1 : _0xb311b2;
      }
      if (_0x3df221("no-color") || _0x3df221("no-colors") || _0x3df221('color=false') || _0x3df221("color=never")) {
        _0x1c3ac1 = 0x0;
      } else if (_0x3df221('color') || _0x3df221("colors") || _0x3df221("color=true") || _0x3df221('color=always')) {
        _0x1c3ac1 = 0x1;
      }
      if ("FORCE_COLOR" in _0x21a650) {
        _0x1c3ac1 = "true" === _0x21a650.FORCE_COLOR ? 0x1 : "false" === _0x21a650.FORCE_COLOR ? 0x0 : 0x0 === _0x21a650.FORCE_COLOR.length ? 0x1 : Math.min(parseInt(_0x21a650.FORCE_COLOR, 0xa), 0x3);
      }
      _0xf061d1.exports = {
        'supportsColor': function (_0x1175a2) {
          return 0x0 !== _0x4f4f12(_0x1175a2, _0x1175a2 && _0x1175a2.isTTY) && {
            'level': _0x4f4f12(_0x1175a2, _0x1175a2 && _0x1175a2.isTTY),
            'hasBasic': true,
            'has256': _0x4f4f12(_0x1175a2, _0x1175a2 && _0x1175a2.isTTY) >= 0x2,
            'has16m': _0x4f4f12(_0x1175a2, _0x1175a2 && _0x1175a2.isTTY) >= 0x3
          };
        },
        'stdout': 0x0 !== _0x4f4f12(true, _0x3a8599.isatty(0x1)) && {
          'level': _0x4f4f12(true, _0x3a8599.isatty(0x1)),
          'hasBasic': true,
          'has256': _0x4f4f12(true, _0x3a8599.isatty(0x1)) >= 0x2,
          'has16m': _0x4f4f12(true, _0x3a8599.isatty(0x1)) >= 0x3
        },
        'stderr': 0x0 !== _0x4f4f12(true, _0x3a8599.isatty(0x2)) && {
          'level': _0x4f4f12(true, _0x3a8599.isatty(0x2)),
          'hasBasic': true,
          'has256': _0x4f4f12(true, _0x3a8599.isatty(0x2)) >= 0x2,
          'has16m': _0x4f4f12(true, _0x3a8599.isatty(0x2)) >= 0x3
        }
      };
    },
    0x1e35: (_0x344a5c, _0x545f54, _0x5f49a4) => {
      'use strict';

      var _0x13896a = _0x5f49a4(0xdde).Long;
      var _0x4b5f0e = _0x5f49a4(0xb01).Double;
      var _0x4d7c52 = _0x5f49a4(0x2ca).Timestamp;
      var _0x2dc581 = _0x5f49a4(0x1526).ObjectID;
      var _0x5a6319 = _0x5f49a4(0x1ff0).Symbol;
      var _0x45ff06 = _0x5f49a4(0xc55).Code;
      var _0x346a78 = _0x5f49a4(0x1c2e).MinKey;
      var _0xc9d918 = _0x5f49a4(0x1800).MaxKey;
      var _0x2a0fd1 = _0x5f49a4(0xa88);
      var _0x3dd563 = _0x5f49a4(0x1163);
      var _0x12dd1c = _0x5f49a4(0x17bc).DBRef;
      var _0xa0879f = _0x5f49a4(0x20f9).BSONRegExp;
      var _0xeac924 = _0x5f49a4(0xa61).Binary;
      var _0xd820e0 = _0x5f49a4(0x1a9d);
      var _0xde5b22 = function (_0xd9e3ff, _0xace6a7, _0x5eb80d) {
        var _0x22a4c8 = (_0xace6a7 = null == _0xace6a7 ? {} : _0xace6a7) && _0xace6a7.index ? _0xace6a7.index : 0x0;
        var _0x394cbf = _0xd9e3ff[_0x22a4c8] | _0xd9e3ff[_0x22a4c8 + 0x1] << 0x8 | _0xd9e3ff[_0x22a4c8 + 0x2] << 0x10 | _0xd9e3ff[_0x22a4c8 + 0x3] << 0x18;
        if (_0x394cbf < 0x5 || _0xd9e3ff.length < _0x394cbf || _0x394cbf + _0x22a4c8 > _0xd9e3ff.length) {
          throw new Error("corrupt bson message");
        }
        if (0x0 !== _0xd9e3ff[_0x22a4c8 + _0x394cbf - 0x1]) {
          throw new Error("One object, sized correctly, with a spot for an EOO, but the EOO isn't 0x00");
        }
        return _0xe84204(_0xd9e3ff, _0x22a4c8, _0xace6a7, _0x5eb80d);
      };
      var _0xe84204 = function (_0xff4475, _0x24da2b, _0x2b5263, _0x518dbd) {
        var _0xa8965a = null != _0x2b5263.evalFunctions && _0x2b5263.evalFunctions;
        var _0x49b377 = null != _0x2b5263.cacheFunctions && _0x2b5263.cacheFunctions;
        var _0x2c1962 = null != _0x2b5263.cacheFunctionsCrc32 && _0x2b5263.cacheFunctionsCrc32;
        if (!_0x2c1962) {}
        var _0x4ca2ab = null == _0x2b5263.fieldsAsRaw ? null : _0x2b5263.fieldsAsRaw;
        var _0x95dc51 = null != _0x2b5263.raw && _0x2b5263.raw;
        var _0x2048c0 = "boolean" == typeof _0x2b5263.bsonRegExp && _0x2b5263.bsonRegExp;
        var _0x4aa955 = null != _0x2b5263.promoteBuffers && _0x2b5263.promoteBuffers;
        var _0x5dbbe3 = null == _0x2b5263.promoteLongs || _0x2b5263.promoteLongs;
        var _0x420121 = null == _0x2b5263.promoteValues || _0x2b5263.promoteValues;
        var _0x158853 = _0x24da2b;
        if (_0xff4475.length < 0x5) {
          throw new Error("corrupt bson message < 5 bytes long");
        }
        var _0x198fb8 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
        if (_0x198fb8 < 0x5 || _0x198fb8 > _0xff4475.length) {
          throw new Error("corrupt bson message");
        }
        var _0x13a4c4 = _0x518dbd ? [] : {};
        for (var _0x334503 = 0x0;;) {
          var _0x54ea34 = _0xff4475[_0x24da2b++];
          if (0x0 === _0x54ea34) {
            break;
          }
          for (var _0x4c116e = _0x24da2b; 0x0 !== _0xff4475[_0x4c116e] && _0x4c116e < _0xff4475.length;) {
            _0x4c116e++;
          }
          if (_0x4c116e >= _0xff4475.length) {
            throw new Error("Bad BSON Document: illegal CString");
          }
          var _0x209583 = _0x518dbd ? _0x334503++ : _0xff4475.toString("utf8", _0x24da2b, _0x4c116e);
          _0x24da2b = _0x4c116e + 0x1;
          if (_0x54ea34 === _0x7b78a1.BSON_DATA_STRING) {
            var _0x3b2e6f = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
            if (_0x3b2e6f <= 0x0 || _0x3b2e6f > _0xff4475.length - _0x24da2b || 0x0 !== _0xff4475[_0x24da2b + _0x3b2e6f - 0x1]) {
              throw new Error("bad string length in bson");
            }
            _0x13a4c4[_0x209583] = _0xff4475.toString("utf8", _0x24da2b, _0x24da2b + _0x3b2e6f - 0x1);
            _0x24da2b += _0x3b2e6f;
          } else {
            if (_0x54ea34 === _0x7b78a1.BSON_DATA_OID) {
              var _0x1db208 = _0xd820e0.allocBuffer(0xc);
              _0xff4475.copy(_0x1db208, 0x0, _0x24da2b, _0x24da2b + 0xc);
              _0x13a4c4[_0x209583] = new _0x2dc581(_0x1db208);
              _0x24da2b += 0xc;
            } else {
              if (_0x54ea34 === _0x7b78a1.BSON_DATA_INT && false === _0x420121) {
                _0x13a4c4[_0x209583] = new _0x3dd563(_0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18);
              } else {
                if (_0x54ea34 === _0x7b78a1.BSON_DATA_INT) {
                  _0x13a4c4[_0x209583] = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                } else {
                  if (_0x54ea34 === _0x7b78a1.BSON_DATA_NUMBER && false === _0x420121) {
                    _0x13a4c4[_0x209583] = new _0x4b5f0e(_0xff4475.readDoubleLE(_0x24da2b));
                    _0x24da2b += 0x8;
                  } else {
                    if (_0x54ea34 === _0x7b78a1.BSON_DATA_NUMBER) {
                      _0x13a4c4[_0x209583] = _0xff4475.readDoubleLE(_0x24da2b);
                      _0x24da2b += 0x8;
                    } else {
                      if (_0x54ea34 === _0x7b78a1.BSON_DATA_DATE) {
                        var _0x357517 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                        var _0x290d60 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                        _0x13a4c4[_0x209583] = new Date(new _0x13896a(_0x357517, _0x290d60).toNumber());
                      } else {
                        if (_0x54ea34 === _0x7b78a1.BSON_DATA_BOOLEAN) {
                          if (0x0 !== _0xff4475[_0x24da2b] && 0x1 !== _0xff4475[_0x24da2b]) {
                            throw new Error("illegal boolean type value");
                          }
                          _0x13a4c4[_0x209583] = 0x1 === _0xff4475[_0x24da2b++];
                        } else {
                          if (_0x54ea34 === _0x7b78a1.BSON_DATA_OBJECT) {
                            var _0x24c09c = _0x24da2b;
                            var _0x3ce2fb = _0xff4475[_0x24da2b] | _0xff4475[_0x24da2b + 0x1] << 0x8 | _0xff4475[_0x24da2b + 0x2] << 0x10 | _0xff4475[_0x24da2b + 0x3] << 0x18;
                            if (_0x3ce2fb <= 0x0 || _0x3ce2fb > _0xff4475.length - _0x24da2b) {
                              throw new Error("bad embedded document length in bson");
                            }
                            _0x13a4c4[_0x209583] = _0x95dc51 ? _0xff4475.slice(_0x24da2b, _0x24da2b + _0x3ce2fb) : _0xe84204(_0xff4475, _0x24c09c, _0x2b5263, false);
                            _0x24da2b += _0x3ce2fb;
                          } else {
                            if (_0x54ea34 === _0x7b78a1.BSON_DATA_ARRAY) {
                              _0x24c09c = _0x24da2b;
                              var _0x18910f = _0x2b5263;
                              var _0x476c99 = _0x24da2b + (_0x3ce2fb = _0xff4475[_0x24da2b] | _0xff4475[_0x24da2b + 0x1] << 0x8 | _0xff4475[_0x24da2b + 0x2] << 0x10 | _0xff4475[_0x24da2b + 0x3] << 0x18);
                              if (_0x4ca2ab && _0x4ca2ab[_0x209583]) {
                                _0x18910f = {};
                                for (var _0x44d9c9 in _0x2b5263) _0x18910f[_0x44d9c9] = _0x2b5263[_0x44d9c9];
                                _0x18910f.raw = true;
                              }
                              _0x13a4c4[_0x209583] = _0xe84204(_0xff4475, _0x24c09c, _0x18910f, true);
                              if (0x0 !== _0xff4475[(_0x24da2b += _0x3ce2fb) - 0x1]) {
                                throw new Error("invalid array terminator byte");
                              }
                              if (_0x24da2b !== _0x476c99) {
                                throw new Error("corrupted array bson");
                              }
                            } else {
                              if (_0x54ea34 === _0x7b78a1.BSON_DATA_UNDEFINED) {
                                _0x13a4c4[_0x209583] = undefined;
                              } else {
                                if (_0x54ea34 === _0x7b78a1.BSON_DATA_NULL) {
                                  _0x13a4c4[_0x209583] = null;
                                } else {
                                  if (_0x54ea34 === _0x7b78a1.BSON_DATA_LONG) {
                                    _0x357517 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                                    _0x290d60 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                                    var _0x2c388c = new _0x13896a(_0x357517, _0x290d60);
                                    _0x13a4c4[_0x209583] = _0x5dbbe3 && true === _0x420121 && _0x2c388c.lessThanOrEqual(_0x5c9e41) && _0x2c388c.greaterThanOrEqual(_0x66ec3c) ? _0x2c388c.toNumber() : _0x2c388c;
                                  } else {
                                    if (_0x54ea34 === _0x7b78a1.BSON_DATA_DECIMAL128) {
                                      var _0x237070 = _0xd820e0.allocBuffer(0x10);
                                      _0xff4475.copy(_0x237070, 0x0, _0x24da2b, _0x24da2b + 0x10);
                                      _0x24da2b += 0x10;
                                      var _0x443c44 = new _0x2a0fd1(_0x237070);
                                      _0x13a4c4[_0x209583] = _0x443c44.toObject ? _0x443c44.toObject() : _0x443c44;
                                    } else {
                                      if (_0x54ea34 === _0x7b78a1.BSON_DATA_BINARY) {
                                        var _0x4a4ef6 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                                        var _0xc24d14 = _0x4a4ef6;
                                        var _0x3db2e1 = _0xff4475[_0x24da2b++];
                                        if (_0x4a4ef6 < 0x0) {
                                          throw new Error("Negative binary type element size found");
                                        }
                                        if (_0x4a4ef6 > _0xff4475.length) {
                                          throw new Error("Binary type size larger than document size");
                                        }
                                        if (null != _0xff4475.slice) {
                                          if (_0x3db2e1 === _0xeac924.SUBTYPE_BYTE_ARRAY) {
                                            if ((_0x4a4ef6 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18) < 0x0) {
                                              throw new Error("Negative binary type element size found for subtype 0x02");
                                            }
                                            if (_0x4a4ef6 > _0xc24d14 - 0x4) {
                                              throw new Error("Binary type with subtype 0x02 contains to long binary size");
                                            }
                                            if (_0x4a4ef6 < _0xc24d14 - 0x4) {
                                              throw new Error("Binary type with subtype 0x02 contains to short binary size");
                                            }
                                          }
                                          _0x13a4c4[_0x209583] = _0x4aa955 && _0x420121 ? _0xff4475.slice(_0x24da2b, _0x24da2b + _0x4a4ef6) : new _0xeac924(_0xff4475.slice(_0x24da2b, _0x24da2b + _0x4a4ef6), _0x3db2e1);
                                        } else {
                                          var _0xccdbd6 = "undefined" != typeof Uint8Array ? new Uint8Array(new ArrayBuffer(_0x4a4ef6)) : new Array(_0x4a4ef6);
                                          if (_0x3db2e1 === _0xeac924.SUBTYPE_BYTE_ARRAY) {
                                            if ((_0x4a4ef6 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18) < 0x0) {
                                              throw new Error("Negative binary type element size found for subtype 0x02");
                                            }
                                            if (_0x4a4ef6 > _0xc24d14 - 0x4) {
                                              throw new Error("Binary type with subtype 0x02 contains to long binary size");
                                            }
                                            if (_0x4a4ef6 < _0xc24d14 - 0x4) {
                                              throw new Error("Binary type with subtype 0x02 contains to short binary size");
                                            }
                                          }
                                          for (_0x4c116e = 0x0; _0x4c116e < _0x4a4ef6; _0x4c116e++) {
                                            _0xccdbd6[_0x4c116e] = _0xff4475[_0x24da2b + _0x4c116e];
                                          }
                                          _0x13a4c4[_0x209583] = _0x4aa955 && _0x420121 ? _0xccdbd6 : new _0xeac924(_0xccdbd6, _0x3db2e1);
                                        }
                                        _0x24da2b += _0x4a4ef6;
                                      } else {
                                        if (_0x54ea34 === _0x7b78a1.BSON_DATA_REGEXP && false === _0x2048c0) {
                                          for (_0x4c116e = _0x24da2b; 0x0 !== _0xff4475[_0x4c116e] && _0x4c116e < _0xff4475.length;) {
                                            _0x4c116e++;
                                          }
                                          if (_0x4c116e >= _0xff4475.length) {
                                            throw new Error("Bad BSON Document: illegal CString");
                                          }
                                          var _0x55ae7f = _0xff4475.toString("utf8", _0x24da2b, _0x4c116e);
                                          for (_0x4c116e = _0x24da2b = _0x4c116e + 0x1; 0x0 !== _0xff4475[_0x4c116e] && _0x4c116e < _0xff4475.length;) {
                                            _0x4c116e++;
                                          }
                                          if (_0x4c116e >= _0xff4475.length) {
                                            throw new Error("Bad BSON Document: illegal CString");
                                          }
                                          var _0x5d1cff = _0xff4475.toString("utf8", _0x24da2b, _0x4c116e);
                                          _0x24da2b = _0x4c116e + 0x1;
                                          var _0x1c9a87 = new Array(_0x5d1cff.length);
                                          for (_0x4c116e = 0x0; _0x4c116e < _0x5d1cff.length; _0x4c116e++) {
                                            switch (_0x5d1cff[_0x4c116e]) {
                                              case 'm':
                                                _0x1c9a87[_0x4c116e] = 'm';
                                                break;
                                              case 's':
                                                _0x1c9a87[_0x4c116e] = 'g';
                                                break;
                                              case 'i':
                                                _0x1c9a87[_0x4c116e] = 'i';
                                            }
                                          }
                                          _0x13a4c4[_0x209583] = new RegExp(_0x55ae7f, _0x1c9a87.join(''));
                                        } else {
                                          if (_0x54ea34 === _0x7b78a1.BSON_DATA_REGEXP && true === _0x2048c0) {
                                            for (_0x4c116e = _0x24da2b; 0x0 !== _0xff4475[_0x4c116e] && _0x4c116e < _0xff4475.length;) {
                                              _0x4c116e++;
                                            }
                                            if (_0x4c116e >= _0xff4475.length) {
                                              throw new Error("Bad BSON Document: illegal CString");
                                            }
                                            _0x55ae7f = _0xff4475.toString("utf8", _0x24da2b, _0x4c116e);
                                            for (_0x4c116e = _0x24da2b = _0x4c116e + 0x1; 0x0 !== _0xff4475[_0x4c116e] && _0x4c116e < _0xff4475.length;) {
                                              _0x4c116e++;
                                            }
                                            if (_0x4c116e >= _0xff4475.length) {
                                              throw new Error("Bad BSON Document: illegal CString");
                                            }
                                            _0x5d1cff = _0xff4475.toString("utf8", _0x24da2b, _0x4c116e);
                                            _0x24da2b = _0x4c116e + 0x1;
                                            _0x13a4c4[_0x209583] = new _0xa0879f(_0x55ae7f, _0x5d1cff);
                                          } else {
                                            if (_0x54ea34 === _0x7b78a1.BSON_DATA_SYMBOL) {
                                              if ((_0x3b2e6f = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18) <= 0x0 || _0x3b2e6f > _0xff4475.length - _0x24da2b || 0x0 !== _0xff4475[_0x24da2b + _0x3b2e6f - 0x1]) {
                                                throw new Error("bad string length in bson");
                                              }
                                              _0x13a4c4[_0x209583] = new _0x5a6319(_0xff4475.toString("utf8", _0x24da2b, _0x24da2b + _0x3b2e6f - 0x1));
                                              _0x24da2b += _0x3b2e6f;
                                            } else {
                                              if (_0x54ea34 === _0x7b78a1.BSON_DATA_TIMESTAMP) {
                                                _0x357517 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                                                _0x290d60 = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                                                _0x13a4c4[_0x209583] = new _0x4d7c52(_0x357517, _0x290d60);
                                              } else {
                                                if (_0x54ea34 === _0x7b78a1.BSON_DATA_MIN_KEY) {
                                                  _0x13a4c4[_0x209583] = new _0x346a78();
                                                } else {
                                                  if (_0x54ea34 === _0x7b78a1.BSON_DATA_MAX_KEY) {
                                                    _0x13a4c4[_0x209583] = new _0xc9d918();
                                                  } else {
                                                    if (_0x54ea34 === _0x7b78a1.BSON_DATA_CODE) {
                                                      if ((_0x3b2e6f = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18) <= 0x0 || _0x3b2e6f > _0xff4475.length - _0x24da2b || 0x0 !== _0xff4475[_0x24da2b + _0x3b2e6f - 0x1]) {
                                                        throw new Error("bad string length in bson");
                                                      }
                                                      var _0x3d5a86 = _0xff4475.toString("utf8", _0x24da2b, _0x24da2b + _0x3b2e6f - 0x1);
                                                      if (_0xa8965a) {
                                                        if (_0x49b377) {
                                                          var _0x4cdf10 = _0x2c1962 ? null(_0x3d5a86) : _0x3d5a86;
                                                          _0x13a4c4[_0x209583] = _0x24de85(_0x3ad671, _0x4cdf10, _0x3d5a86, _0x13a4c4);
                                                        } else {
                                                          _0x13a4c4[_0x209583] = _0xbd372c(_0x3d5a86);
                                                        }
                                                      } else {
                                                        _0x13a4c4[_0x209583] = new _0x45ff06(_0x3d5a86);
                                                      }
                                                      _0x24da2b += _0x3b2e6f;
                                                    } else {
                                                      if (_0x54ea34 === _0x7b78a1.BSON_DATA_CODE_W_SCOPE) {
                                                        var _0x1289ee = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18;
                                                        if (_0x1289ee < 0xd) {
                                                          throw new Error("code_w_scope total size shorter minimum expected length");
                                                        }
                                                        if ((_0x3b2e6f = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18) <= 0x0 || _0x3b2e6f > _0xff4475.length - _0x24da2b || 0x0 !== _0xff4475[_0x24da2b + _0x3b2e6f - 0x1]) {
                                                          throw new Error("bad string length in bson");
                                                        }
                                                        _0x3d5a86 = _0xff4475.toString("utf8", _0x24da2b, _0x24da2b + _0x3b2e6f - 0x1);
                                                        _0x24c09c = _0x24da2b += _0x3b2e6f;
                                                        _0x3ce2fb = _0xff4475[_0x24da2b] | _0xff4475[_0x24da2b + 0x1] << 0x8 | _0xff4475[_0x24da2b + 0x2] << 0x10 | _0xff4475[_0x24da2b + 0x3] << 0x18;
                                                        var _0xb2b6d5 = _0xe84204(_0xff4475, _0x24c09c, _0x2b5263, false);
                                                        _0x24da2b += _0x3ce2fb;
                                                        if (_0x1289ee < 0x8 + _0x3ce2fb + _0x3b2e6f) {
                                                          throw new Error("code_w_scope total size is to short, truncating scope");
                                                        }
                                                        if (_0x1289ee > 0x8 + _0x3ce2fb + _0x3b2e6f) {
                                                          throw new Error("code_w_scope total size is to long, clips outer document");
                                                        }
                                                        if (_0xa8965a) {
                                                          if (_0x49b377) {
                                                            _0x4cdf10 = _0x2c1962 ? null(_0x3d5a86) : _0x3d5a86;
                                                            _0x13a4c4[_0x209583] = _0x24de85(_0x3ad671, _0x4cdf10, _0x3d5a86, _0x13a4c4);
                                                          } else {
                                                            _0x13a4c4[_0x209583] = _0xbd372c(_0x3d5a86);
                                                          }
                                                          _0x13a4c4[_0x209583].scope = _0xb2b6d5;
                                                        } else {
                                                          _0x13a4c4[_0x209583] = new _0x45ff06(_0x3d5a86, _0xb2b6d5);
                                                        }
                                                      } else {
                                                        if (_0x54ea34 !== _0x7b78a1.BSON_DATA_DBPOINTER) {
                                                          throw new Error("Detected unknown BSON type " + _0x54ea34.toString(0x10) + " for fieldname \"" + _0x209583 + "\", are you using the latest BSON parser");
                                                        }
                                                        if ((_0x3b2e6f = _0xff4475[_0x24da2b++] | _0xff4475[_0x24da2b++] << 0x8 | _0xff4475[_0x24da2b++] << 0x10 | _0xff4475[_0x24da2b++] << 0x18) <= 0x0 || _0x3b2e6f > _0xff4475.length - _0x24da2b || 0x0 !== _0xff4475[_0x24da2b + _0x3b2e6f - 0x1]) {
                                                          throw new Error("bad string length in bson");
                                                        }
                                                        var _0x39ff3a = _0xff4475.toString("utf8", _0x24da2b, _0x24da2b + _0x3b2e6f - 0x1);
                                                        _0x24da2b += _0x3b2e6f;
                                                        var _0x3cb1df = _0xd820e0.allocBuffer(0xc);
                                                        _0xff4475.copy(_0x3cb1df, 0x0, _0x24da2b, _0x24da2b + 0xc);
                                                        _0x1db208 = new _0x2dc581(_0x3cb1df);
                                                        _0x24da2b += 0xc;
                                                        var _0x1bf197 = _0x39ff3a.split('.');
                                                        var _0x2256c7 = _0x1bf197.shift();
                                                        var _0x3f4b9f = _0x1bf197.join('.');
                                                        _0x13a4c4[_0x209583] = new _0x12dd1c(_0x3f4b9f, _0x1db208, _0x2256c7);
                                                      }
                                                    }
                                                  }
                                                }
                                              }
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        if (_0x198fb8 !== _0x24da2b - _0x158853) {
          if (_0x518dbd) {
            throw new Error("corrupt array bson");
          }
          throw new Error("corrupt object bson");
        }
        if (null != _0x13a4c4.$id) {
          _0x13a4c4 = new _0x12dd1c(_0x13a4c4.$ref, _0x13a4c4.$id, _0x13a4c4.$db);
        }
        return _0x13a4c4;
      };
      var _0x24de85 = function (_0x5bb0bf, _0x3c8d6f, _0x2ebd01, _0x3fa4f3) {
        if (null == _0x5bb0bf[_0x3c8d6f]) {
          eval("value = " + _0x2ebd01);
          _0x5bb0bf[_0x3c8d6f] = null;
        }
        return _0x5bb0bf[_0x3c8d6f].bind(_0x3fa4f3);
      };
      var _0xbd372c = function (_0x26e71d) {
        eval("value = " + _0x26e71d);
        return null;
      };
      var _0x7b78a1 = {};
      var _0x3ad671 = _0x7b78a1.functionCache = {};
      _0x7b78a1.BSON_DATA_NUMBER = 0x1;
      _0x7b78a1.BSON_DATA_STRING = 0x2;
      _0x7b78a1.BSON_DATA_OBJECT = 0x3;
      _0x7b78a1.BSON_DATA_ARRAY = 0x4;
      _0x7b78a1.BSON_DATA_BINARY = 0x5;
      _0x7b78a1.BSON_DATA_UNDEFINED = 0x6;
      _0x7b78a1.BSON_DATA_OID = 0x7;
      _0x7b78a1.BSON_DATA_BOOLEAN = 0x8;
      _0x7b78a1.BSON_DATA_DATE = 0x9;
      _0x7b78a1.BSON_DATA_NULL = 0xa;
      _0x7b78a1.BSON_DATA_REGEXP = 0xb;
      _0x7b78a1.BSON_DATA_DBPOINTER = 0xc;
      _0x7b78a1.BSON_DATA_CODE = 0xd;
      _0x7b78a1.BSON_DATA_SYMBOL = 0xe;
      _0x7b78a1.BSON_DATA_CODE_W_SCOPE = 0xf;
      _0x7b78a1.BSON_DATA_INT = 0x10;
      _0x7b78a1.BSON_DATA_TIMESTAMP = 0x11;
      _0x7b78a1.BSON_DATA_LONG = 0x12;
      _0x7b78a1.BSON_DATA_DECIMAL128 = 0x13;
      _0x7b78a1.BSON_DATA_MIN_KEY = 0xff;
      _0x7b78a1.BSON_DATA_MAX_KEY = 0x7f;
      _0x7b78a1.BSON_BINARY_SUBTYPE_DEFAULT = 0x0;
      _0x7b78a1.BSON_BINARY_SUBTYPE_FUNCTION = 0x1;
      _0x7b78a1.BSON_BINARY_SUBTYPE_BYTE_ARRAY = 0x2;
      _0x7b78a1.BSON_BINARY_SUBTYPE_UUID = 0x3;
      _0x7b78a1.BSON_BINARY_SUBTYPE_MD5 = 0x4;
      _0x7b78a1.BSON_BINARY_SUBTYPE_USER_DEFINED = 0x80;
      _0x7b78a1.BSON_INT32_MAX = 0x7fffffff;
      _0x7b78a1.BSON_INT32_MIN = -0x80000000;
      _0x7b78a1.BSON_INT64_MAX = Math.pow(0x2, 0x3f) - 0x1;
      _0x7b78a1.BSON_INT64_MIN = -Math.pow(0x2, 0x3f);
      _0x7b78a1.JS_INT_MAX = 0x20000000000000;
      _0x7b78a1.JS_INT_MIN = -0x20000000000000;
      var _0x5c9e41 = _0x13896a.fromNumber(0x20000000000000);
      var _0x66ec3c = _0x13896a.fromNumber(-0x20000000000000);
      _0x344a5c.exports = _0xde5b22;
    },
    0x1e53: (_0x12f9f2, _0x50421b, _0x13aaec) => {
      'use strict';

      var _0x5be4cf = _0x13aaec(0x1549);
      _0x12f9f2.exports = function (_0x42f7d7, _0x1a1f9c, _0x4ac545, _0x1aac83, _0x15cafa) {
        var _0x109369 = new Error(_0x42f7d7);
        return _0x5be4cf(_0x109369, _0x1a1f9c, _0x4ac545, _0x1aac83, _0x15cafa);
      };
    },
    0x1e99: (_0x4af978, _0x3d57be, _0x373915) => {
      _0x3d57be.formatArgs = function (_0x113b0e) {
        _0x113b0e[0x0] = (this.useColors ? '%c' : '') + this.namespace + (this.useColors ? " %c" : " ") + _0x113b0e[0x0] + (this.useColors ? "%c " : " ") + '+' + _0x4af978.exports.humanize(this.diff);
        if (!this.useColors) {
          return;
        }
        const _0x1df665 = "color: " + this.color;
        _0x113b0e.splice(0x1, 0x0, _0x1df665, "color: inherit");
        let _0x5bb616 = 0x0;
        let _0x4f6e24 = 0x0;
        _0x113b0e[0x0].replace(/%[a-zA-Z%]/g, _0x5bf073 => {
          if ('%%' !== _0x5bf073) {
            _0x5bb616++;
            if ('%c' === _0x5bf073) {
              _0x4f6e24 = _0x5bb616;
            }
          }
        });
        _0x113b0e.splice(_0x4f6e24, 0x0, _0x1df665);
      };
      _0x3d57be.save = function (_0x47691e) {
        try {
          if (_0x47691e) {
            _0x3d57be.storage.setItem("debug", _0x47691e);
          } else {
            _0x3d57be.storage.removeItem("debug");
          }
        } catch (_0xa01583) {}
      };
      _0x3d57be.load = function () {
        let _0x53d7e1;
        try {
          _0x53d7e1 = _0x3d57be.storage.getItem("debug") || _0x3d57be.storage.getItem("DEBUG");
        } catch (_0x10dc12) {}
        if (!_0x53d7e1 && "undefined" != typeof process && "env" in process) {
          _0x53d7e1 = process.env.DEBUG;
        }
        return _0x53d7e1;
      };
      _0x3d57be.useColors = function () {
        if ("undefined" != typeof window && window.process && ("renderer" === window.process.type || window.process.__nwjs)) {
          return true;
        }
        if ('undefined' != typeof navigator && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)) {
          return false;
        }
        let _0x23de7c;
        return 'undefined' != typeof document && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || "undefined" != typeof window && window.console && (window.console.firebug || window.console.exception && window.console.table) || "undefined" != typeof navigator && navigator.userAgent && (_0x23de7c = navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)) && parseInt(_0x23de7c[0x1], 0xa) >= 0x1f || "undefined" != typeof navigator && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/);
      };
      _0x3d57be.storage = function () {
        try {
          return localStorage;
        } catch (_0x1bf5f0) {}
      }();
      _0x3d57be.destroy = (() => {
        let _0x29bd24 = false;
        return () => {
          if (!_0x29bd24) {
            _0x29bd24 = true;
            console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");
          }
        };
      })();
      _0x3d57be.colors = ["#0000CC", "#0000FF", "#0033CC", "#0033FF", "#0066CC", "#0066FF", '#0099CC', "#0099FF", '#00CC00', "#00CC33", '#00CC66', "#00CC99", "#00CCCC", "#00CCFF", "#3300CC", "#3300FF", "#3333CC", '#3333FF', "#3366CC", "#3366FF", "#3399CC", '#3399FF', '#33CC00', "#33CC33", "#33CC66", "#33CC99", "#33CCCC", "#33CCFF", "#6600CC", "#6600FF", "#6633CC", "#6633FF", '#66CC00', "#66CC33", '#9900CC', '#9900FF', "#9933CC", "#9933FF", "#99CC00", "#99CC33", "#CC0000", "#CC0033", "#CC0066", "#CC0099", '#CC00CC', "#CC00FF", "#CC3300", "#CC3333", "#CC3366", "#CC3399", '#CC33CC', '#CC33FF', "#CC6600", "#CC6633", "#CC9900", "#CC9933", "#CCCC00", '#CCCC33', "#FF0000", "#FF0033", "#FF0066", "#FF0099", "#FF00CC", "#FF00FF", "#FF3300", '#FF3333', "#FF3366", '#FF3399', "#FF33CC", "#FF33FF", "#FF6600", "#FF6633", "#FF9900", "#FF9933", '#FFCC00', "#FFCC33"];
      _0x3d57be.log = console.debug || console.log || (() => {});
      _0x4af978.exports = _0x373915(0x2e0)(_0x3d57be);
      const {
        formatters: _0x5f3781
      } = _0x4af978.exports;
      _0x5f3781.j = function (_0x2d39cf) {
        try {
          return JSON.stringify(_0x2d39cf);
        } catch (_0x263cf4) {
          return "[UnexpectedJSONParseError]: " + _0x263cf4.message;
        }
      };
    },
    0x1f18: (_0x4c848c, _0x29a1e3, _0x390336) => {
      'use strict';

      var _0x4ebfeb = _0x390336(0x252c);
      var _0x2e1784 = _0x390336(0x1d62);
      var _0x1afad9 = _0x390336(0x258f);
      var _0x368b3b = _0x390336(0x2392);
      var _0x5f0f83 = _0x390336(0x21a3);
      var _0x23ab65 = _0x390336(0x163c);
      var _0x2ffc5b = _0x390336(0xc5c).http;
      var _0xca6140 = _0x390336(0xc5c).https;
      var _0x28388f = _0x390336(0x1b68);
      var _0x2fbadf = _0x390336(0xc22);
      var _0x4138e8 = _0x390336(0x25a9).version;
      var _0xa2106 = _0x390336(0x1e53);
      var _0x4df835 = _0x390336(0x1549);
      var _0x3d756b = _0x390336(0x1b4b);
      var _0x317de1 = _0x390336(0x788);
      var _0x4a98af = /https:?/;
      function _0x3f0fa7(_0x4bf754, _0x4b01d4, _0x59dd42) {
        _0x4bf754.hostname = _0x4b01d4.host;
        _0x4bf754.host = _0x4b01d4.host;
        _0x4bf754.port = _0x4b01d4.port;
        _0x4bf754.path = _0x59dd42;
        if (_0x4b01d4.auth) {
          var _0x483510 = Buffer.from(_0x4b01d4.auth.username + ':' + _0x4b01d4.auth.password, "utf8").toString("base64");
          _0x4bf754.headers["Proxy-Authorization"] = "Basic " + _0x483510;
        }
        _0x4bf754.beforeRedirect = function (_0x34d53c) {
          _0x34d53c.headers.host = _0x34d53c.host;
          _0x3f0fa7(_0x34d53c, _0x4b01d4, _0x34d53c.href);
        };
      }
      _0x4c848c.exports = function (_0x6a4ad) {
        return new Promise(function (_0xfdd21f, _0x9d9f14) {
          var _0x5cae87;
          function _0x5ec597() {
            if (_0x6a4ad.cancelToken) {
              _0x6a4ad.cancelToken.unsubscribe(_0x5cae87);
            }
            if (_0x6a4ad.signal) {
              _0x6a4ad.signal.removeEventListener('abort', _0x5cae87);
            }
          }
          var _0x22135d = function (_0x2bf451) {
            _0x5ec597();
            _0xfdd21f(_0x2bf451);
          };
          var _0x536494 = function (_0x5ee335) {
            _0x5ec597();
            _0x9d9f14(_0x5ee335);
          };
          var _0x361176 = _0x6a4ad.data;
          var _0x2cba1a = _0x6a4ad.headers;
          var _0x4bafc2 = {};
          Object.keys(_0x2cba1a).forEach(function (_0x34e696) {
            _0x4bafc2[_0x34e696.toLowerCase()] = _0x34e696;
          });
          if ("user-agent" in _0x4bafc2) {
            if (!_0x2cba1a[_0x4bafc2['user-agent']]) {
              delete _0x2cba1a[_0x4bafc2['user-agent']];
            }
          } else {
            _0x2cba1a['User-Agent'] = "axios/" + _0x4138e8;
          }
          if (_0x361176 && !_0x4ebfeb.isStream(_0x361176)) {
            if (Buffer.isBuffer(_0x361176)) {
              ;
            } else {
              if (_0x4ebfeb.isArrayBuffer(_0x361176)) {
                _0x361176 = Buffer.from(new Uint8Array(_0x361176));
              } else {
                if (!_0x4ebfeb.isString(_0x361176)) {
                  return _0x536494(_0xa2106("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream", _0x6a4ad));
                }
                _0x361176 = Buffer.from(_0x361176, 'utf-8');
              }
            }
            if (!_0x4bafc2["content-length"]) {
              _0x2cba1a["Content-Length"] = _0x361176.length;
            }
          }
          var _0x3f73c1 = undefined;
          if (_0x6a4ad.auth) {
            _0x3f73c1 = (_0x6a4ad.auth.username || '') + ':' + (_0x6a4ad.auth.password || '');
          }
          var _0x1023c3 = _0x1afad9(_0x6a4ad.baseURL, _0x6a4ad.url);
          var _0x392345 = _0x28388f.parse(_0x1023c3);
          var _0x193e06 = _0x392345.protocol || 'http:';
          if (!_0x3f73c1 && _0x392345.auth) {
            var _0x14da0b = _0x392345.auth.split(':');
            _0x3f73c1 = (_0x14da0b[0x0] || '') + ':' + (_0x14da0b[0x1] || '');
          }
          if (_0x3f73c1 && _0x4bafc2.authorization) {
            delete _0x2cba1a[_0x4bafc2.authorization];
          }
          var _0x9ab456 = _0x4a98af.test(_0x193e06);
          var _0x5236db = _0x9ab456 ? _0x6a4ad.httpsAgent : _0x6a4ad.httpAgent;
          var _0x432a6c = {
            'path': _0x368b3b(_0x392345.path, _0x6a4ad.params, _0x6a4ad.paramsSerializer).replace(/^\?/, ''),
            'method': _0x6a4ad.method.toUpperCase(),
            'headers': _0x2cba1a,
            'agent': _0x5236db,
            'agents': {
              'http': _0x6a4ad.httpAgent,
              'https': _0x6a4ad.httpsAgent
            },
            'auth': _0x3f73c1
          };
          if (_0x6a4ad.socketPath) {
            _0x432a6c.socketPath = _0x6a4ad.socketPath;
          } else {
            _0x432a6c.hostname = _0x392345.hostname;
            _0x432a6c.port = _0x392345.port;
          }
          var _0x387495;
          var _0x3c7665 = _0x6a4ad.proxy;
          if (!_0x3c7665 && false !== _0x3c7665) {
            var _0xb75924 = _0x193e06.slice(0x0, -0x1) + '_proxy';
            var _0x1bdf03 = process.env[_0xb75924] || process.env[_0xb75924.toUpperCase()];
            if (_0x1bdf03) {
              var _0x34d36d = _0x28388f.parse(_0x1bdf03);
              var _0x14f69c = process.env.no_proxy || process.env.NO_PROXY;
              var _0x1912a3 = true;
              if (_0x14f69c) {
                _0x1912a3 = !_0x14f69c.split(',').map(function (_0x4056a9) {
                  return _0x4056a9.trim();
                }).some(function (_0x2cef95) {
                  return !!_0x2cef95 && ('*' === _0x2cef95 || '.' === _0x2cef95[0x0] && _0x392345.hostname.substr(_0x392345.hostname.length - _0x2cef95.length) === _0x2cef95 || _0x392345.hostname === _0x2cef95);
                });
              }
              if (_0x1912a3 && (_0x3c7665 = {
                'host': _0x34d36d.hostname,
                'port': _0x34d36d.port,
                'protocol': _0x34d36d.protocol
              }, _0x34d36d.auth)) {
                var _0x26a029 = _0x34d36d.auth.split(':');
                _0x3c7665.auth = {
                  'username': _0x26a029[0x0],
                  'password': _0x26a029[0x1]
                };
              }
            }
          }
          if (_0x3c7665) {
            _0x432a6c.headers.host = _0x392345.hostname + (_0x392345.port ? ':' + _0x392345.port : '');
            _0x3f0fa7(_0x432a6c, _0x3c7665, _0x193e06 + '//' + _0x392345.hostname + (_0x392345.port ? ':' + _0x392345.port : '') + _0x432a6c.path);
          }
          var _0x5939c4 = _0x9ab456 && (!_0x3c7665 || _0x4a98af.test(_0x3c7665.protocol));
          if (_0x6a4ad.transport) {
            _0x387495 = _0x6a4ad.transport;
          } else if (0x0 === _0x6a4ad.maxRedirects) {
            _0x387495 = _0x5939c4 ? _0x23ab65 : _0x5f0f83;
          } else {
            if (_0x6a4ad.maxRedirects) {
              _0x432a6c.maxRedirects = _0x6a4ad.maxRedirects;
            }
            _0x387495 = _0x5939c4 ? _0xca6140 : _0x2ffc5b;
          }
          if (_0x6a4ad.maxBodyLength > -0x1) {
            _0x432a6c.maxBodyLength = _0x6a4ad.maxBodyLength;
          }
          if (_0x6a4ad.insecureHTTPParser) {
            _0x432a6c.insecureHTTPParser = _0x6a4ad.insecureHTTPParser;
          }
          var _0x4a5e7f = _0x387495.request(_0x432a6c, function (_0x2aea2e) {
            if (!_0x4a5e7f.aborted) {
              var _0x487ead = _0x2aea2e;
              var _0x1f2340 = _0x2aea2e.req || _0x4a5e7f;
              if (0xcc !== _0x2aea2e.statusCode && 'HEAD' !== _0x1f2340.method && false !== _0x6a4ad.decompress) {
                switch (_0x2aea2e.headers["content-encoding"]) {
                  case "gzip":
                  case "compress":
                  case "deflate":
                    _0x487ead = _0x487ead.pipe(_0x2fbadf.createUnzip());
                    delete _0x2aea2e.headers["content-encoding"];
                }
              }
              var _0x3b26d2 = {
                'status': _0x2aea2e.statusCode,
                'statusText': _0x2aea2e.statusMessage,
                'headers': _0x2aea2e.headers,
                'config': _0x6a4ad,
                'request': _0x1f2340
              };
              if ("stream" === _0x6a4ad.responseType) {
                _0x3b26d2.data = _0x487ead;
                _0x2e1784(_0x22135d, _0x536494, _0x3b26d2);
              } else {
                var _0xd4830c = [];
                var _0x309c32 = 0x0;
                _0x487ead.on('data', function (_0x4c23ae) {
                  _0xd4830c.push(_0x4c23ae);
                  _0x309c32 += _0x4c23ae.length;
                  if (_0x6a4ad.maxContentLength > -0x1 && _0x309c32 > _0x6a4ad.maxContentLength) {
                    _0x487ead.destroy();
                    _0x536494(_0xa2106("maxContentLength size of " + _0x6a4ad.maxContentLength + " exceeded", _0x6a4ad, null, _0x1f2340));
                  }
                });
                _0x487ead.on("error", function (_0x1169ee) {
                  if (!_0x4a5e7f.aborted) {
                    _0x536494(_0x4df835(_0x1169ee, _0x6a4ad, null, _0x1f2340));
                  }
                });
                _0x487ead.on("end", function () {
                  var _0xd99df7 = Buffer.concat(_0xd4830c);
                  if ("arraybuffer" !== _0x6a4ad.responseType) {
                    _0xd99df7 = _0xd99df7.toString(_0x6a4ad.responseEncoding);
                    if (!(_0x6a4ad.responseEncoding && "utf8" !== _0x6a4ad.responseEncoding)) {
                      _0xd99df7 = _0x4ebfeb.stripBOM(_0xd99df7);
                    }
                  }
                  _0x3b26d2.data = _0xd99df7;
                  _0x2e1784(_0x22135d, _0x536494, _0x3b26d2);
                });
              }
            }
          });
          _0x4a5e7f.on("error", function (_0x358925) {
            if (!(_0x4a5e7f.aborted && "ERR_FR_TOO_MANY_REDIRECTS" !== _0x358925.code)) {
              _0x536494(_0x4df835(_0x358925, _0x6a4ad, null, _0x4a5e7f));
            }
          });
          if (_0x6a4ad.timeout) {
            var _0x263a4b = parseInt(_0x6a4ad.timeout, 0xa);
            if (isNaN(_0x263a4b)) {
              return void _0x536494(_0xa2106("error trying to parse `config.timeout` to int", _0x6a4ad, 'ERR_PARSE_TIMEOUT', _0x4a5e7f));
            }
            _0x4a5e7f.setTimeout(_0x263a4b, function () {
              _0x4a5e7f.abort();
              var _0x5a5ecc = _0x6a4ad.transitional || _0x3d756b.transitional;
              _0x536494(_0xa2106("timeout of " + _0x263a4b + "ms exceeded", _0x6a4ad, _0x5a5ecc.clarifyTimeoutError ? "ETIMEDOUT" : "ECONNABORTED", _0x4a5e7f));
            });
          }
          if (_0x6a4ad.cancelToken || _0x6a4ad.signal) {
            _0x5cae87 = function (_0x42d943) {
              if (!_0x4a5e7f.aborted) {
                _0x4a5e7f.abort();
                _0x536494(!_0x42d943 || _0x42d943 && _0x42d943.type ? new _0x317de1("canceled") : _0x42d943);
              }
            };
            if (_0x6a4ad.cancelToken) {
              _0x6a4ad.cancelToken.subscribe(_0x5cae87);
            }
            if (_0x6a4ad.signal) {
              if (_0x6a4ad.signal.aborted) {
                _0x5cae87();
              } else {
                _0x6a4ad.signal.addEventListener("abort", _0x5cae87);
              }
            }
          }
          if (_0x4ebfeb.isStream(_0x361176)) {
            _0x361176.on("error", function (_0xf7c65) {
              _0x536494(_0x4df835(_0xf7c65, _0x6a4ad, null, _0x4a5e7f));
            }).pipe(_0x4a5e7f);
          } else {
            _0x4a5e7f.end(_0x361176);
          }
        });
      };
    },
    0x1f26: (_0x5a0281, _0x3a9be0, _0x208b2c) => {
      'use strict';

      const _0x4a659c = _0x208b2c(0xe97);
      const _0x1b753d = _0x208b2c(0x1b10);
      const _0xb3bfcc = _0x208b2c(0x759).copySync;
      const _0x2dbfdd = _0x208b2c(0x152e).removeSync;
      const _0x53d6fd = _0x208b2c(0xed6).mkdirpSync;
      const _0x22d57a = _0x208b2c(0x193e);
      function _0x2b2c8a(_0x4fb32d, _0x4ee3cd, _0x4a61c1) {
        try {
          _0x4a659c.renameSync(_0x4fb32d, _0x4ee3cd);
        } catch (_0x3aeda7) {
          if ('EXDEV' !== _0x3aeda7.code) {
            throw _0x3aeda7;
          }
          return function (_0x2c13a7, _0x21a44e, _0x58d20f) {
            _0xb3bfcc(_0x2c13a7, _0x21a44e, {
              'overwrite': _0x58d20f,
              'errorOnExist': true
            });
            return _0x2dbfdd(_0x2c13a7);
          }(_0x4fb32d, _0x4ee3cd, _0x4a61c1);
        }
      }
      _0x5a0281.exports = function (_0x5d2d0f, _0x477281, _0x3bf0e0) {
        const _0x1cb590 = (_0x3bf0e0 = _0x3bf0e0 || {}).overwrite || _0x3bf0e0.clobber || false;
        const {
          srcStat: _0x2f470e
        } = _0x22d57a.checkPathsSync(_0x5d2d0f, _0x477281, "move");
        _0x22d57a.checkParentPathsSync(_0x5d2d0f, _0x2f470e, _0x477281, "move");
        _0x53d6fd(_0x1b753d.dirname(_0x477281));
        return function (_0x376f4f, _0x2adb20, _0x4200ba) {
          if (_0x4200ba) {
            _0x2dbfdd(_0x2adb20);
            return _0x2b2c8a(_0x376f4f, _0x2adb20, _0x4200ba);
          }
          if (_0x4a659c.existsSync(_0x2adb20)) {
            throw new Error("dest already exists.");
          }
          return _0x2b2c8a(_0x376f4f, _0x2adb20, _0x4200ba);
        }(_0x5d2d0f, _0x477281, _0x1cb590);
      };
    },
    0x1f2c: _0x9a4412 => {
      'use strict';

      _0x9a4412.exports = function (_0x56e325) {
        return function (_0x31bf77) {
          return _0x56e325.apply(null, _0x31bf77);
        };
      };
    },
    0x1f4f: (_0x244c15, _0x1b00d6, _0x13c8f2) => {
      'use strict';

      var _0x5baaf8 = _0x13c8f2(0x252c);
      var _0x79dd60 = _0x13c8f2(0x2334);
      var _0xc60545 = _0x13c8f2(0x1423);
      var _0xba3e98 = _0x13c8f2(0x14df);
      var _0x1d89a9 = function _0xe80c1d(_0x35cff6) {
        var _0x2b0c45 = new _0xc60545(_0x35cff6);
        var _0x57e60b = _0x79dd60(_0xc60545.prototype.request, _0x2b0c45);
        _0x5baaf8.extend(_0x57e60b, _0xc60545.prototype, _0x2b0c45);
        _0x5baaf8.extend(_0x57e60b, _0x2b0c45);
        _0x57e60b.create = function (_0x21ee6d) {
          return _0xe80c1d(_0xba3e98(_0x35cff6, _0x21ee6d));
        };
        return _0x57e60b;
      }(_0x13c8f2(0x1b4b));
      _0x1d89a9.Axios = _0xc60545;
      _0x1d89a9.Cancel = _0x13c8f2(0x788);
      _0x1d89a9.CancelToken = _0x13c8f2(0xc77);
      _0x1d89a9.isCancel = _0x13c8f2(0xf18);
      _0x1d89a9.VERSION = _0x13c8f2(0x25a9).version;
      _0x1d89a9.all = function (_0x3def91) {
        return Promise.all(_0x3def91);
      };
      _0x1d89a9.spread = _0x13c8f2(0x1f2c);
      _0x1d89a9.isAxiosError = _0x13c8f2(0x139b);
      _0x244c15.exports = _0x1d89a9;
      _0x244c15.exports["default"] = _0x1d89a9;
    },
    0x1ff0: (_0x33baee, _0x3ef116, _0xe77c87) => {
      var _0x1e7e9f = Buffer && _0xe77c87(0x233f).inspect.custom || 'inspect';
      function _0x172385(_0x4190c0) {
        if (!(this instanceof _0x172385)) {
          return new _0x172385(_0x4190c0);
        }
        this._bsontype = "Symbol";
        this.value = _0x4190c0;
      }
      _0x172385.prototype.valueOf = function () {
        return this.value;
      };
      _0x172385.prototype.toString = function () {
        return this.value;
      };
      _0x172385.prototype[_0x1e7e9f] = function () {
        return this.value;
      };
      _0x172385.prototype.toJSON = function () {
        return this.value;
      };
      _0x33baee.exports = _0x172385;
      _0x33baee.exports.Symbol = _0x172385;
    },
    0x2028: function (_0x46b307, _0x971f43, _0x4a39c8) {
      'use strict';

      var _0x9ceaf3;
      var _0x5a9b0a = this && this.__createBinding || (Object.create ? function (_0x193b9e, _0x53e0b0, _0x2df3e8, _0x52e939) {
        if (undefined === _0x52e939) {
          _0x52e939 = _0x2df3e8;
        }
        var _0x154779 = Object.getOwnPropertyDescriptor(_0x53e0b0, _0x2df3e8);
        if (!(_0x154779 && !("get" in _0x154779 ? !_0x53e0b0.__esModule : _0x154779.writable || _0x154779.configurable))) {
          _0x154779 = {
            'enumerable': true,
            'get': function () {
              return _0x53e0b0[_0x2df3e8];
            }
          };
        }
        Object.defineProperty(_0x193b9e, _0x52e939, _0x154779);
      } : function (_0x5bde67, _0x31a611, _0x45c456, _0x32b8c8) {
        if (undefined === _0x32b8c8) {
          _0x32b8c8 = _0x45c456;
        }
        _0x5bde67[_0x32b8c8] = _0x31a611[_0x45c456];
      });
      var _0x2ef5d6 = this && this.__setModuleDefault || (Object.create ? function (_0x378efa, _0x5b2158) {
        Object.defineProperty(_0x378efa, "default", {
          'enumerable': true,
          'value': _0x5b2158
        });
      } : function (_0x1fde90, _0x3d9bd9) {
        _0x1fde90["default"] = _0x3d9bd9;
      });
      var _0x3d8fe1 = this && this.__importStar || (_0x9ceaf3 = function (_0x390401) {
        _0x9ceaf3 = Object.getOwnPropertyNames || function (_0x459828) {
          var _0x1185bb = [];
          for (var _0x43d91a in _0x459828) if (Object.prototype.hasOwnProperty.call(_0x459828, _0x43d91a)) {
            _0x1185bb[_0x1185bb.length] = _0x43d91a;
          }
          return _0x1185bb;
        };
        return _0x9ceaf3(_0x390401);
      }, function (_0x1a9c2e) {
        if (_0x1a9c2e && _0x1a9c2e.__esModule) {
          return _0x1a9c2e;
        }
        var _0x24632a = {};
        if (null != _0x1a9c2e) {
          var _0xaf70c6 = _0x9ceaf3(_0x1a9c2e);
          for (var _0x11d13d = 0x0; _0x11d13d < _0xaf70c6.length; _0x11d13d++) {
            if ("default" !== _0xaf70c6[_0x11d13d]) {
              _0x5a9b0a(_0x24632a, _0x1a9c2e, _0xaf70c6[_0x11d13d]);
            }
          }
        }
        _0x2ef5d6(_0x24632a, _0x1a9c2e);
        return _0x24632a;
      });
      Object.defineProperty(_0x971f43, '__esModule', {
        'value': true
      });
      _0x971f43.WebviewManager = undefined;
      const _0x36a32e = _0x3d8fe1(_0x4a39c8(0x576));
      const _0x45428d = _0x3d8fe1(_0x4a39c8(0x1b10));
      const _0xe7733 = _0x3d8fe1(_0x4a39c8(0x645));
      const _0x15f84c = _0x4a39c8(0x3f);
      const _0x59b04f = _0x4a39c8(0x1140);
      _0x971f43.WebviewManager = class {
        ["_panel"];
        ["_extensionUri"];
        ["_context"];
        constructor(_0x40c776) {
          this._extensionUri = _0x40c776.extensionUri;
          this._context = _0x40c776;
          _0x59b04f.logger.debug("WebviewManager 实例已创建");
        }
        ["showPoolPage"]() {
          _0x59b04f.logger.debug("显示Aug激活页面");
          if (this._panel) {
            try {
              _0x59b04f.logger.debug("Aug激活面板已存在，重新显示并更新状态");
              this._panel.reveal(_0x36a32e.ViewColumn.One);
              return void this._updateLoginStatus();
            } catch (_0x13091b) {
              _0x59b04f.logger.warn("面板已被处置但引用未清除，将创建新面板", _0x13091b);
              this._panel = undefined;
            }
          }
          _0x59b04f.logger.debug("创建新的Aug激活面板");
          this._panel = _0x36a32e.window.createWebviewPanel("codepoolPool", "Aug激活管理", _0x36a32e.ViewColumn.One, {
            'enableScripts': true,
            'retainContextWhenHidden': true,
            'localResourceRoots': [_0x36a32e.Uri.joinPath(this._extensionUri, "webview")]
          });
          this._panel.webview.html = this._getPoolHtml();
          this._updateLoginStatus();
          _0x59b04f.logger.debug("设置Webview消息处理器");
          this._panel.webview.onDidReceiveMessage(async _0xe756ac => {
            _0x59b04f.logger.debug("收到Webview消息: " + _0xe756ac.command, _0xe756ac);
            switch (_0xe756ac.command) {
              case "login":
                _0x59b04f.logger.info("从Webview收到登录请求");
                await _0x36a32e.commands.executeCommand('augproxy.cardLogin', _0xe756ac.code);
                this._updateLoginStatus();
                break;
              case "logout":
                _0x59b04f.logger.info("从Webview收到登出请求");
                await _0x36a32e.commands.executeCommand("augproxy.logout");
                this._updateLoginStatus();
                break;
              case "error":
                _0x59b04f.logger.error("从Webview收到错误消息: " + _0xe756ac.message);
                _0x36a32e.window.showErrorMessage(_0xe756ac.message);
                break;
              case "openDevTools":
                _0x59b04f.logger.info("从Webview收到打开开发者工具请求");
                this.openDevTools();
            }
          }, undefined, this._context.subscriptions);
          this._panel.onDidDispose(() => {
            _0x59b04f.logger.debug("Aug激活面板被关闭，执行清理操作");
            try {
              if (this._panel && this._panel.webview) {
                this._panel.webview.postMessage({
                  'command': "cleanup"
                });
              }
            } catch (_0x57531c) {
              _0x59b04f.logger.warn('清理面板时出错', _0x57531c);
            } finally {
              this._panel = undefined;
            }
          }, null, this._context.subscriptions);
        }
        ["openDevTools"]() {
          _0x59b04f.logger.debug("尝试打开Webview开发者工具");
          if (this._panel) {
            _0x59b04f.logger.info("打开Webview开发者工具");
            _0x36a32e.commands.executeCommand("workbench.action.webview.openDeveloperTools");
          } else {
            _0x59b04f.logger.warn("无法打开开发者工具：Aug激活面板未打开");
            _0x36a32e.window.showWarningMessage("请先打开Aug激活页面");
          }
        }
        ["_getPoolHtml"]() {
          _0x59b04f.logger.debug('获取Aug激活页面HTML内容');
          const _0x30ca9f = _0x45428d.join(this._extensionUri.fsPath, "webview", "pool.html");
          _0x59b04f.logger.debug("HTML文件路径: " + _0x30ca9f);
          return _0xe7733.readFileSync(_0x30ca9f, "utf8");
        }
        async ["_updateLoginStatus"]() {
          _0x59b04f.logger.debug("更新用户登录状态");
          if (this._panel) {
            _0x59b04f.logger.debug("向Webview发送更新状态消息");
            this._panel.webview.postMessage({
              'command': "updateLoginStatus",
              'user': _0x15f84c.shareLocal.user,
              'vipInfo': _0x15f84c.shareLocal.user?.["vip"] || null
            });
          }
        }
      };
    },
    0x20f9: _0x40befb => {
      function _0x259338(_0x3ec453, _0x192cf2) {
        if (!(this instanceof _0x259338)) {
          return new _0x259338();
        }
        this._bsontype = "BSONRegExp";
        this.pattern = _0x3ec453 || '';
        this.options = _0x192cf2 || '';
        for (var _0xd40800 = 0x0; _0xd40800 < this.options.length; _0xd40800++) {
          if ('i' !== this.options[_0xd40800] && 'm' !== this.options[_0xd40800] && 'x' !== this.options[_0xd40800] && 'l' !== this.options[_0xd40800] && 's' !== this.options[_0xd40800] && 'u' !== this.options[_0xd40800]) {
            throw new Error("the regular expression options [" + this.options[_0xd40800] + "] is not supported");
          }
        }
      }
      _0x40befb.exports = _0x259338;
      _0x40befb.exports.BSONRegExp = _0x259338;
    },
    0x21a3: _0x2f7284 => {
      'use strict';

      _0x2f7284.exports = require("http");
    },
    0x21a9: (_0x5d89ca, _0x40f622, _0x554c10) => {
      'use strict';

      _0x5d89ca.exports = {
        'moveSync': _0x554c10(0x1f26)
      };
    },
    0x2221: (_0x4b3d8e, _0x3b0eb6, _0x271683) => {
      'use strict';

      const _0x1beeec = _0x271683(0x4d4).S;
      const _0x441c53 = _0x271683(0xe97);
      const _0x16f8e8 = ["access", "appendFile", "chmod", 'chown', "close", 'copyFile', "fchmod", "fchown", "fdatasync", 'fstat', "fsync", 'ftruncate', "futimes", "lchown", "lchmod", "link", "lstat", "mkdir", "mkdtemp", "open", 'readFile', "readdir", "readlink", "realpath", "rename", "rmdir", "stat", "symlink", "truncate", "unlink", "utimes", "writeFile"].filter(_0x593968 => "function" == typeof _0x441c53[_0x593968]);
      Object.keys(_0x441c53).forEach(_0x306c9b => {
        if ("promises" !== _0x306c9b) {
          _0x3b0eb6[_0x306c9b] = _0x441c53[_0x306c9b];
        }
      });
      _0x16f8e8.forEach(_0x693d53 => {
        _0x3b0eb6[_0x693d53] = _0x1beeec(_0x441c53[_0x693d53]);
      });
      _0x3b0eb6.exists = function (_0x44a73e, _0x231629) {
        return "function" == typeof _0x231629 ? _0x441c53.exists(_0x44a73e, _0x231629) : new Promise(_0x5a7fc3 => _0x441c53.exists(_0x44a73e, _0x5a7fc3));
      };
      _0x3b0eb6.read = function (_0x22fa11, _0x1e6f45, _0x42b6e3, _0x25e59a, _0x269714, _0x31a5d1) {
        return "function" == typeof _0x31a5d1 ? _0x441c53.read(_0x22fa11, _0x1e6f45, _0x42b6e3, _0x25e59a, _0x269714, _0x31a5d1) : new Promise((_0x83bbe9, _0x289bf6) => {
          _0x441c53.read(_0x22fa11, _0x1e6f45, _0x42b6e3, _0x25e59a, _0x269714, (_0x1cfd1f, _0x249df6, _0x1c39fa) => {
            if (_0x1cfd1f) {
              return _0x289bf6(_0x1cfd1f);
            }
            _0x83bbe9({
              'bytesRead': _0x249df6,
              'buffer': _0x1c39fa
            });
          });
        });
      };
      _0x3b0eb6.write = function (_0x409af1, _0x58acdc, ..._0x32fcab) {
        return "function" == typeof _0x32fcab[_0x32fcab.length - 0x1] ? _0x441c53.write(_0x409af1, _0x58acdc, ..._0x32fcab) : new Promise((_0x182ac3, _0x396b53) => {
          _0x441c53.write(_0x409af1, _0x58acdc, ..._0x32fcab, (_0x34dac9, _0x4365a0, _0x130cbb) => {
            if (_0x34dac9) {
              return _0x396b53(_0x34dac9);
            }
            _0x182ac3({
              'bytesWritten': _0x4365a0,
              'buffer': _0x130cbb
            });
          });
        });
      };
      if ("function" == typeof _0x441c53.realpath.native) {
        _0x3b0eb6.realpath.native = _0x1beeec(_0x441c53.realpath.native);
      }
    },
    0x2334: _0x27b0df => {
      'use strict';

      _0x27b0df.exports = function (_0x1c84c3, _0x388b25) {
        return function () {
          var _0x21bbad = new Array(arguments.length);
          for (var _0x164f23 = 0x0; _0x164f23 < _0x21bbad.length; _0x164f23++) {
            _0x21bbad[_0x164f23] = arguments[_0x164f23];
          }
          return _0x1c84c3.apply(_0x388b25, _0x21bbad);
        };
      };
    },
    0x233f: _0x3402e2 => {
      'use strict';

      _0x3402e2.exports = require("util");
    },
    0x2392: (_0x1c8d21, _0x489aa9, _0x1177f6) => {
      'use strict';

      var _0xef51db = _0x1177f6(0x252c);
      function _0x28df40(_0x38e79d) {
        return encodeURIComponent(_0x38e79d).replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',').replace(/%20/g, '+').replace(/%5B/gi, '[').replace(/%5D/gi, ']');
      }
      _0x1c8d21.exports = function (_0x5f297d, _0x267759, _0xe2499e) {
        if (!_0x267759) {
          return _0x5f297d;
        }
        var _0x335825;
        if (_0xe2499e) {
          _0x335825 = _0xe2499e(_0x267759);
        } else {
          if (_0xef51db.isURLSearchParams(_0x267759)) {
            _0x335825 = _0x267759.toString();
          } else {
            var _0x374dcd = [];
            _0xef51db.forEach(_0x267759, function (_0x5f02b8, _0x13afd9) {
              if (null != _0x5f02b8) {
                if (_0xef51db.isArray(_0x5f02b8)) {
                  _0x13afd9 += '[]';
                } else {
                  _0x5f02b8 = [_0x5f02b8];
                }
                _0xef51db.forEach(_0x5f02b8, function (_0x377f86) {
                  if (_0xef51db.isDate(_0x377f86)) {
                    _0x377f86 = _0x377f86.toISOString();
                  } else if (_0xef51db.isObject(_0x377f86)) {
                    _0x377f86 = JSON.stringify(_0x377f86);
                  }
                  _0x374dcd.push(encodeURIComponent(_0x13afd9).replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',').replace(/%20/g, '+').replace(/%5B/gi, '[').replace(/%5D/gi, ']') + '=' + encodeURIComponent(_0x377f86).replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',').replace(/%20/g, '+').replace(/%5B/gi, '[').replace(/%5D/gi, ']'));
                });
              }
            });
            _0x335825 = _0x374dcd.join('&');
          }
        }
        if (_0x335825) {
          var _0x452d0a = _0x5f297d.indexOf('#');
          if (-0x1 !== _0x452d0a) {
            _0x5f297d = _0x5f297d.slice(0x0, _0x452d0a);
          }
          _0x5f297d += (-0x1 === _0x5f297d.indexOf('?') ? '?' : '&') + _0x335825;
        }
        return _0x5f297d;
      };
    },
    0x23b1: _0x343f1d => {
      'use strict';

      _0x343f1d.exports = function (_0x19d061) {
        return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(_0x19d061);
      };
    },
    0x23b4: _0x395d94 => {
      'use strict';

      _0x395d94.exports = require("constants");
    },
    0x2448: (_0xec2a7f, _0x578d63, _0x312069) => {
      'use strict';

      const _0x108568 = _0x312069(0x4d4).z;
      const _0x3b5dc3 = _0x312069(0x2221);
      _0xec2a7f.exports = {
        'pathExists': _0x108568(function (_0x259626) {
          return _0x3b5dc3.access(_0x259626).then(() => true)["catch"](() => false);
        }),
        'pathExistsSync': _0x3b5dc3.existsSync
      };
    },
    0x252c: (_0xf92472, _0x48004c, _0x4d1df5) => {
      'use strict';

      var _0x104540 = _0x4d1df5(0x2334);
      var _0x374438 = Object.prototype.toString;
      function _0x37f35a(_0x3b83d7) {
        return "[object Array]" === _0x374438.call(_0x3b83d7);
      }
      function _0xe4fc7d(_0x4765f6) {
        return undefined === _0x4765f6;
      }
      function _0x20c74f(_0x23378f) {
        return null !== _0x23378f && "object" == typeof _0x23378f;
      }
      function _0x315a4c(_0x581372) {
        if ("[object Object]" !== _0x374438.call(_0x581372)) {
          return false;
        }
        var _0x55f007 = Object.getPrototypeOf(_0x581372);
        return null === _0x55f007 || _0x55f007 === Object.prototype;
      }
      function _0x2535be(_0x3d3793) {
        return "[object Function]" === _0x374438.call(_0x3d3793);
      }
      function _0x39118c(_0x9c360d, _0x242241) {
        if (null != _0x9c360d) {
          if ("object" != typeof _0x9c360d) {
            _0x9c360d = [_0x9c360d];
          }
          if ("[object Array]" === _0x374438.call(_0x9c360d)) {
            var _0x48bf15 = 0x0;
            for (var _0x4b35ef = _0x9c360d.length; _0x48bf15 < _0x4b35ef; _0x48bf15++) {
              _0x242241.call(null, _0x9c360d[_0x48bf15], _0x48bf15, _0x9c360d);
            }
          } else {
            for (var _0x2e5ea4 in _0x9c360d) if (Object.prototype.hasOwnProperty.call(_0x9c360d, _0x2e5ea4)) {
              _0x242241.call(null, _0x9c360d[_0x2e5ea4], _0x2e5ea4, _0x9c360d);
            }
          }
        }
      }
      _0xf92472.exports = {
        'isArray': _0x37f35a,
        'isArrayBuffer': function (_0x309e4e) {
          return "[object ArrayBuffer]" === _0x374438.call(_0x309e4e);
        },
        'isBuffer': function (_0x2ec23d) {
          return null !== _0x2ec23d && !(undefined === _0x2ec23d) && null !== _0x2ec23d.constructor && !(undefined === _0x2ec23d.constructor) && "function" == typeof _0x2ec23d.constructor.isBuffer && _0x2ec23d.constructor.isBuffer(_0x2ec23d);
        },
        'isFormData': function (_0x27315f) {
          return "undefined" != typeof FormData && _0x27315f instanceof FormData;
        },
        'isArrayBufferView': function (_0x62d1f3) {
          return "undefined" != typeof ArrayBuffer && ArrayBuffer.isView ? ArrayBuffer.isView(_0x62d1f3) : _0x62d1f3 && _0x62d1f3.buffer && _0x62d1f3.buffer instanceof ArrayBuffer;
        },
        'isString': function (_0x3f6f9b) {
          return "string" == typeof _0x3f6f9b;
        },
        'isNumber': function (_0x53a2b3) {
          return 'number' == typeof _0x53a2b3;
        },
        'isObject': _0x20c74f,
        'isPlainObject': _0x315a4c,
        'isUndefined': _0xe4fc7d,
        'isDate': function (_0x3ece12) {
          return "[object Date]" === _0x374438.call(_0x3ece12);
        },
        'isFile': function (_0x258d29) {
          return "[object File]" === _0x374438.call(_0x258d29);
        },
        'isBlob': function (_0xe5b2e6) {
          return "[object Blob]" === _0x374438.call(_0xe5b2e6);
        },
        'isFunction': _0x2535be,
        'isStream': function (_0x160bef) {
          return null !== _0x160bef && "object" == typeof _0x160bef && "[object Function]" === _0x374438.call(_0x160bef.pipe);
        },
        'isURLSearchParams': function (_0x30e6aa) {
          return "undefined" != typeof URLSearchParams && _0x30e6aa instanceof URLSearchParams;
        },
        'isStandardBrowserEnv': function () {
          return ("undefined" == typeof navigator || "ReactNative" !== navigator.product && "NativeScript" !== navigator.product && 'NS' !== navigator.product) && "undefined" != typeof window && "undefined" != typeof document;
        },
        'forEach': _0x39118c,
        'merge': function _0xb29fe2() {
          var _0x18f415 = {};
          function _0x5e2040(_0x3a5a7f, _0x39e7a0) {
            if (_0x315a4c(_0x18f415[_0x39e7a0]) && _0x315a4c(_0x3a5a7f)) {
              _0x18f415[_0x39e7a0] = _0xb29fe2(_0x18f415[_0x39e7a0], _0x3a5a7f);
            } else if (_0x315a4c(_0x3a5a7f)) {
              _0x18f415[_0x39e7a0] = _0xb29fe2({}, _0x3a5a7f);
            } else if ("[object Array]" === _0x374438.call(_0x3a5a7f)) {
              _0x18f415[_0x39e7a0] = _0x3a5a7f.slice();
            } else {
              _0x18f415[_0x39e7a0] = _0x3a5a7f;
            }
          }
          var _0xb20720 = 0x0;
          for (var _0x240ba5 = arguments.length; _0xb20720 < _0x240ba5; _0xb20720++) {
            _0x39118c(arguments[_0xb20720], _0x5e2040);
          }
          return _0x18f415;
        },
        'extend': function (_0x4584ca, _0x53c1a1, _0xceabe9) {
          _0x39118c(_0x53c1a1, function (_0x4348ff, _0x25cf73) {
            _0x4584ca[_0x25cf73] = _0xceabe9 && "function" == typeof _0x4348ff ? _0x104540(_0x4348ff, _0xceabe9) : _0x4348ff;
          });
          return _0x4584ca;
        },
        'trim': function (_0x324df5) {
          return _0x324df5.trim ? _0x324df5.trim() : _0x324df5.replace(/^\s+|\s+$/g, '');
        },
        'stripBOM': function (_0x4f113d) {
          if (0xfeff === _0x4f113d.charCodeAt(0x0)) {
            _0x4f113d = _0x4f113d.slice(0x1);
          }
          return _0x4f113d;
        }
      };
    },
    0x258f: (_0x3a59ee, _0x57eb95, _0x412e9f) => {
      'use strict';

      var _0x555edf = _0x412e9f(0x23b1);
      var _0x318437 = _0x412e9f(0x1248);
      _0x3a59ee.exports = function (_0x2f9e72, _0x5cca9c) {
        return _0x2f9e72 && !_0x555edf(_0x5cca9c) ? _0x318437(_0x2f9e72, _0x5cca9c) : _0x5cca9c;
      };
    },
    0x2590: (_0x5617fe, _0x472e7b, _0x1edef1) => {
      'use strict';

      const _0x325f0b = _0x1edef1(0x4d4).S;
      const _0x3898d9 = _0x1edef1(0x1b10);
      const _0x2e8384 = _0x1edef1(0xe97);
      const _0x5d96a0 = _0x1edef1(0xed6);
      const _0x3754bc = _0x1edef1(0x2448).pathExists;
      _0x5617fe.exports = {
        'createFile': _0x325f0b(function (_0xa6de59, _0x1b1268) {
          function _0x45d0df() {
            _0x2e8384.writeFile(_0xa6de59, '', _0x698e61 => {
              if (_0x698e61) {
                return _0x1b1268(_0x698e61);
              }
              _0x1b1268();
            });
          }
          _0x2e8384.stat(_0xa6de59, (_0x17b245, _0x1089c2) => {
            if (!_0x17b245 && _0x1089c2.isFile()) {
              return _0x1b1268();
            }
            const _0x10b881 = _0x3898d9.dirname(_0xa6de59);
            _0x3754bc(_0x10b881, (_0x1e206b, _0x6dd53) => _0x1e206b ? _0x1b1268(_0x1e206b) : _0x6dd53 ? _0x45d0df() : void _0x5d96a0.mkdirs(_0x10b881, _0x4770c2 => {
              if (_0x4770c2) {
                return _0x1b1268(_0x4770c2);
              }
              _0x45d0df();
            }));
          });
        }),
        'createFileSync': function (_0x53e148) {
          let _0x5bfb14;
          try {
            _0x5bfb14 = _0x2e8384.statSync(_0x53e148);
          } catch (_0x4a229f) {}
          if (_0x5bfb14 && _0x5bfb14.isFile()) {
            return;
          }
          const _0x4d9784 = _0x3898d9.dirname(_0x53e148);
          if (!_0x2e8384.existsSync(_0x4d9784)) {
            _0x5d96a0.mkdirsSync(_0x4d9784);
          }
          _0x2e8384.writeFileSync(_0x53e148, '');
        }
      };
    },
    0x25a9: _0xbf4941 => {
      _0xbf4941.exports = {
        'version': '0.24.0'
      };
    },
    0x26a8: _0x3e5ba4 => {
      'use strict';

      _0x3e5ba4.exports = require('fs');
    }
  };
  var _0x40ffa9 = {};
  function _0x17c6d4(_0x244ff1) {
    var _0x59cf5d = _0x40ffa9[_0x244ff1];
    if (undefined !== _0x59cf5d) {
      return _0x59cf5d.exports;
    }
    var _0x1a7723 = _0x40ffa9[_0x244ff1] = {
      'exports': {}
    };
    _0x2acbc8[_0x244ff1].call(_0x1a7723.exports, _0x1a7723, _0x1a7723.exports, _0x17c6d4);
    return _0x1a7723.exports;
  }
  _0x17c6d4.d = (_0xcf6a14, _0x2e65d7) => {
    for (var _0x25539c in _0x2e65d7) if (_0x17c6d4.o(_0x2e65d7, _0x25539c) && !_0x17c6d4.o(_0xcf6a14, _0x25539c)) {
      Object.defineProperty(_0xcf6a14, _0x25539c, {
        'enumerable': true,
        'get': _0x2e65d7[_0x25539c]
      });
    }
  };
  _0x17c6d4.o = (_0x8f4a28, _0x35343c) => Object.prototype.hasOwnProperty.call(_0x8f4a28, _0x35343c);
  _0x17c6d4.r = _0x5b663f => {
    if ("undefined" != typeof Symbol && Symbol.toStringTag) {
      Object.defineProperty(_0x5b663f, Symbol.toStringTag, {
        'value': "Module"
      });
    }
    Object.defineProperty(_0x5b663f, "__esModule", {
      'value': true
    });
  };
  var _0x254def = _0x17c6d4(0x10a9);
  module.exports = _0x254def;
})();
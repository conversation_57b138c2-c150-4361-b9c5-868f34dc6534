{"name": "augment-switcher", "displayName": "Augment Login Switcher", "description": "Prompt for a token and switch the Augment extension login (by invoking or injecting vscode-augment.directLogin).", "version": "0.1.2", "publisher": "local-dev", "engines": {"vscode": "^1.85.0"}, "categories": ["Other"], "activationEvents": ["onCommand:augmentSwitcher.switchLogin", "onView:augmentSwitcher.view"], "main": "./extension.js", "contributes": {"commands": [{"command": "augmentSwitcher.switchLogin", "title": "Augment: 输入 Token 切换登录"}], "viewsContainers": {"activitybar": [{"id": "augmentSwitcher", "title": "Augment", "icon": "media/icon.svg"}]}, "views": {"augmentSwitcher": [{"id": "augmentSwitcher.view", "name": "登录切换", "type": "webview"}]}, "configuration": {"title": "Augment Login Switcher", "properties": {"augmentSwitcher.host": {"type": "string", "default": "", "markdownDescription": "Augment 后端 Host（例如 https://api.example.com/ 结尾带斜杠）。界面中为必填项。", "scope": "application"}}}}, "scripts": {"vscode:prepublish": "echo Skipping build for plain JS extension"}, "license": "MIT"}
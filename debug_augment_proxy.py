#!/usr/bin/env python3
"""
Debug script specifically for augment-proxy product
"""

import requests
import json
from typing import Dict, Any, Optional
try:
    import bson
except ImportError:
    print("Warning: bson module not found. Install with: pip install pymongo")
    bson = None


class DebugAugmentProxy:
    def __init__(self, base_url: str = "https://deepl.micosoft.icu"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/secret',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.auth_token: Optional[str] = None
        
    def _encode_data(self, data: Dict[str, Any]) -> bytes:
        """Encode data using BSON and XOR with 0x37"""
        if not data:
            return b''
        
        if not bson:
            # Fallback to JSON for testing
            print("Warning: Using JSON fallback instead of BSON")
            json_data = json.dumps(data).encode('utf-8')
            encoded = bytearray()
            for byte in json_data:
                encoded.append(byte ^ 0x37)
            return bytes(encoded)
        
        # Serialize to BSON
        bson_data = bson.encode(data)
        print(f"BSON encoded data length: {len(bson_data)}")
        print(f"BSON data (first 50 bytes): {bson_data[:50]}")
        
        # XOR each byte with 0x37
        encoded = bytearray()
        for byte in bson_data:
            encoded.append(byte ^ 0x37)
            
        print(f"XOR encoded data (first 50 bytes): {bytes(encoded)[:50]}")
        return bytes(encoded)
    
    def _decode_response(self, response_data: bytes) -> Dict[str, Any]:
        """Decode response by XOR with 0x37 and BSON deserialize"""
        if not response_data:
            return {}
            
        print(f"Raw response length: {len(response_data)}")
        print(f"Raw response (first 50 bytes): {response_data[:50]}")
            
        # XOR each byte with 0x37
        decoded = bytearray()
        for byte in response_data:
            decoded.append(byte ^ 0x37)
            
        print(f"XOR decoded (first 50 bytes): {bytes(decoded)[:50]}")
            
        if not bson:
            # Fallback to JSON for testing
            try:
                json_str = bytes(decoded).decode('utf-8')
                print(f"JSON string: {json_str[:200]}...")
                return json.loads(json_str)
            except Exception as e:
                print(f"JSON decode error: {e}")
                return {}
            
        # Deserialize from BSON
        try:
            result = bson.decode(bytes(decoded))
            print(f"BSON decoded result: {result}")
            return result
        except Exception as e:
            print(f"BSON decode error: {e}")
            return {}
    
    def _api_post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make API POST request with custom encoding"""
        url = f"{self.base_url}{endpoint}"
        print(f"\n=== API Request: {endpoint} ===")
        print(f"URL: {url}")
        print(f"Data: {data}")
        
        # Prepare headers
        headers = self.session.headers.copy()
        if self.auth_token:
            headers['X-Auth-Token'] = self.auth_token
            print(f"Auth token: {self.auth_token[:20]}...")
            
        # Encode request data
        encoded_data = self._encode_data(data or {})
        
        try:
            response = self.session.post(url, data=encoded_data, headers=headers, timeout=30)
            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            
            response.raise_for_status()
            
            # Decode response
            result = self._decode_response(response.content)
            
            print(f"Decoded response: {result}")
            
            # Check response code
            if result.get('code') == 0:
                return result.get('data', {})
            else:
                error_msg = result.get('msg', 'Unknown error')
                raise Exception(f"API Error: {error_msg}")
                
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            raise Exception(f"Request failed: {e}")
    
    def card_login(self, card_code: str) -> Dict[str, Any]:
        """Login using card code"""
        print(f"Logging in with card: {card_code}")
        
        result = self._api_post("/api/users/card-login", {
            'card': card_code,
            'agent': 'main'
        })
        
        # Extract user info and token
        user_info = {
            'id': result.get('id'),
            'token': result.get('token'),
            'vip': result.get('vip')
        }
        
        # Store auth token for subsequent requests
        self.auth_token = user_info['token']
        
        print(f"Login successful. User ID: {user_info['id']}")
        return user_info
    
    def get_vips(self) -> Dict[str, Any]:
        """Get user VIP information"""
        return self._api_post("/api/users/vips")
    
    def get_pool_list(self, product: str) -> Dict[str, Any]:
        """Get available pools for a product"""
        return self._api_post("/api/pools/gain_list", {
            'product': product
        })
    
    def pool_gain(self, product: str, pool_id: str) -> Dict[str, Any]:
        """Get token, host, and email for switching to an account"""
        return self._api_post("/api/pools/gain", {
            'product': product,
            'pool_id': pool_id,
            'version': 21
        })


def main():
    """Debug augment-proxy specifically"""
    debug = DebugAugmentProxy()
    
    try:
        # Step 1: Login
        card_code = input("Enter your card code: ").strip()
        user_info = debug.card_login(card_code)
        
        # Step 2: Get VIP info
        print("\n=== Getting VIP Info ===")
        vips_info = debug.get_vips()
        print(f"VIP info: {json.dumps(vips_info, indent=2)}")
        
        # Step 3: Test augment-proxy specifically
        print("\n=== Testing augment-proxy pools ===")
        pools = debug.get_pool_list("augment-proxy")
        print(f"Pools for augment-proxy: {json.dumps(pools, indent=2)}")
        
        # Step 4: Check the structure of pools response
        print(f"Pools response type: {type(pools)}")
        print(f"Pools response: {pools}")

        # Handle different response structures
        pool_list = None
        if isinstance(pools, list):
            pool_list = pools
        elif isinstance(pools, dict) and 'list' in pools:
            pool_list = pools['list']
        elif isinstance(pools, dict) and pools:
            # Maybe the pools are directly in the dict
            pool_list = [pools] if 'id' in pools else None

        if pool_list and len(pool_list) > 0:
            pool_id = pool_list[0].get('id') if isinstance(pool_list[0], dict) else pool_list[0]
            print(f"\n=== Gaining access to pool: {pool_id} ===")
            token_info = debug.pool_gain("augment-proxy", pool_id)

            print("\n=== AUGMENT-PROXY TOKEN INFO ===")
            print(f"Token: {token_info.get('token')}")
            print(f"Host: {token_info.get('host')}")
            print(f"Email: {token_info.get('email')}")
            print(f"Full URL: https://{token_info.get('host')}/")
        else:
            print("No pools found for augment-proxy")
            print("This might be normal - pools could be:")
            print("1. Not available for your account")
            print("2. Require specific conditions (score, timing, etc.)")
            print("3. Managed through a different system")

            # Try with a test pool_id to see what happens
            print("\n=== Testing with dummy pool_id ===")
            try:
                token_info = debug.pool_gain("augment-proxy", "test_pool_id")
                print("Unexpected success with dummy pool_id!")
                print(f"Token info: {token_info}")
            except Exception as e:
                print(f"Expected error with dummy pool_id: {e}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

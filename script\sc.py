#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import sys
import requests
from bson import BSON

BASE_URL = "https://deepl.micosoft.icu"
SECRET_CT = "application/secret"
XOR_KEY = 0x37

def xor_bytes(buf: bytes, key: int = XOR_KEY) -> bytes:
    return bytes((b ^ key) for b in buf)

def encode_secret(payload: dict) -> bytes:
    # BSON -> XOR
    raw = BSON.encode(payload)
    return xor_bytes(raw)

def try_decode_secret(resp: requests.Response):
    # 如果是 application/secret，则先 XOR 再 BSON 解码
    ct = resp.headers.get("Content-Type", "")
    if ct.split(";")[0].strip().lower() == SECRET_CT:
        raw = xor_bytes(resp.content)
        return BSON(raw).decode()
    # 否则尝试 JSON，其次文本
    try:
        return resp.json()
    except Exception:
        return resp.text

def api_post(path: str, data: dict | None, token: str | None = None):
    url = f"{BASE_URL}{path}"
    headers = {"Content-Type": SECRET_CT}
    if token:
        headers["X-Auth-Token"] = token
    body = encode_secret(data or {})
    resp = requests.post(url, data=body, headers=headers, timeout=30)
    # 不强制 raise_for_status，后端常用 code/msg 包装
    decoded = try_decode_secret(resp)
    return decoded

def ensure_ok(envelope):
    """
    后端返回一般为 { code, data, msg }.
    code == 0 代表成功，返回 data；否则抛错。
    如不是此结构，原样返回。
    """
    if isinstance(envelope, dict) and "code" in envelope:
        if envelope.get("code") == 0:
            return envelope.get("data")
        msg = envelope.get("msg") or envelope
        raise RuntimeError(f"API error: {msg}")
    return envelope

def card_login(card: str) -> dict:
    """
    POST /api/users/card-login
    请求体: { card, agent: "main" }
    返回: { id, token, vip }（在 data 中）
    """
    env = api_post("/api/users/card-login", {"card": card, "agent": "main"})
    return ensure_ok(env)

def pools_gain_list(token: str, product: str):
    """
    POST /api/pools/gain_list
    请求体: { product }
    """
    env = api_post("/api/pools/gain_list", {"product": product}, token=token)
    return ensure_ok(env)

def pools_gain(token: str, product: str, pool_id: str, version: int = 21):
    """
    POST /api/pools/gain
    请求体: { product, pool_id, version: 21 }
    """
    env = api_post("/api/pools/gain", {"product": product, "pool_id": pool_id, "version": version}, token=token)
    return ensure_ok(env)

def main():
    parser = argparse.ArgumentParser(description="Call /api/pools/gain_list and /api/pools/gain using card-login token")
    parser.add_argument("--card", required=True, help="Card string for /api/users/card-login")
    parser.add_argument("--product", default="augment", help='Product name (e.g. "augment" or "windsurf"). Default: augment')
    parser.add_argument("--pool-id", help="Pool ID to call /api/pools/gain. If omitted, only lists gain_list.")
    parser.add_argument("--version", type=int, default=21, help="Version for /api/pools/gain. Default: 21")
    args = parser.parse_args()

    try:
        print("Logging in with card...")
        login = card_login(args.card)
        token = login.get("token")
        if not token:
            raise RuntimeError(f"Login succeeded but token missing: {login}")

        print(f"Login success. user_id={login.get('id')}, vip={login.get('vip')}, token=****")

        print(f"Fetching gain_list for product={args.product} ...")
        gl = pools_gain_list(token, args.product)
        print("gain_list response:")
        print(gl)

        if args.pool_id:
            print(f"Fetching gain for product={args.product}, pool_id={args.pool_id}, version={args.version} ...")
            gain = pools_gain(token, args.product, args.pool_id, version=args.version)
            print("gain response:")
            print(gain)

        return 0
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        return 1

if __name__ == "__main__":
    sys.exit(main())
{"name": "augproxy", "displayName": "Augment激活", "description": "Augment激活, 一键激活, 无需换号, 无需代理", "version": "1.0.2", "icon": "resources/icon.png", "engines": {"vscode": "^1.23.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "augproxy.openPoolPage", "title": "AugProxy: 打开Aug激活管理页面"}, {"command": "augproxy.showLogs", "title": "AugProxy: 显示日志面板"}, {"command": "augproxy.changeAugment", "title": "AugProxy: augment登录"}, {"command": "augproxy.changeWindsurf", "title": "AugProxy: windsurf登录"}], "configuration": {"title": "AugProxy", "properties": {"augproxy.logLevel": {"type": "string", "default": "info", "enum": ["debug", "info", "warn", "error"], "enumDescriptions": ["详细的调试信息", "一般信息", "警告信息", "错误信息"], "description": "设置日志级别"}}}}, "scripts": {"vscode:prepublish": "yarn run package", "compile": "webpack", "watch": "webpack --watch", "package": "cross-env NODE_ENV=production webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "yarn run compile-tests && yarn run compile && yarn run lint", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/bson": "^1.0.11", "@types/fs-extra": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/vscode": "^1.23.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "cross-env": "^7.0.3", "eslint": "^9.25.1", "javascript-obfuscator": "^4.1.1", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.99.7", "webpack-cli": "^6.0.1", "webpack-obfuscator": "^3.5.1"}, "dependencies": {"axios": "^0.24.0", "bson": "^1.1.6", "fs-extra": "8.1"}, "repository": {"type": "git", "url": "git+https://github.com/keg1255/windsurf-pool.git"}, "keywords": ["Augment激活"], "author": "keg1255", "license": "ISC", "bugs": {"url": "https://github.com/keg1255/windsurf-pool/issues"}, "homepage": "https://github.com/keg1255/windsurf-pool#readme"}
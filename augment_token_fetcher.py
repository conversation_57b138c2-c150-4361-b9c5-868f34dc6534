#!/usr/bin/env python3
"""
Python script to fetch Augment token, host, and email from the proxy service.
Based on the reverse-engineered API calls from extension/decode.js
"""

import requests
import json
from typing import Dict, Any, Optional
try:
    import bson
except ImportError:
    print("Warning: bson module not found. Install with: pip install pymongo")
    bson = None


class AugmentTokenFetcher:
    def __init__(self, base_url: str = "https://deepl.micosoft.icu"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/secret',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.auth_token: Optional[str] = None
        
    def _encode_data(self, data: Dict[str, Any]) -> bytes:
        """Encode data using BSON and XOR with 0x37"""
        if not data:
            return b''

        if not bson:
            raise Exception("BSON module not available. Install with: pip install pymongo")

        # Serialize to BSON
        bson_data = bson.encode(data)

        # XOR each byte with 0x37
        encoded = bytearray()
        for byte in bson_data:
            encoded.append(byte ^ 0x37)

        return bytes(encoded)
    
    def _decode_response(self, response_data: bytes) -> Dict[str, Any]:
        """Decode response by XOR with 0x37 and BSON deserialize"""
        if not response_data:
            return {}

        if not bson:
            raise Exception("BSON module not available. Install with: pip install pymongo")

        # XOR each byte with 0x37
        decoded = bytearray()
        for byte in response_data:
            decoded.append(byte ^ 0x37)

        # Deserialize from BSON
        try:
            return bson.decode(bytes(decoded))
        except Exception as e:
            print(f"BSON decode error: {e}")
            print(f"Raw response (first 100 bytes): {response_data[:100]}")
            print(f"Decoded bytes (first 100): {bytes(decoded)[:100]}")
            return {}
    
    def _api_post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make API POST request with custom encoding"""
        url = f"{self.base_url}{endpoint}"
        
        # Prepare headers
        headers = self.session.headers.copy()
        if self.auth_token:
            headers['X-Auth-Token'] = self.auth_token
            
        # Encode request data
        encoded_data = self._encode_data(data or {})
        
        try:
            response = self.session.post(url, data=encoded_data, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Decode response
            result = self._decode_response(response.content)
            
            print(f"API Response for {endpoint}:", result)
            
            # Check response code
            if result.get('code') == 0:
                return result.get('data', {})
            else:
                error_msg = result.get('msg', 'Unknown error')
                raise Exception(f"API Error: {error_msg}")
                
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {e}")
    
    def card_login(self, card_code: str) -> Dict[str, Any]:
        """Login using card code"""
        print(f"Logging in with card: {card_code}")
        
        result = self._api_post("/api/users/card-login", {
            'card': card_code,
            'agent': 'main'
        })
        
        # Extract user info and token
        user_info = {
            'id': result.get('id'),
            'token': result.get('token'),
            'vip': result.get('vip')
        }
        
        # Store auth token for subsequent requests
        self.auth_token = user_info['token']
        
        print(f"Login successful. User ID: {user_info['id']}")
        return user_info
    
    def whoami(self) -> Dict[str, Any]:
        """Get current user info"""
        if not self.auth_token:
            raise Exception("Not authenticated. Please login first.")

        return self._api_post("/api/users/whoami")

    def get_vips(self) -> Dict[str, Any]:
        """Get user VIP information and available products"""
        if not self.auth_token:
            raise Exception("Not authenticated. Please login first.")

        return self._api_post("/api/users/vips")

    def get_pool_list(self, product: str = "augment") -> Dict[str, Any]:
        """Get available pools for a product"""
        if not self.auth_token:
            raise Exception("Not authenticated. Please login first.")

        return self._api_post("/api/pools/gain_list", {
            'product': product
        })
    
    def pool_gain(self, product: str = "augment", pool_id: str = None) -> Dict[str, Any]:
        """
        Get token, host, and email for switching to an account
        
        Args:
            product: Product type (e.g., "augment", "windsurf")
            pool_id: Pool ID to gain from
            
        Returns:
            Dict containing token, host, email
        """
        if not self.auth_token:
            raise Exception("Not authenticated. Please login first.")
            
        if not pool_id:
            # Get available pools first
            pools = self.get_pool_list(product)
            if not pools.get('list'):
                raise Exception(f"No pools available for product: {product}")
            
            # Use the first available pool
            pool_id = pools['list'][0]['id']
            print(f"Using pool ID: {pool_id}")
        
        print(f"Gaining access to {product} pool: {pool_id}")
        
        result = self._api_post("/api/pools/gain", {
            'product': product,
            'pool_id': pool_id,
            'version': 21  # 0x15 in hex
        })
        
        return {
            'token': result.get('token'),
            'host': result.get('host'), 
            'email': result.get('email')
        }


def main():
    """Example usage"""
    fetcher = AugmentTokenFetcher()
    
    try:
        # Step 1: Login with card code
        card_code = input("Enter your card code: ").strip()
        user_info = fetcher.card_login(card_code)
        
        # Step 2: Get user info
        whoami_info = fetcher.whoami()
        print(f"Current user: {whoami_info}")

        # Step 3: Get VIP info to see available products
        vips_info = fetcher.get_vips()
        print(f"VIP info: {vips_info}")

        # Extract available products from VIP list
        available_products = []
        if vips_info.get('list'):
            available_products = list(set([vip.get('product') for vip in vips_info['list'] if vip.get('product')]))
            print(f"Available products: {available_products}")

        # Step 4: Try to get pools for each available product
        for product in available_products:
            print(f"\n--- Checking pools for product: {product} ---")
            try:
                pools = fetcher.get_pool_list(product)
                print(f"Pools for {product}: {pools}")

                # Step 5: If pools available, try to gain access
                if pools.get('list'):
                    print(f"Found {len(pools['list'])} pools for {product}")
                    token_info = fetcher.pool_gain(product)

                    print(f"\n=== {product.upper()} TOKEN INFO ===")
                    print(f"Token: {token_info['token']}")
                    print(f"Host: {token_info['host']}")
                    print(f"Email: {token_info['email']}")
                    print(f"Full URL: https://{token_info['host']}/")

                else:
                    print(f"No pools available for {product}")

            except Exception as e:
                print(f"Error getting pools for {product}: {e}")

        # If no products found, try the default ones
        if not available_products:
            print("\nNo products found in VIP info, trying default products...")
            for product in ["augment", "augment-proxy", "windsurf"]:
                print(f"\n--- Trying default product: {product} ---")
                try:
                    pools = fetcher.get_pool_list(product)
                    print(f"Pools for {product}: {pools}")

                    if pools.get('list'):
                        token_info = fetcher.pool_gain(product)
                        print(f"\n=== {product.upper()} TOKEN INFO ===")
                        print(f"Token: {token_info['token']}")
                        print(f"Host: {token_info['host']}")
                        print(f"Email: {token_info['email']}")
                        print(f"Full URL: https://{token_info['host']}/")
                        break

                except Exception as e:
                    print(f"Error with {product}: {e}")
            
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()

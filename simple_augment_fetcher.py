#!/usr/bin/env python3
"""
Simplified version using requests to understand the API structure.
This version doesn't handle the BSON encoding but shows the API endpoints.
"""

import requests
import json
from typing import Dict, Any, Optional


class SimpleAugmentFetcher:
    def __init__(self, base_url: str = "https://deepl.micosoft.icu"):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token: Optional[str] = None
        
    def test_endpoints(self, card_code: str):
        """
        Test the API endpoints to understand the structure.
        Note: This may not work due to the custom BSON encoding,
        but it shows the expected API calls.
        """
        
        print("=== Testing API Endpoints ===")
        print(f"Base URL: {self.base_url}")
        
        # Test 1: Card Login
        print("\n1. Testing card login...")
        login_data = {
            'card': card_code,
            'agent': 'main'
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/users/card-login",
                json=login_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            print(f"Login response status: {response.status_code}")
            print(f"Login response headers: {dict(response.headers)}")
            print(f"Login response content: {response.content[:200]}...")
            
        except Exception as e:
            print(f"Login request failed: {e}")
        
        # Test 2: VIP Info (requires auth)
        print("\n2. Testing VIP info...")
        try:
            response = self.session.post(
                f"{self.base_url}/api/users/vips",
                json={},
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            print(f"VIP info response status: {response.status_code}")
            print(f"VIP info response: {response.content[:200]}...")

        except Exception as e:
            print(f"VIP info request failed: {e}")

        # Test 3: Pool List (requires auth)
        print("\n3. Testing pool list...")
        for product in ["augment", "augment-proxy", "windsurf"]:
            print(f"\n  Testing product: {product}")
            try:
                response = self.session.post(
                    f"{self.base_url}/api/pools/gain_list",
                    json={'product': product},
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                print(f"  Pool list response status: {response.status_code}")
                print(f"  Pool list response: {response.content[:200]}...")

            except Exception as e:
                print(f"  Pool list request failed: {e}")
        
        # Test 4: Pool Gain (requires auth and pool_id)
        print("\n4. Testing pool gain...")
        try:
            response = self.session.post(
                f"{self.base_url}/api/pools/gain",
                json={
                    'product': 'augment',
                    'pool_id': 'test_pool_id',
                    'version': 21
                },
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            print(f"Pool gain response status: {response.status_code}")
            print(f"Pool gain response: {response.content[:200]}...")

        except Exception as e:
            print(f"Pool gain request failed: {e}")


def analyze_network_traffic():
    """
    Instructions for analyzing network traffic to understand the API better
    """
    print("""
=== How to Analyze the Real API Calls ===

Since the API uses custom BSON encoding with XOR encryption, you can:

1. Use the original extension and monitor network traffic:
   - Open browser dev tools (F12)
   - Go to Network tab
   - Trigger account switching in the extension
   - Look for requests to deepl.micosoft.icu

2. Key API endpoints to monitor:
   - POST /api/users/card-login
   - POST /api/pools/gain_list  
   - POST /api/pools/gain

3. Request format:
   - Content-Type: application/secret
   - Body: BSON-encoded data XORed with 0x37
   - Headers: X-Auth-Token (after login)

4. Response format:
   - Content-Type: application/secret
   - Body: BSON-encoded data XORed with 0x37
   - Structure: {code: 0, data: {...}} for success

5. To decode manually:
   - Copy the request/response body as hex
   - XOR each byte with 0x37
   - Decode the result as BSON
   
Example Python code to decode a captured response:

```python
import bson

def decode_response(hex_data: str) -> dict:
    # Convert hex string to bytes
    data = bytes.fromhex(hex_data)
    
    # XOR with 0x37
    decoded = bytearray()
    for byte in data:
        decoded.append(byte ^ 0x37)
    
    # Decode BSON
    return bson.decode(bytes(decoded))
```
""")


def main():
    print("=== Augment Token Fetcher Analysis ===")
    
    choice = input("Choose option:\n1. Test API endpoints (may fail due to encoding)\n2. Show analysis instructions\nEnter 1 or 2: ").strip()
    
    if choice == "1":
        card_code = input("Enter your card code: ").strip()
        fetcher = SimpleAugmentFetcher()
        fetcher.test_endpoints(card_code)
        
    elif choice == "2":
        analyze_network_traffic()
        
    else:
        print("Invalid choice")


if __name__ == "__main__":
    main()

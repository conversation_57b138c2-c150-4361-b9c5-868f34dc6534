# Augment Token Fetcher

这个项目包含了用 Python 还原从代理服务获取 Augment token、host 和 email 信息的过程。

## 文件说明

- `augment_token_fetcher.py` - 完整版本，包含 BSON 编码/解码
- `simple_augment_fetcher.py` - 简化版本，用于测试和分析
- `requirements.txt` - Python 依赖

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法 1: 使用完整版本

```bash
python augment_token_fetcher.py
```

输入你的卡密，脚本会：
1. 登录获取认证 token
2. 获取可用的池子列表
3. 从池子中获取 Augment 的 token、host、email

### 方法 2: 使用简化版本分析

```bash
python simple_augment_fetcher.py
```

这个版本主要用于：
- 测试 API 端点
- 了解请求结构
- 获取网络分析指导

## API 流程

根据 `extension/decode.js` 的逆向分析，获取 token 的流程是：

1. **登录**: `POST /api/users/card-login`
   ```json
   {
     "card": "your_card_code",
     "agent": "main"
   }
   ```

2. **获取用户 VIP 信息**: `POST /api/users/vips`
   ```json
   {}
   ```
   返回用户的 VIP 产品列表，包含可用的产品类型

3. **获取池子列表**: `POST /api/pools/gain_list`
   ```json
   {
     "product": "product_name_from_vip_list"
   }
   ```

4. **获取账号信息**: `POST /api/pools/gain`
   ```json
   {
     "product": "product_name",
     "pool_id": "pool_id_from_step3",
     "version": 21
   }
   ```

## 数据编码

API 使用特殊的编码格式：
- Content-Type: `application/secret`
- 请求体: BSON 序列化后每个字节与 0x37 异或
- 响应体: 同样的编码格式
- 认证: `X-Auth-Token` 头部

## 注意事项

- 需要有效的卡密才能登录
- API 服务器: `https://deepl.micosoft.icu`
- 获取的 token 用于调用 `vscode-augment.directLogin` 命令
- host 用于构建完整的服务 URL: `https://{host}/`

## 故障排除

如果遇到 "Available pools: []" 的问题：

1. **检查产品类型**: 首先调用 `/api/users/vips` 获取你账号可用的产品列表
2. **使用正确的产品名**: 常见的产品名包括：
   - `augment` - Augment 插件
   - `augment-proxy` - Augment 代理版本
   - `windsurf` - Windsurf 编辑器
3. **检查 VIP 状态**: 确保你的账号有对应产品的 VIP 权限

其他问题排查：
1. 检查网络连接
2. 确认卡密有效
3. 查看控制台输出的详细错误信息
4. 使用浏览器开发者工具监控原始扩展的网络请求

## 常见产品类型

根据代码分析，系统支持的产品类型包括：
- `augment` - 标准 Augment 插件
- `augment-proxy` - Augment 代理版本
- `windsurf` - Windsurf 编辑器

脚本会自动从你的 VIP 信息中获取可用的产品类型，如果没有找到则会尝试这些默认产品。

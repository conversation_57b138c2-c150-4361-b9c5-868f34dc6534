#!/usr/bin/env python3
"""
Network analysis helper for debugging API calls
"""

import json


def decode_hex_response(hex_data: str) -> dict:
    """
    Decode a hex-encoded response from network capture
    
    Usage:
    1. Open browser dev tools
    2. Go to Network tab
    3. Trigger the API call in the extension
    4. Copy the response body as hex
    5. Paste it here
    """
    try:
        # Remove spaces and convert to bytes
        hex_data = hex_data.replace(' ', '').replace('\n', '')
        data = bytes.fromhex(hex_data)
        
        print(f"Raw data length: {len(data)}")
        print(f"Raw data (first 50 bytes): {data[:50]}")
        
        # XOR with 0x37
        decoded = bytearray()
        for byte in data:
            decoded.append(byte ^ 0x37)
        
        print(f"XOR decoded (first 50 bytes): {bytes(decoded)[:50]}")
        
        # Try BSON decode
        try:
            import bson
            result = bson.decode(bytes(decoded))
            print("BSON decode successful:")
            print(json.dumps(result, indent=2, default=str))
            return result
        except ImportError:
            print("BSON module not available, trying JSON...")
        except Exception as e:
            print(f"BSON decode failed: {e}")
        
        # Try JSON decode
        try:
            json_str = bytes(decoded).decode('utf-8')
            result = json.loads(json_str)
            print("JSON decode successful:")
            print(json.dumps(result, indent=2))
            return result
        except Exception as e:
            print(f"JSON decode failed: {e}")
            
        # Show raw decoded text
        try:
            text = bytes(decoded).decode('utf-8', errors='ignore')
            print(f"Raw decoded text: {text[:500]}...")
        except Exception as e:
            print(f"Text decode failed: {e}")
            
        return {}
        
    except Exception as e:
        print(f"Error: {e}")
        return {}


def encode_request_data(data: dict) -> str:
    """
    Encode request data to compare with network capture
    """
    try:
        # Try BSON encode
        try:
            import bson
            bson_data = bson.encode(data)
            print(f"BSON encoded length: {len(bson_data)}")
        except ImportError:
            print("BSON module not available, using JSON...")
            bson_data = json.dumps(data).encode('utf-8')
        
        # XOR with 0x37
        encoded = bytearray()
        for byte in bson_data:
            encoded.append(byte ^ 0x37)
        
        hex_result = bytes(encoded).hex()
        print(f"Encoded hex: {hex_result}")
        return hex_result
        
    except Exception as e:
        print(f"Error: {e}")
        return ""


def main():
    print("=== Network Analysis Helper ===")
    print()
    
    while True:
        print("Choose an option:")
        print("1. Decode hex response from network capture")
        print("2. Encode request data for comparison")
        print("3. Show network capture instructions")
        print("4. Exit")
        
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "1":
            print("\nPaste the hex response data (or 'q' to go back):")
            hex_data = input().strip()
            if hex_data.lower() != 'q':
                decode_hex_response(hex_data)
                
        elif choice == "2":
            print("\nEnter JSON data to encode (or 'q' to go back):")
            json_str = input().strip()
            if json_str.lower() != 'q':
                try:
                    data = json.loads(json_str)
                    encode_request_data(data)
                except Exception as e:
                    print(f"Invalid JSON: {e}")
                    
        elif choice == "3":
            print("""
=== Network Capture Instructions ===

To capture and analyze the original extension's API calls:

1. Open VS Code with the augment-proxy extension
2. Open browser dev tools (F12)
3. Go to Network tab
4. Clear existing requests
5. Trigger account switching in the extension
6. Look for requests to deepl.micosoft.icu

For each request:
- Right-click → Copy → Copy response body
- Paste the hex data into option 1 above

Key endpoints to monitor:
- POST /api/users/card-login
- POST /api/users/vips  
- POST /api/pools/gain_list
- POST /api/pools/gain

Compare the request/response data with your Python script output.
""")
            
        elif choice == "4":
            break
            
        else:
            print("Invalid choice")
        
        print("\n" + "="*50 + "\n")


if __name__ == "__main__":
    main()

// Minimal JS VS Code extension to switch Augment login
// It will try to call the command "vscode-augment.directLogin" with (token, host?)
// If the command is missing, it offers to patch the Augment extension (like the reference) and asks to reload.

const vscode = require('vscode');
const fs = require('fs').promises;
const path = require('path');



async function activate(context) {
  // 查找augment扩展路径
  async function findAugmentExtensionPath() {
    const extensionDir = path.dirname(context.extensionPath);
    try {
      const entries = await fs.readdir(extensionDir);
      const augmentDirs = entries.filter(dir => dir.startsWith('augment.vscode-augment-'));
      if (augmentDirs.length === 0) {
        throw new Error('未找到 augment 扩展');
      }
      // 选择最新版本
      augmentDirs.sort((a, b) => b.localeCompare(a));
      const augmentPath = path.join(extensionDir, augmentDirs[0], 'out/extension.js');

      // 检查文件是否存在
      try {
        await fs.access(augmentPath);
        return augmentPath;
      } catch {
        throw new Error(`Augment 扩展文件不存在: ${augmentPath}`);
      }
    } catch (error) {
      throw new Error(`查找 augment 扩展失败: ${error.message}`);
    }
  }

  // hack augment扩展，注入directLogin命令
  async function hackAugmentExtension() {
    try {
      const augmentPath = await findAugmentExtensionPath();
      let content = await fs.readFile(augmentPath, 'utf-8');

      // 检查是否已经注入过
      if (content.includes('vscode-augment.directLogin')) {
        console.log('directLogin 命令已存在，无需重复注入');
        return true;
      }

      // 查找注入点
      const uriHandlerRegex = /\((\w+)\.window\.registerUriHandler/;
      const authSessionRegex = /(\w+)\.authRedirectURI\.path/;

      const uriMatch = content.match(uriHandlerRegex);
      const authMatch = content.match(authSessionRegex);

      if (!uriMatch || !authMatch) {
        throw new Error('未找到合适的注入点');
      }

      // 注入directLogin命令
      const injectedCode = `($1.commands.registerCommand("vscode-augment.directLogin",function(){${authMatch[1]}._authSession._context.globalState.update("sessionId",crypto.randomUUID());return ${authMatch[1]}._authSession.saveSession(...arguments)}),$1.window.registerUriHandler`;

      content = content.replace(uriHandlerRegex, injectedCode);

      // 确保crypto.randomUUID可用
      if (!content.startsWith('globalThis.crypto')) {
        const cryptoPolyfill = `globalThis.crypto||(globalThis.crypto={}),globalThis.crypto.randomUUID||(crypto.randomUUID=function(){let o="";for(let t=0;t<36;t++)if(t===8||t===13||t===18||t===23)o+="-";else if(t===14)o+="4";else if(t===19){o+=((Math.random()*16|0)&3|8).toString(16)}else{o+=(Math.random()*16|0).toString(16)}return o});`;
        content = cryptoPolyfill + content;
      }

      // 写回文件
      await fs.writeFile(augmentPath, content, 'utf-8');
      console.log('成功注入 directLogin 命令到 augment 扩展');
      return true;
    } catch (error) {
      console.error('hack augment 扩展失败:', error);
      throw error;
    }
  }

  async function executeDirectLogin(token, host) {
    const command = 'vscode-augment.directLogin';
    const all = await vscode.commands.getCommands(true);
    const exists = all.includes(command);

    if (!exists) {
      // 尝试hack augment扩展
      try {
        await hackAugmentExtension();
        vscode.window.showInformationMessage('已注入 directLogin 命令，请重载窗口后重试');
        const pick = await vscode.window.showInformationMessage('现在重载窗口吗？', '立即重载', '稍后');
        if (pick === '立即重载') {
          await vscode.commands.executeCommand('workbench.action.reloadWindow');
        }
        return;
      } catch (error) {
        throw new Error(`无法注入 directLogin 命令: ${error.message}`);
      }
    }

    const t = (token || '').trim();
    if (!t) throw new Error('Token 不能为空');
    const h = (host || '').trim();
    if (!h) throw new Error('Host 不能为空');
    await vscode.commands.executeCommand(command, t, h);
  }

  function getNonce() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let s = '';
    for (let i = 0; i < 32; i++) s += chars.charAt(Math.floor(Math.random()*chars.length));
    return s;
  }

  function getHtml(nonce, initialHost, historyJson) {
    const csp = `default-src 'none'; img-src https: data:; style-src 'unsafe-inline'; script-src 'nonce-${nonce}';`;
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Security-Policy" content="${csp}">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Augment 登录切换</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif; padding: 16px; }
    .row { margin-bottom: 12px; }
    input[type=text] { width: 100%; padding: 8px; box-sizing: border-box; }
    .actions { display: flex; gap: 8px; }
    button { padding: 6px 12px; }
    .hint { color: gray; font-size: 12px; }
  </style>
</head>
<body>
  <h2>Augment 登录切换</h2>
  <div class="row">
    <label>Token</label>
    <input id="token" type="text" placeholder="输入 Augment token" />
  </div>
  <div class="row">
    <label>Host（必填）</label>
    <input id="host" type="text" placeholder="如 https://api.example.com/ 末尾带 /" value="${initialHost || ''}" />
    <div class="hint">Host 为必填项</div>
  </div>
  <div class="row">
    <label>最近记录</label>
    <select id="history"></select>
    <div class="hint">选择后会填充到上面的字段</div>
  </div>
  <div class="actions">
    <button id="login">切换登录</button>
  </div>
  <script nonce="${nonce}">
    const vscode = acquireVsCodeApi();
    const $ = id => document.getElementById(id);
    const HISTORY = ${historyJson || '[]'};
    const historySel = $('history');
    if (Array.isArray(HISTORY)) {
      historySel.innerHTML = '';
      historySel.appendChild(new Option('选择一条记录', ''));
      HISTORY.forEach(h => {
        const label = (h.host || '') + (h.token ? ' - ' + h.token.slice(0,6) + '…' : '');
        const opt = new Option(label, JSON.stringify(h));
        historySel.appendChild(opt);
      });
    }
    function setFieldsFromHistory() {
      const v = $('history').value;
      if (!v) return;
      try {
        const obj = JSON.parse(v);
        if (obj.token) $('token').value = obj.token;
        if (obj.host) $('host').value = obj.host;
      } catch {}
    }

    $('history').addEventListener('change', setFieldsFromHistory);
    $('login').addEventListener('click', () => {
      const token = $('token').value.trim();
      const host = $('host').value.trim();
      if (!token) { alert('Token 不能为空'); return; }
      if (!host) { alert('Host 不能为空'); return; }
      vscode.postMessage({ type: 'login', token, host });
    });
  </script>
</body>
</html>`;
  }

  const disposable = vscode.commands.registerCommand('augmentSwitcher.switchLogin', async () => {
    // 保留命令形式以便快捷打开独立面板
    const initialHost = (context.globalState.get('augmentSwitcher.lastHost') || vscode.workspace.getConfiguration().get('augmentSwitcher.host') || '').trim();
    const history = context.globalState.get('augmentSwitcher.history') || [];
    const panel = vscode.window.createWebviewPanel('augmentSwitcher', 'Augment 登录切换', vscode.ViewColumn.One, { enableScripts: true });
    const nonce = getNonce();
    panel.webview.html = getHtml(nonce, initialHost, JSON.stringify(history));
    panel.webview.onDidReceiveMessage(async (msg) => {
      try {
        if (msg.type === 'login') {
          await executeDirectLogin(msg.token || '', msg.host || initialHost);
          // 记住最近一次输入
          const rec = { token: (msg.token || '').trim(), host: (msg.host || initialHost || '').trim(), ts: Date.now() };
          const hist = Array.isArray(history) ? history : [];
          // 去重并保留最近 10 条
          const merged = [rec, ...hist.filter(h => !(h.host === rec.host && h.token === rec.token))].slice(0, 10);
          await context.globalState.update('augmentSwitcher.lastToken', rec.token);
          await context.globalState.update('augmentSwitcher.lastHost', rec.host);
          await context.globalState.update('augmentSwitcher.history', merged);
          vscode.window.showInformationMessage('已切换 Augment 登录，建议重载窗口以生效');
          const pick = await vscode.window.showInformationMessage('现在重载窗口以应用会话吗？', '立即重载', '稍后');
          if (pick === '立即重载') await vscode.commands.executeCommand('workbench.action.reloadWindow');
        }
      } catch (e) {
        vscode.window.showErrorMessage(String(e && e.message || e));
      }
    });
  });
  context.subscriptions.push(disposable);

  // 侧栏视图提供者
  class SwitcherViewProvider {
    constructor(context) { this.context = context; }
    resolveWebviewView(webviewView) {
      webviewView.webview.options = { enableScripts: true };
      const initialHost = (this.context.globalState.get('augmentSwitcher.lastHost') || vscode.workspace.getConfiguration().get('augmentSwitcher.host') || '').trim();
      const history = this.context.globalState.get('augmentSwitcher.history') || [];
      const nonce = getNonce();
      webviewView.webview.html = getHtml(nonce, initialHost, JSON.stringify(history));
      webviewView.webview.onDidReceiveMessage(async (msg) => {
        try {
          if (msg.type === 'login') {
            await executeDirectLogin(msg.token || '', msg.host || initialHost);
            // 记住最近一次输入
            const rec = { token: (msg.token || '').trim(), host: (msg.host || initialHost || '').trim(), ts: Date.now() };
            const history = this.context.globalState.get('augmentSwitcher.history') || [];
            const merged = [rec, ...history.filter(h => !(h.host === rec.host && h.token === rec.token))].slice(0, 10);
            await this.context.globalState.update('augmentSwitcher.lastToken', rec.token);
            await this.context.globalState.update('augmentSwitcher.lastHost', rec.host);
            await this.context.globalState.update('augmentSwitcher.history', merged);
            vscode.window.showInformationMessage('已切换 Augment 登录，建议重载窗口以生效');
            const pick = await vscode.window.showInformationMessage('现在重载窗口以应用会话吗？', '立即重载', '稍后');
            if (pick === '立即重载') await vscode.commands.executeCommand('workbench.action.reloadWindow');
          }
        } catch (e) {
          vscode.window.showErrorMessage(String(e && e.message || e));
        }
      });
    }
  }
  context.subscriptions.push(vscode.window.registerWebviewViewProvider('augmentSwitcher.view', new SwitcherViewProvider(context)));
}

function deactivate() {}

module.exports = { activate, deactivate };


# Augment Login Switcher

一个最小化 VS Code 插件：让你输入 token 并切换 Augment 插件的登录。

- 提供命令："Augment: 输入 Token 切换登录" (augmentSwitcher.switchLogin)
- 优先尝试调用 Augment 扩展的命令 `vscode-augment.directLogin`
- 若命令不存在，将提示你是否注入支持（修改 augment.vscode-augment-*/out/extension.js）并要求重载
- 可通过设置 `augmentSwitcher.host` 指定后端 host（例如 `https://api.example.com/`）

> 安全提示：请仅使用你信任的后端域名，不要输入可疑域名或来源不明的 token。

## 使用
1. 在 VS Code 中安装/加载该扩展
2. 运行命令面板：Augment: 输入 Token 切换登录
3. 按提示输入 token（和必要时的 host）
4. 确认后会提示你重载窗口

